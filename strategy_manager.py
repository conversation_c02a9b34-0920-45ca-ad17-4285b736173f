#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略管理工具

功能：
1. 列出所有可用策略
2. 查看策略详细信息
3. 切换当前使用的策略
4. 验证策略配置
5. 测试策略加载

使用方式：
python strategy_manager.py --list                    # 列出所有策略
python strategy_manager.py --info strategy_name      # 查看策略详情
python strategy_manager.py --set-active strategy_name # 设置当前策略
python strategy_manager.py --validate                # 验证所有策略配置
python strategy_manager.py --test strategy_name      # 测试策略加载
"""

import argparse
import yaml
import os
import sys
import importlib
import backtrader as bt
from datetime import datetime

def load_config(config_path='config.yaml'):
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        print(f"错误: 配置文件 {config_path} 不存在")
        return None
    except Exception as e:
        print(f"错误: 加载配置文件时出错: {str(e)}")
        return None

def save_config(config, config_path='config.yaml'):
    """保存配置文件"""
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
        return True
    except Exception as e:
        print(f"错误: 保存配置文件时出错: {str(e)}")
        return False

def list_strategies(config):
    """列出所有可用策略"""
    if 'strategy_config' not in config:
        print("配置文件中没有策略配置")
        return
    
    strategy_config = config['strategy_config']
    active_strategy = strategy_config.get('active_strategy', '')
    strategies = strategy_config.get('strategies', {})
    
    if not strategies:
        print("没有配置任何策略")
        return
    
    print("\n=== 可用策略列表 ===")
    print(f"当前活跃策略: {active_strategy}")
    print()
    
    for name, info in strategies.items():
        status = " ✓ [当前]" if name == active_strategy else ""
        print(f"• {name}{status}")
        print(f"  描述: {info.get('description', '无描述')}")
        print(f"  模块: {info['module']}")
        print(f"  类名: {info['class']}")
        if info.get('parameters'):
            print(f"  自定义参数: {len(info['parameters'])} 项")
        print()

def show_strategy_info(config, strategy_name):
    """显示策略详细信息"""
    if 'strategy_config' not in config:
        print("配置文件中没有策略配置")
        return
    
    strategies = config['strategy_config'].get('strategies', {})
    
    if strategy_name not in strategies:
        print(f"策略 '{strategy_name}' 不存在")
        available = list(strategies.keys())
        if available:
            print(f"可用策略: {', '.join(available)}")
        return
    
    info = strategies[strategy_name]
    active_strategy = config['strategy_config'].get('active_strategy', '')
    
    print(f"\n=== 策略详细信息: {strategy_name} ===")
    print(f"状态: {'当前活跃' if strategy_name == active_strategy else '非活跃'}")
    print(f"描述: {info.get('description', '无描述')}")
    print(f"模块文件: {info['module']}.py")
    print(f"策略类名: {info['class']}")
    
    if info.get('parameters'):
        print(f"\n自定义参数 ({len(info['parameters'])} 项):")
        for param, value in info['parameters'].items():
            print(f"  {param}: {value}")
    else:
        print("\n参数: 使用策略默认参数")
    
    print(f"\n完整配置:")
    print(f"  module: '{info['module']}'")
    print(f"  class: '{info['class']}'")
    if info.get('parameters'):
        print(f"  parameters:")
        for param, value in info['parameters'].items():
            print(f"    {param}: {value}")
    else:
        print(f"  parameters: {{}}")

def set_active_strategy(config, strategy_name, config_path='config.yaml'):
    """设置当前活跃策略"""
    if 'strategy_config' not in config:
        print("配置文件中没有策略配置")
        return False
    
    strategies = config['strategy_config'].get('strategies', {})
    
    if strategy_name not in strategies:
        print(f"策略 '{strategy_name}' 不存在")
        available = list(strategies.keys())
        if available:
            print(f"可用策略: {', '.join(available)}")
        return False
    
    old_strategy = config['strategy_config'].get('active_strategy', '')
    config['strategy_config']['active_strategy'] = strategy_name
    
    if save_config(config, config_path):
        print(f"成功设置活跃策略: {old_strategy} -> {strategy_name}")
        print(f"策略描述: {strategies[strategy_name].get('description', '无描述')}")
        return True
    else:
        return False

def validate_strategy(strategy_name, strategy_info):
    """验证单个策略配置"""
    errors = []
    warnings = []
    
    # 检查必需字段
    if 'module' not in strategy_info:
        errors.append("缺少 'module' 字段")
    if 'class' not in strategy_info:
        errors.append("缺少 'class' 字段")
    
    if errors:
        return False, errors, warnings
    
    module_name = strategy_info['module']
    class_name = strategy_info['class']
    
    # 检查模块文件是否存在
    module_file = f"{module_name}.py"
    if not os.path.exists(module_file):
        errors.append(f"模块文件 '{module_file}' 不存在")
    
    # 尝试导入模块和类
    try:
        module = importlib.import_module(module_name)
        
        if not hasattr(module, class_name):
            errors.append(f"模块 '{module_name}' 中不存在类 '{class_name}'")
        else:
            strategy_class = getattr(module, class_name)
            
            # 检查是否继承自bt.Strategy
            if not issubclass(strategy_class, bt.Strategy):
                errors.append(f"类 '{class_name}' 不是有效的Backtrader策略类（必须继承自bt.Strategy）")
            else:
                # 检查参数配置
                parameters = strategy_info.get('parameters', {})
                if parameters:
                    # 获取策略的参数定义
                    strategy_params = getattr(strategy_class, 'params', ())
                    valid_params = set()
                    
                    # 处理不同的参数定义格式
                    if hasattr(strategy_params, '_getpairs'):
                        # backtrader的参数对象
                        for param_item in strategy_params._getpairs():
                            if isinstance(param_item, (tuple, list)) and len(param_item) >= 1:
                                valid_params.add(param_item[0])
                            else:
                                # 如果不是元组或列表，直接添加
                                valid_params.add(str(param_item))
                    elif isinstance(strategy_params, (tuple, list)):
                        # 元组或列表格式
                        for param in strategy_params:
                            if isinstance(param, tuple) and len(param) >= 1:
                                valid_params.add(param[0])
                            elif isinstance(param, str):
                                valid_params.add(param)
                    elif isinstance(strategy_params, dict):
                        # 字典格式
                        valid_params.update(strategy_params.keys())
                    
                    # 检查配置的参数是否有效
                    for param_name in parameters.keys():
                        if valid_params and param_name not in valid_params:
                            warnings.append(f"参数 '{param_name}' 可能不是有效的策略参数")
    
    except ImportError as e:
        errors.append(f"无法导入模块 '{module_name}': {str(e)}")
    except Exception as e:
        errors.append(f"验证策略时出错: {str(e)}")
    
    return len(errors) == 0, errors, warnings

def validate_all_strategies(config):
    """验证所有策略配置"""
    if 'strategy_config' not in config:
        print("配置文件中没有策略配置")
        return
    
    strategies = config['strategy_config'].get('strategies', {})
    active_strategy = config['strategy_config'].get('active_strategy', '')
    
    if not strategies:
        print("没有配置任何策略")
        return
    
    print(f"\n=== 策略配置验证 ===")
    print(f"总策略数: {len(strategies)}")
    print(f"当前活跃策略: {active_strategy}")
    print()
    
    valid_count = 0
    invalid_count = 0
    
    for name, info in strategies.items():
        print(f"验证策略: {name}")
        is_valid, errors, warnings = validate_strategy(name, info)
        
        if is_valid:
            print(f"  ✓ 验证通过")
            valid_count += 1
            if warnings:
                for warning in warnings:
                    print(f"  ⚠ 警告: {warning}")
        else:
            print(f"  ✗ 验证失败")
            invalid_count += 1
            for error in errors:
                print(f"    - {error}")
            for warning in warnings:
                print(f"  ⚠ 警告: {warning}")
        print()
    
    print(f"验证结果: {valid_count} 个有效, {invalid_count} 个无效")
    
    # 检查活跃策略是否有效
    if active_strategy:
        if active_strategy not in strategies:
            print(f"⚠ 当前活跃策略 '{active_strategy}' 不存在于配置中")
        else:
            is_valid, _, _ = validate_strategy(active_strategy, strategies[active_strategy])
            if not is_valid:
                print(f"⚠ 当前活跃策略 '{active_strategy}' 配置无效")

def test_strategy_loading(config, strategy_name):
    """测试策略加载"""
    if 'strategy_config' not in config:
        print("配置文件中没有策略配置")
        return
    
    strategies = config['strategy_config'].get('strategies', {})
    
    if strategy_name not in strategies:
        print(f"策略 '{strategy_name}' 不存在")
        return
    
    print(f"\n=== 测试策略加载: {strategy_name} ===")
    
    strategy_info = strategies[strategy_name]
    
    # 验证配置
    is_valid, errors, warnings = validate_strategy(strategy_name, strategy_info)
    
    if not is_valid:
        print("❌ 策略配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        return
    
    if warnings:
        print("⚠ 警告:")
        for warning in warnings:
            print(f"  - {warning}")
    
    try:
        # 尝试加载策略
        module_name = strategy_info['module']
        class_name = strategy_info['class']
        parameters = strategy_info.get('parameters', {})
        
        print(f"正在导入模块: {module_name}")
        module = importlib.import_module(module_name)
        
        print(f"正在获取策略类: {class_name}")
        strategy_class = getattr(module, class_name)
        
        print(f"策略类信息:")
        print(f"  - 类名: {strategy_class.__name__}")
        print(f"  - 模块: {strategy_class.__module__}")
        print(f"  - 基类: {[base.__name__ for base in strategy_class.__bases__]}")
        
        # 显示策略参数
        if hasattr(strategy_class, 'params'):
            print(f"  - 可用参数:")
            strategy_params = strategy_class.params
            if hasattr(strategy_params, '_getpairs'):
                for param_name, default_value in strategy_params._getpairs():
                    custom_value = parameters.get(param_name, '使用默认值')
                    print(f"    {param_name}: {default_value} (配置: {custom_value})")
        
        print("✅ 策略加载测试成功!")
        
    except Exception as e:
        print(f"❌ 策略加载测试失败: {str(e)}")

def main():
    parser = argparse.ArgumentParser(description='策略管理工具')
    parser.add_argument('--config', type=str, default='config.yaml', help='配置文件路径')
    parser.add_argument('--list', action='store_true', help='列出所有可用策略')
    parser.add_argument('--info', type=str, help='显示指定策略的详细信息')
    parser.add_argument('--set-active', type=str, help='设置当前活跃策略')
    parser.add_argument('--validate', action='store_true', help='验证所有策略配置')
    parser.add_argument('--test', type=str, help='测试指定策略的加载')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    if config is None:
        return 1
    
    # 执行相应操作
    if args.list:
        list_strategies(config)
    elif args.info:
        show_strategy_info(config, args.info)
    elif args.set_active:
        if not set_active_strategy(config, args.set_active, args.config):
            return 1
    elif args.validate:
        validate_all_strategies(config)
    elif args.test:
        test_strategy_loading(config, args.test)
    else:
        # 默认显示概览
        print("策略管理工具 - 概览")
        list_strategies(config)
        print("\n使用 --help 查看所有可用选项")
    
    return 0

if __name__ == '__main__':
    sys.exit(main()) 