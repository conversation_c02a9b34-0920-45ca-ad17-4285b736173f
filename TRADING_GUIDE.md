# 股票交易进程管理系统使用指南

## 概述

本系统提供了完整的股票交易进程管理功能，支持从配置文件自动初始化、手动添加/移除股票、实时监控等功能。

## 文件说明

- `trade_manager.py` - 核心交易管理器类
- `trade_cli.py` - 命令行管理工具
- `start_trading.py` - 手动启动脚本
- `auto_trading.py` - 自动选股交易脚本（推荐）
- `select_stocks.py` - 股票选择算法
- `config.yaml` - 配置文件

## 使用方法

### 1. 自动选股交易（推荐）

#### 方法一：完全自动化（推荐）
```bash
# 自动选股并启动交易系统
python auto_trading.py

# 仅执行选股，不启动交易
python auto_trading.py --select-only

# 指定日期进行选股
python auto_trading.py --date 20231201

# 启动后不进入监控模式
python auto_trading.py --no-monitor
```

这个脚本会：
- 自动执行选股算法
- 更新配置文件中的股票列表
- 启动所有选中股票的交易进程
- 持续监控进程状态
- 支持优雅退出（Ctrl+C）

#### 方法二：使用命令行工具
```bash
# 自动选股并启动交易进程
python trade_cli.py auto-start

# 指定日期进行自动选股
python trade_cli.py auto-start --date 20231201
```

### 2. 手动初始化

#### 方法一：使用启动脚本
```bash
# 启动交易系统，自动从配置文件初始化所有股票
python start_trading.py
```

这个脚本会：
- 自动从 `config.yaml` 读取股票列表
- 启动所有配置的股票交易进程
- 持续监控进程状态
- 支持优雅退出（Ctrl+C）

#### 方法二：使用命令行工具
```bash
# 从配置文件初始化所有股票交易进程
python trade_cli.py init
```

### 3. 手动管理股票

#### 添加新股票
```bash
# 添加股票（使用配置文件中的默认资金）
python trade_cli.py add 600000

# 添加股票并指定初始资金
python trade_cli.py add 600000 --initial-cash 50000
```

#### 移除股票
```bash
# 停止并移除指定股票的交易进程
python trade_cli.py remove 600000
```

#### 查看活跃股票
```bash
# 列出当前正在交易的所有股票
python trade_cli.py list
```

#### 查看股票状态
```bash
# 查看指定股票的详细状态
python trade_cli.py status 600000
```

#### 停止所有交易
```bash
# 停止所有股票的交易进程
python trade_cli.py stop-all
```

### 4. 配置文件设置

确保 `config.yaml` 中包含以下配置：

```yaml
backtest:
  is_live: true  # 必须设置为 true 才能进行实盘交易
  stock_codes:   # 要交易的股票列表
    - "600000"
    - "000001"
    - "000002"
  initial_cash: 100000  # 初始资金
  # 其他配置...

logging:
  level: INFO              # 全局日志级别
  log_dir: logs           # 日志文件目录
  file_level: DEBUG       # 文件日志级别
  console_level: INFO     # 控制台日志级别
  file_format: '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
  console_format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
```

## 功能特性

### 1. 进程管理
- **独立进程**：每只股票都有独立的数据处理进程和交易进程
- **进程监控**：实时监控进程状态，检测异常
- **资源清理**：自动清理进程资源，防止内存泄漏
- **优雅退出**：支持信号处理，确保安全退出

### 2. 日志记录
- **分级日志**：支持不同级别的日志记录
- **文件日志**：所有操作都记录到日志文件
- **控制台输出**：重要信息同时显示在控制台
- **时间戳**：所有日志都包含详细的时间戳

### 3. 通知功能
- **企业微信通知**：重要事件自动发送企业微信通知
- **状态通知**：启动、停止、异常等状态变化通知
- **错误通知**：异常情况及时通知

### 4. 安全特性
- **重复检查**：防止重复启动同一股票的交易进程
- **异常处理**：完整的异常处理机制
- **状态验证**：操作前验证系统状态
- **资源保护**：防止资源泄漏和冲突

## 使用场景

### 场景1：完全自动化交易
```bash
# 每日自动选股并启动交易
python auto_trading.py

# 定时任务中使用（不进入监控模式）
python auto_trading.py --no-monitor
```

### 场景2：系统启动时手动初始化
```bash
# 服务器启动时运行
python start_trading.py
```

### 场景3：运行时动态管理
```bash
# 查看当前状态
python trade_cli.py list

# 添加新股票
python trade_cli.py add 600036

# 移除表现不佳的股票
python trade_cli.py remove 600000

# 查看特定股票状态
python trade_cli.py status 600036
```

### 场景4：系统维护
```bash
# 停止所有交易
python trade_cli.py stop-all

# 重新初始化
python trade_cli.py init
```

## 监控和维护

### 1. 日志监控
日志文件位于 `logs/` 目录下：
- `trade_manager_*.log` - 交易管理器日志
- `auto_trading_*.log` - 自动交易系统日志
- `trade_cli_*.log` - 命令行工具日志
- `start_trading_*.log` - 启动脚本日志
- `backtest_*_live_*.log` - 各股票交易日志
- `backtest_*_data_*.log` - 各股票数据处理日志

所有日志文件都包含：
- 详细的时间戳
- 函数名和行号信息
- 不同级别的日志记录（DEBUG、INFO、WARNING、ERROR）
- 异常堆栈信息

### 2. 进程状态检查
```bash
# 检查所有股票状态
python trade_cli.py list

# 检查特定股票详细状态
python trade_cli.py status 600000
```

### 3. 异常处理
- 系统会自动检测进程异常
- 异常情况会发送企业微信通知
- 可以通过命令行工具手动重启异常股票

## 注意事项

1. **配置检查**：启动前确保 `config.yaml` 配置正确
2. **权限确认**：确保有足够的系统权限创建进程
3. **资源监控**：监控系统资源使用情况
4. **网络连接**：确保网络连接稳定
5. **数据源**：确保数据源服务正常

## 故障排除

### 1. 初始化失败
- 检查配置文件格式
- 确认 `is_live` 设置为 `true`
- 检查股票代码格式
- 查看日志文件获取详细错误信息

### 2. 进程异常退出
- 查看对应的日志文件
- 检查网络连接
- 确认数据源服务状态
- 使用 `remove` 和 `add` 命令重启股票

### 3. 通知不工作
- 检查企业微信配置
- 确认网络连接
- 查看日志中的错误信息

## 扩展功能

系统设计为可扩展的，您可以：
- 添加更多的监控指标
- 实现自动重启功能
- 添加更多的通知渠道
- 集成更多的分析工具 