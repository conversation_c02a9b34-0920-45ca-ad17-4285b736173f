import argparse
from trade_manager import TradeManager
from simple_trade_manager import SimpleTradeManager
from stock_data_manager import format_stock_code_with_name
import sys
import subprocess
import os
import logging
import yaml
from datetime import datetime

def setup_cli_logger():
    """设置命令行工具日志"""
    try:
        # 加载配置
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        log_config = config.get('logging', {})
    except Exception:
        # 如果配置文件不存在或有问题，使用默认配置
        log_config = {
            'level': 'INFO',
            'log_dir': 'logs',
            'file_level': 'DEBUG',
            'console_level': 'INFO',
            'file_format': '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            'console_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        }
    
    logger = logging.getLogger("TradeCLI")
    logger.setLevel(getattr(logging, log_config.get('level', 'INFO')))
    
    # 清除已有的handlers，避免重复
    if logger.handlers:
        logger.handlers.clear()
    
    # 确保日志目录存在
    log_dir = log_config.get('log_dir', 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 添加文件处理器
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f'{log_dir}/trade_cli_{timestamp}.log'
    fh = logging.FileHandler(log_filename, encoding='utf-8')
    fh.setLevel(getattr(logging, log_config.get('file_level', 'DEBUG')))
    
    # 添加控制台处理器
    ch = logging.StreamHandler()
    ch.setLevel(getattr(logging, log_config.get('console_level', 'INFO')))
    
    # 创建formatter
    file_format = log_config.get('file_format', 
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s')
    console_format = log_config.get('console_format', 
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    file_formatter = logging.Formatter(file_format, datefmt='%Y-%m-%d %H:%M:%S')
    console_formatter = logging.Formatter(console_format, datefmt='%Y-%m-%d %H:%M:%S')
    
    fh.setFormatter(file_formatter)
    ch.setFormatter(console_formatter)
    
    # 添加handlers
    logger.addHandler(fh)
    logger.addHandler(ch)
    
    # 防止日志向上传播，避免重复输出
    logger.propagate = False
    
    return logger

def auto_select_and_start(target_date=None):
    """自动选股并启动交易进程"""
    logger = logging.getLogger("TradeCLI")
    
    try:
        logger.info("开始执行自动选股...")
        print("正在执行自动选股...")
        
        # 构建选股命令
        cmd = ["python", "select_stocks.py"]
        if target_date:
            cmd.extend(["--date", target_date])
            logger.info(f"使用指定日期: {target_date}")
        
        logger.debug(f"执行命令: {' '.join(cmd)}")
        
        # 执行选股脚本
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("自动选股完成")
            print("自动选股完成")
            if result.stdout:
                logger.debug(f"选股输出: {result.stdout}")
                print("选股输出:", result.stdout)
            return True
        else:
            logger.error("自动选股失败")
            print("自动选股失败")
            if result.stderr:
                logger.error(f"错误输出: {result.stderr}")
                print("错误输出:", result.stderr)
            return False
            
    except Exception as e:
        error_msg = f"执行自动选股时出错: {str(e)}"
        logger.error(error_msg, exc_info=True)
        print(error_msg)
        return False

def main():
    # 设置日志
    logger = setup_cli_logger()
    logger.info("启动交易进程管理工具")
    
    parser = argparse.ArgumentParser(description='股票交易进程管理工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # 添加股票命令
    add_parser = subparsers.add_parser('add', help='添加新的股票交易进程')
    add_parser.add_argument('stock_code', help='股票代码')
    add_parser.add_argument('--initial-cash', type=float, help='初始资金')

    # 移除股票命令
    remove_parser = subparsers.add_parser('remove', help='停止并移除股票交易进程')
    remove_parser.add_argument('stock_code', help='股票代码')

    # 重启股票命令
    restart_parser = subparsers.add_parser('restart', help='重启指定股票的交易进程')
    restart_parser.add_argument('stock_code', help='股票代码')
    restart_parser.add_argument('--initial-cash', type=float, help='初始资金（重启时使用）')

    # 列出活跃股票命令
    subparsers.add_parser('list', help='列出当前正在交易的股票')

    # 获取股票状态命令
    status_parser = subparsers.add_parser('status', help='获取指定股票的交易状态')
    status_parser.add_argument('stock_code', help='股票代码')

    # 初始化命令
    subparsers.add_parser('init', help='从配置文件初始化所有股票交易进程')

    # 停止所有命令
    subparsers.add_parser('stop-all', help='停止所有股票交易进程')

    # 自动选股并启动交易命令
    auto_parser = subparsers.add_parser('auto-start', help='自动选股并启动交易进程')
    auto_parser.add_argument('--date', type=str, help='目标日期（格式：YYYYMMDD），不提供则使用当前日期')

    args = parser.parse_args()
    
    logger.debug(f"解析的命令行参数: {args}")

    # 创建交易管理器实例（优先使用简化管理器）
    logger.info("创建交易管理器实例")
    try:
        # 使用简化交易管理器
        manager = SimpleTradeManager()
        logger.info("使用简化交易管理器")
    except Exception as e:
        # 如果失败，回退到普通交易管理器
        logger.warning(f"简化交易管理器初始化失败，使用普通交易管理器: {str(e)}")
        manager = TradeManager()

    try:
        if args.command == 'add':
            formatted_code = format_stock_code_with_name(args.stock_code)
            logger.info(f"执行添加股票命令: {formatted_code}, 初始资金: {args.initial_cash}")
            success = manager.add_stock(args.stock_code, args.initial_cash)
            if success:
                logger.info(f"成功添加股票 {formatted_code} 的交易进程")
                print(f"成功添加股票 {formatted_code} 的交易进程")
            else:
                logger.error(f"添加股票 {formatted_code} 失败")
                print(f"添加股票 {formatted_code} 失败")
                sys.exit(1)

        elif args.command == 'remove':
            formatted_code = format_stock_code_with_name(args.stock_code)
            logger.info(f"执行移除股票命令: {formatted_code}")
            success = manager.remove_stock(args.stock_code)
            if success:
                logger.info(f"成功移除股票 {formatted_code} 的交易进程")
                print(f"成功移除股票 {formatted_code} 的交易进程")
            else:
                logger.error(f"移除股票 {formatted_code} 失败")
                print(f"移除股票 {formatted_code} 失败")
                sys.exit(1)

        elif args.command == 'restart':
            formatted_code = format_stock_code_with_name(args.stock_code)
            logger.info(f"执行重启股票命令: {formatted_code}, 初始资金: {args.initial_cash}")
            success = manager.restart_stock(args.stock_code, args.initial_cash)
            if success:
                logger.info(f"成功重启股票 {formatted_code} 的交易进程")
                print(f"成功重启股票 {formatted_code} 的交易进程")
            else:
                logger.error(f"重启股票 {formatted_code} 失败")
                print(f"重启股票 {formatted_code} 失败")
                sys.exit(1)

        elif args.command == 'list':
            logger.info("执行列出活跃股票命令")
            active_stocks = manager.get_active_stocks()
            logger.debug(f"获取到活跃股票: {active_stocks}")
            if active_stocks:
                print("当前正在交易的股票：")
                for stock in active_stocks:
                    formatted_stock = format_stock_code_with_name(stock)
                    print(f"- {formatted_stock}")
            else:
                print("当前没有正在交易的股票")

        elif args.command == 'status':
            formatted_code = format_stock_code_with_name(args.stock_code)
            logger.info(f"执行查看股票状态命令: {formatted_code}")
            status = manager.get_stock_status(args.stock_code)
            logger.debug(f"股票 {formatted_code} 状态: {status}")
            if status['status'] == 'not_trading':
                print(f"股票 {formatted_code} 当前不在交易中")
            elif status['status'] == 'error':
                print(f"股票 {formatted_code} 状态查询出错: {status.get('error', '未知错误')}")
            else:
                print(f"股票 {formatted_code} 交易状态：")
                # 兼容不同管理器的状态格式
                if 'process_count' in status:
                    # 简化管理器格式
                    print(f"- 运行中的进程数: {status['process_count']}")
                    if status.get('processes'):
                        for i, proc in enumerate(status['processes'], 1):
                            print(f"- 进程{i}: PID={proc['pid']}")
                else:
                    # 通用格式
                    print(f"- 状态: {status['status']}")
                    for key, value in status.items():
                        if key != 'status':
                            print(f"- {key}: {value}")

        elif args.command == 'init':
            logger.info("执行从配置文件初始化命令")
            print("正在从配置文件初始化股票交易进程...")
            success = manager.init_from_config()
            if success:
                active_stocks = manager.get_active_stocks()
                active_formatted = [format_stock_code_with_name(stock) for stock in active_stocks]
                logger.info(f"初始化成功，启动了 {len(active_stocks)} 只股票: {', '.join(active_formatted)}")
                print(f"初始化完成！成功启动 {len(active_stocks)} 只股票的交易进程：")
                for formatted_stock in active_formatted:
                    print(f"- {formatted_stock}")
            else:
                logger.error("初始化失败")
                print("初始化失败，请检查配置文件和日志")
                sys.exit(1)

        elif args.command == 'stop-all':
            logger.info("执行停止所有交易进程命令")
            active_stocks = manager.get_active_stocks()
            if not active_stocks:
                logger.info("当前没有活跃的交易进程")
                print("当前没有正在交易的股票")
            else:
                active_formatted = [format_stock_code_with_name(stock) for stock in active_stocks]
                logger.info(f"正在停止 {len(active_stocks)} 只股票的交易进程: {', '.join(active_formatted)}")
                print(f"正在停止 {len(active_stocks)} 只股票的交易进程...")
                manager.cleanup()
                logger.info("所有交易进程已停止")
                print("所有交易进程已停止")

        elif args.command == 'auto-start':
            logger.info(f"执行自动选股并启动交易命令，日期: {args.date}")
            print("开始自动选股并启动交易进程...")
            success = auto_select_and_start(args.date)
            if success:
                logger.info("自动选股和交易启动完成")
                print("自动选股和交易启动完成！")
                # 显示当前活跃股票
                active_stocks = manager.get_active_stocks()
                if active_stocks:
                    active_formatted = [format_stock_code_with_name(stock) for stock in active_stocks]
                    logger.info(f"当前交易股票: {', '.join(active_formatted)}")
                    print(f"当前交易股票: {', '.join(active_formatted)}")
            else:
                logger.error("自动选股和交易启动失败")
                print("自动选股和交易启动失败，请查看日志")
                sys.exit(1)

        else:
            logger.warning("未识别的命令，显示帮助信息")
            parser.print_help()

    except KeyboardInterrupt:
        logger.info("收到键盘中断信号，开始清理资源")
        print("\n正在清理资源...")
        manager.cleanup()
        logger.info("资源清理完成")
        print("已清理所有资源")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序执行过程中发生未预期的错误: {str(e)}", exc_info=True)
        print(f"程序执行出错: {str(e)}")
        sys.exit(1)
    finally:
        logger.info("交易进程管理工具退出")

if __name__ == '__main__':
    main() 