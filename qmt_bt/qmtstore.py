import random
from xtquant import xtdata, x<PERSON><PERSON><PERSON>, xttype
from backtrader.metabase import MetaParams
import backtrader as bt
import pandas as pd


class MetaSingleton(MetaParams):
    '''Metaclass to make a metaclassed class a singleton'''

    def __init__(cls, name, bases, dct):
        super(MetaSingleton, cls).__init__(name, bases, dct)
        cls._singleton = None

    def __call__(cls, *args, **kwargs):
        if cls._singleton is None:
            cls._singleton = (
                super(MetaSingleton, cls).__call__(*args, **kwargs))

        return cls._singleton


class QMTStore(object, metaclass=MetaSingleton):
    
    def __init__(self, stock_code=None):
        self.stock_code = stock_code
    
    def getdata(self, *args, **kwargs): 
        '''Returns ``DataCls`` with args, kwargs'''
        # 如果kwargs中已经包含stock_code，则使用kwargs中的值
        if 'stock_code' not in kwargs:
            kwargs['stock_code'] = self.stock_code
        qmtFeed = self.__class__.DataCls(*args, **kwargs)
        return qmtFeed
    
    def getdatas(self, *args, **kwargs):
        '''Returns ``DataCls`` with *args, **kwargs (multiple entries)'''
        return [self.getdata(*args, **{**kwargs, 'dataname': stock}) for stock in kwargs.pop('code_list', 1)]
    
    def setdatas(self, cerebro, datas):
        '''Set the datas'''
        for data in datas:
            cerebro.adddata(data)

    def getbroker(self, is_live=False, *args, **kwargs):
        '''Returns broker with *args, **kwargs from registered ``BrokerCls``'''
        mini_qmt_path = 'D:\\qmt\\userdata_mini'
        account_id = '**********'
        # 如果kwargs中已经包含stock_code，则使用kwargs中的值
        if 'stock_code' not in kwargs:
            kwargs['stock_code'] = self.stock_code
        return self.__class__.BrokerCls(mini_qmt_path, account_id, is_live, *args, **kwargs)
    
