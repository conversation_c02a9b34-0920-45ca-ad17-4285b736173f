from collections import deque
from datetime import datetime
import backtrader as bt
from backtrader.feed import DataBase
import pandas as pd
import logging
import os
from datetime import datetime

from .qmtstore import QMTStore

# 配置日志
def setup_feed_logger(stock_code=None):
    # 创建logs目录（如果不存在）
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 日志文件名包含当前日期时间和股票代码
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if stock_code:
        log_filename = f'logs/qmtfeed_{stock_code}_{timestamp}.log'
        logger_name = f'QMTFeed_{stock_code}'
    else:
        log_filename = f'logs/qmtfeed_{timestamp}.log'
        logger_name = 'QMTFeed'
    
    # 配置logger
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.DEBUG)
    
    # 清除已有的handlers
    if logger.handlers:
        logger.handlers.clear()
    
    # 添加文件处理器
    fh = logging.FileHandler(log_filename, encoding='utf-8')
    fh.setLevel(logging.DEBUG)
    
    # 添加控制台处理器（仅显示关键信息）
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)
    
    # 创建formatter
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    fh.setFormatter(file_formatter)
    ch.setFormatter(console_formatter)
    
    # 添加handlers
    logger.addHandler(fh)
    logger.addHandler(ch)
    
    return logger

# 全局日志对象
LOGGER = setup_feed_logger()

class MetaQMTFeed(DataBase.__class__):
    def __init__(cls, name, bases, dct):
        '''Class has already been created ... register'''
        # Initialize the class
        super(MetaQMTFeed, cls).__init__(name, bases, dct)

        # Register with the store
        QMTStore.DataCls = cls


class QMTFeed(DataBase, metaclass=MetaQMTFeed):
    """
    QMT eXchange Trading Library Data Feed.
    """

    lines = ('amount', 'volume_cum', 'amount_cum', 'avg_price',)

    params = (
        ('timeframe', bt.TimeFrame.Seconds),
    )
    def __init__(self, stock_code, conn, *args, **kwargs):
        self.stock_code = stock_code
        self.conn = conn
        # 为每个股票创建单独的日志
        self.logger = setup_feed_logger(stock_code)
        self.logger.info(f"初始化QMTFeed: stock_code={stock_code}")

    def islive(self):
        return True

    def start(self):
        # super(QMTFeed, self).start()
        DataBase.start(self)

        # self.resample(timeframe=bt.TimeFrame.Seconds, compression=1)
        if self.conn is None:
            self.logger.error("数据管道连接失败!")
            raise Exception("数据管道连接失败!")
        self.logger.info("QMTFeed启动成功")

    def stop(self):
        # super(QMTFeed, self).stop()
        DataBase.stop(self)
        if self.conn is not None:
            self.conn.close()
            self.conn = None
            self.logger.info("QMTFeed停止，连接已关闭")

    def _load(self):
        if self.conn is None:
            self.logger.error("数据管道连接为空")
            return False
        
        try:
            self_bar = self.conn.recv()
            self.logger.debug(f"接收数据: {self_bar}")
            
            self.lines.datetime[0] = bt.utils.date2num(pd.to_datetime(datetime.fromtimestamp(self_bar['time']/1000)))
            self.lines.open[0] = self_bar['open']
            self.lines.high[0] = self_bar['high']
            self.lines.low[0] = self_bar['low']
            self.lines.close[0] = self_bar['close']
            self.lines.volume[0] = self_bar['volume']
            self.lines.amount[0] = self_bar['amount']
            self.lines.volume_cum[0] = self_bar['volume_cum']
            self.lines.amount_cum[0] = self_bar['amount_cum']
            self.lines.avg_price[0] = self_bar['avg_price']
            self.lines.openinterest[0] = -1
            return True
        except Exception as e:
            self.logger.error(f"加载数据时出错: {str(e)}")
            return False