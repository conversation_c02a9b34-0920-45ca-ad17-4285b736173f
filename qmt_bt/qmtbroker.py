import backtrader as bt
import logging
import time
import threading
from datetime import datetime, timedelta
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount
from xtquant import xtconstant
from stock_data_manager import format_stock_code_with_name
from backtrader import BrokerBase, OrderBase, Order
from backtrader.position import Position
from backtrader.utils.py3 import queue, with_metaclass
import random
from .qmtstore import QMTStore
import os, sys
from backtrader import BackBroker
import uuid


class MyXtQuantTraderCallback(XtQuantTraderCallback):
    def __init__(self, logger, broker_instance=None):
        self.logger = logger
        self.broker_instance = broker_instance  # 引用broker实例
        self.order_mapping = {}  # 映射QMT订单ID到backtrader订单对象

    def on_disconnected(self):
        """
        连接断开
        :return:
        """
        self.logger.info('连接断开回调')

    def on_stock_order(self, order):
        """
        委托回报推送
        :param order: XtOrder对象
        :return:
        """
        broker_id = getattr(self.broker_instance, 'broker_id', 'UNKNOWN') if self.broker_instance else 'NO_BROKER'
        self.logger.info(f"[{broker_id}] 委托回调: stock_code={format_stock_code_with_name(order.stock_code)}, order_status={order.order_status}, order_sysid={order.order_sysid}，成交数量： {order.traded_volume}，成交均价： {order.traded_price}")
        
        # 添加详细的调试日志
        self.logger.debug(f"[{broker_id}] === 委托回调开始处理 ===")
        self.logger.debug(f"[{broker_id}] 委托回调详细信息: order_remark='{getattr(order, 'order_remark', 'NOT_FOUND')}', order_id={getattr(order, 'order_id', 'NOT_FOUND')}")
        self.logger.debug(f"[{broker_id}] order对象的所有属性: {[attr for attr in dir(order) if not attr.startswith('_')]}")
        self.logger.debug(f"[{broker_id}] broker_instance存在: {self.broker_instance is not None}")
        if self.broker_instance:
            self.logger.debug(f"[{broker_id}] broker_instance.stock_code: '{self.broker_instance.stock_code}'")
        
        # 检查order_remark是否存在
        if not hasattr(order, 'order_remark'):
            self.logger.warning(f"[{broker_id}] 委托回调中order对象没有order_remark属性")
            return
        
        order_remark = getattr(order, 'order_remark', None)
        self.logger.debug(f"[{broker_id}] 获取到的order_remark: '{order_remark}'")
        
        if not order_remark:
            self.logger.warning(f"[{broker_id}] 委托回调中order_remark为空")
            return
        
        # 检查是否是当前股票的订单
        self.logger.debug(f"[{broker_id}] 开始检查股票代码匹配...")
        self.logger.debug(f"[{broker_id}] 订单股票代码: '{order.stock_code}'")
        if self.broker_instance and self.broker_instance.stock_code:
            self.logger.debug(f"[{broker_id}] broker股票代码: '{self.broker_instance.stock_code}'")
            if order.stock_code != self.broker_instance.stock_code:
                self.logger.debug(f"[{broker_id}] 跳过其他股票的委托回调: {format_stock_code_with_name(order.stock_code)} != {format_stock_code_with_name(self.broker_instance.stock_code)}")
                return
            else:
                self.logger.debug(f"[{broker_id}] 股票代码匹配，继续处理")
        else:
            self.logger.debug(f"[{broker_id}] broker_instance或stock_code为空，跳过股票代码检查")
        
        # 统一处理订单状态和执行信息更新
        if self.broker_instance:
            self.logger.debug(f"[{broker_id}] 调用broker_instance.process_order_callback")
            self.broker_instance.process_order_callback(order)
        else:
            self.logger.warning(f"[{broker_id}] broker_instance为None，无法处理委托回调")
        
        self.logger.debug(f"[{broker_id}] === 委托回调处理结束 ===")

    def on_stock_trade(self, trade):
        """
        成交变动推送 - 关键的价格更新回调
        :param trade: XtTrade对象
        :return:
        """
        self.logger.info('成交回调', trade.order_remark, 
                        f"委托方向(48买 49卖) {trade.offset_flag} 成交价格 {trade.traded_price} 成交数量 {trade.traded_volume}")
        self.logger.info(f"成交回调: account_id={trade.account_id}, stock_code={format_stock_code_with_name(trade.stock_code)}, order_id={trade.order_id}")
        
        # 检查是否是当前股票的订单
        if self.broker_instance and self.broker_instance.stock_code and trade.stock_code != self.broker_instance.stock_code:
            self.logger.debug(f"跳过其他股票的成交回调: {format_stock_code_with_name(trade.stock_code)} != {format_stock_code_with_name(self.broker_instance.stock_code)}")
            return
        
        # 更新backtrader订单的执行价格
        if self.broker_instance:
            self.broker_instance.update_executed_price(trade)

    def on_order_error(self, order_error):
        """
        委托失败推送
        :param order_error:XtOrderError 对象
        :return:
        """
        self.logger.info(f"委托报错回调 订单备注: {order_error.order_remark}, 错误信息: {order_error.error_msg}")
        
        # 检查是否是当前股票的订单（通过订单备注中的股票代码判断）
        if self.broker_instance and self.broker_instance.stock_code:
            remark_stock_code = self.broker_instance._extract_stock_code_from_remark(order_error.order_remark)
            if remark_stock_code and remark_stock_code != self.broker_instance.stock_code:
                self.logger.debug(f"跳过其他股票的委托错误回调: {remark_stock_code} != {self.broker_instance.stock_code}")
                return
            elif not remark_stock_code:
                # 如果没有股票代码，尝试通过pending_orders判断（兼容旧格式）
                bt_order_id = self.broker_instance._extract_bt_order_id(order_error.order_remark)
                if not (bt_order_id and bt_order_id in self.broker_instance.pending_orders):
                    self.logger.debug(f"跳过其他股票的委托错误回调: 订单备注={order_error.order_remark}")
                    return
        
        # 处理订单错误
        if self.broker_instance:
            self.broker_instance.handle_order_error(order_error)

    def on_cancel_error(self, cancel_error):
        """
        撤单失败推送
        :param cancel_error: XtCancelError 对象
        :return:
        """
        self.logger.info(sys._getframe().f_code.co_name)

    def on_order_stock_async_response(self, response):
        """
        异步下单回报推送
        :param response: XtOrderResponse 对象
        :return:
        """
        self.logger.info(f"异步委托回调 订单备注: {response.order_remark}")
        
        # 检查是否是当前股票的订单（通过订单备注中的股票代码判断）
        if self.broker_instance and self.broker_instance.stock_code:
            remark_stock_code = self.broker_instance._extract_stock_code_from_remark(response.order_remark)
            if remark_stock_code and remark_stock_code != self.broker_instance.stock_code:
                self.logger.debug(f"跳过其他股票的异步委托回调: {remark_stock_code} != {self.broker_instance.stock_code}")
                return
            elif not remark_stock_code:
                # 如果没有股票代码，尝试通过pending_orders判断（兼容旧格式）
                bt_order_id = self.broker_instance._extract_bt_order_id(response.order_remark)
                if not (bt_order_id and bt_order_id in self.broker_instance.pending_orders):
                    self.logger.debug(f"跳过其他股票的异步委托回调: 订单备注={response.order_remark}")
                    return
        
        # 建立订单映射关系
        if self.broker_instance:
            self.broker_instance.map_order_response(response)

    def on_cancel_order_stock_async_response(self, response):
        """
        :param response: XtCancelOrderResponse 对象
        :return:
        """
        self.logger.info(sys._getframe().f_code.co_name)

    def on_account_status(self, status):
        """
        :param response: XtAccountStatus 对象
        :return:
        """
        self.logger.info(sys._getframe().f_code.co_name)


# 配置日志
def setup_broker_logger(stock_code=None):
    # 创建logs目录（如果不存在）
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 日志文件名包含当前日期时间和股票代码
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if stock_code:
        log_filename = f'logs/qmtbroker_{stock_code}_{timestamp}.log'
        logger_name = f'QMTBroker_{stock_code}'
    else:
        log_filename = f'logs/qmtbroker_{timestamp}.log'
        logger_name = 'QMTBroker'
    
    # 配置logger
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.DEBUG)
    
    # 清除已有的handlers
    if logger.handlers:
        logger.handlers.clear()
    
    # 添加文件处理器
    fh = logging.FileHandler(log_filename, encoding='utf-8')
    fh.setLevel(logging.DEBUG)
    
    # 添加控制台处理器
    ch = logging.StreamHandler()
    ch.setLevel(logging.DEBUG)
    
    # 创建formatter
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    fh.setFormatter(file_formatter)
    ch.setFormatter(console_formatter)
    
    # 添加handlers
    logger.addHandler(fh)
    logger.addHandler(ch)
    
    return logger

class MetaQMTBroker(BrokerBase.__class__):
    def __init__(cls, name, bases, dct):
        '''Class has already been created ... register'''
        # Initialize the class
        super(MetaQMTBroker, cls).__init__(name, bases, dct)
        QMTStore.BrokerCls = cls

class QMTBroker(BackBroker, metaclass=MetaQMTBroker):

    def __init__(self, mini_qmt_path, account_id, is_live, **kwargs):
        super(QMTBroker, self).__init__()
        self.mini_qmt_path = mini_qmt_path
        self.account_id = account_id
        self.is_live = is_live
        self.stock_code = kwargs.get('stock_code')
        
        # 添加broker实例唯一标识符
        self.broker_id = str(uuid.uuid4())[:8]  # 使用UUID的前8位作为标识符
        
        # 为每个股票创建单独的日志
        self.logger = setup_broker_logger(self.stock_code)
        
        # 订单映射和管理
        self.order_mapping = {}  # QMT订单ID -> backtrader订单对象
        self.bt_order_mapping = {}  # backtrader订单ID -> QMT订单ID
        self.pending_orders = {}  # 待处理的订单
        self.order_submit_time = {}  # 订单提交时间记录
        self.orders = []  # 订单列表（实盘模式需要）
        
        # 记录队列初始化
        self._log_queue_operation("INIT", "所有队列初始化完成")
        
        self.logger.info(f"""
        ====== QMTBroker初始化 ======
        Broker ID: {self.broker_id}
        路径: {mini_qmt_path}
        账户: {account_id}
        实盘模式: {is_live}
        股票代码: {self.stock_code}
        """)

        if self.is_live:
            session_id = int(random.randint(100000, 999999))
            self.logger.debug(f"[{self.broker_id}] 生成会话ID: {session_id}")

            xt_trader = XtQuantTrader(self.mini_qmt_path, session_id)
            # 创建交易回调类对象，并声明接收回调
            callback = MyXtQuantTraderCallback(self.logger, self)
            xt_trader.register_callback(callback)
            # 启动交易对象
            xt_trader.start()
            self.logger.debug(f"[{self.broker_id}] 交易对象已启动")
            
            # 连接客户端
            connect_result = xt_trader.connect()
            if connect_result == 0:
                self.logger.info(f"[{self.broker_id}] 连接成功")
            else:
                self.logger.error(f"[{self.broker_id}] 连接失败，错误码: {connect_result}")

            account = StockAccount(self.account_id)
            # 订阅账号
            res = xt_trader.subscribe(account)
            if res == 0:
                self.logger.info(f"[{self.broker_id}] 账户 {self.account_id} 订阅成功")
            else:
                self.logger.error(f"[{self.broker_id}] 账户订阅失败，错误码: {res}")

            self.xt_trader = xt_trader
            self.account = account
            
            # 程序启动时同步QMT订单信息到本地
            self.sync_qmt_orders_to_local()

    def _log_queue_operation(self, operation, description="", bt_order_id=None, qmt_order_id=None, additional_info=None):
        """
        记录队列操作的详细日志
        :param operation: 操作类型 (ADD, REMOVE, UPDATE, QUERY, INIT等)
        :param description: 操作描述
        :param bt_order_id: backtrader订单ID
        :param qmt_order_id: QMT订单ID
        :param additional_info: 额外信息字典
        """
        try:
            queue_status = {
                'pending_orders_count': len(self.pending_orders),
                'pending_orders_keys': list(self.pending_orders.keys()),
                'order_submit_time_count': len(self.order_submit_time),
                'order_submit_time_keys': list(self.order_submit_time.keys()),
                'bt_order_mapping_count': len(self.bt_order_mapping),
                'bt_order_mapping': dict(self.bt_order_mapping)
            }
            
            log_msg = f"""
            [{self.broker_id}] ===== 队列操作日志 =====
            操作类型: {operation}
            操作描述: {description}
            BT订单ID: {bt_order_id}
            QMT订单ID: {qmt_order_id}
            额外信息: {additional_info}
            ===== 队列当前状态 =====
            pending_orders: 数量={queue_status['pending_orders_count']}, 键={queue_status['pending_orders_keys']}
            order_submit_time: 数量={queue_status['order_submit_time_count']}, 键={queue_status['order_submit_time_keys']}
            bt_order_mapping: 数量={queue_status['bt_order_mapping_count']}, 映射={queue_status['bt_order_mapping']}
            ===============================
            """
            self.logger.debug(log_msg)
            
        except Exception as e:
            self.logger.error(f"[{self.broker_id}] 记录队列操作日志失败: {str(e)}")

    def map_order_response(self, response):
        """
        建立QMT订单响应与backtrader订单的映射关系
        """
        try:
            # 记录操作前状态
            self._log_queue_operation("QUERY", "map_order_response开始", additional_info={'response_order_remark': response.order_remark, 'response_order_id': response.order_id})
            
            # 从order_remark中提取backtrader订单ID
            bt_order_id = self._extract_bt_order_id(response.order_remark)
            if bt_order_id and response.order_id:
                # 添加映射
                self.bt_order_mapping[bt_order_id] = response.order_id
                
                # 记录添加操作
                self._log_queue_operation("ADD", "建立订单映射", bt_order_id=bt_order_id, qmt_order_id=response.order_id)
                
                self.logger.debug(f"建立订单映射: BT订单ID {bt_order_id} -> QMT订单ID {response.order_id}")
            else:
                self.logger.warning(f"无法建立订单映射: bt_order_id={bt_order_id}, qmt_order_id={response.order_id}")
                
        except Exception as e:
            self.logger.error(f"建立订单映射失败: {str(e)}")


    def update_executed_price(self, trade):
        """
        更新订单执行价格 - 成交回报方法（保持不变）
        """
        try:
            bt_order_id = self._extract_bt_order_id(trade.order_remark)
            
            # 记录查询操作
            self._log_queue_operation("QUERY", "update_executed_price查询订单", bt_order_id=bt_order_id)
            
            if bt_order_id in self.pending_orders:
                bt_order = self.pending_orders[bt_order_id]
                
                # 更新执行信息
                if not hasattr(bt_order, 'executed') or bt_order.executed is None:
                    # 创建执行信息对象
                    from backtrader.order import OrderExecuted
                    bt_order.executed = OrderExecuted()
                
                # 更新执行价格和数量
                bt_order.executed.price = trade.traded_price
                bt_order.executed.size = trade.traded_volume
                bt_order.executed.value = trade.traded_price * trade.traded_volume
                
                # 计算手续费（如果有的话）
                if hasattr(trade, 'commission'):
                    bt_order.executed.comm = trade.commission
                else:
                    # 使用broker的手续费计算
                    bt_order.executed.comm = self._calculate_commission(
                        trade.traded_volume, trade.traded_price, trade.offset_flag
                    )
                
                # 更新订单状态为已完成
                bt_order.status = bt_order.Completed
                
                # 记录更新操作
                self._log_queue_operation("UPDATE", "更新订单执行信息", bt_order_id=bt_order_id, 
                                        additional_info={'executed_price': trade.traded_price, 'executed_size': trade.traded_volume})
                
                self.logger.info(f"""
                ====== 成交回报价格更新 ======
                BT订单ID: {bt_order_id}
                执行价格: {bt_order.executed.price:.4f}
                执行数量: {bt_order.executed.size}
                执行金额: {bt_order.executed.value:.2f}
                手续费: {bt_order.executed.comm:.2f}
                """)
                
                # 通知策略订单已执行
                self._notify_order_executed(bt_order)
                
        except Exception as e:
            self.logger.error(f"成交回报更新执行价格失败: {str(e)}")

    def handle_order_error(self, order_error):
        """
        处理订单错误
        """
        try:
            bt_order_id = self._extract_bt_order_id(order_error.order_remark)
            
            # 记录查询操作
            self._log_queue_operation("QUERY", "handle_order_error查询订单", bt_order_id=bt_order_id)
            
            if bt_order_id in self.pending_orders:
                bt_order = self.pending_orders[bt_order_id]
                bt_order.status = bt_order.Rejected
                
                # 记录错误处理
                self._log_queue_operation("UPDATE", "订单错误处理", bt_order_id=bt_order_id, 
                                        additional_info={'error_msg': order_error.error_msg})
                
                self.logger.error(f"订单执行失败: BT订单ID {bt_order_id}, 错误: {order_error.error_msg}")
                
                # 通知策略订单被拒绝
                self._notify_order_executed(bt_order)
                
        except Exception as e:
            self.logger.error(f"处理订单错误失败: {str(e)}")

    def _extract_bt_order_id(self, order_remark):
        """
        从订单备注中提取backtrader订单ID
        """
        try:
            self.logger.debug(f"[{self.broker_id}] 提取BT订单ID: order_remark='{order_remark}'")
            
            # order_remark格式为 "BT_订单ID_股票代码" 或旧格式 "BT_订单ID"
            if order_remark and order_remark.startswith('BT_'):
                parts = order_remark.split('_')
                self.logger.debug(f"[{self.broker_id}] 订单备注分割结果: {parts}")
                if len(parts) >= 2:
                    bt_order_id_str = parts[1]
                    # 转换为整数类型，确保与pending_orders字典的键类型一致
                    try:
                        bt_order_id = int(bt_order_id_str)
                        self.logger.debug(f"[{self.broker_id}] 提取到BT订单ID: {bt_order_id} (类型: {type(bt_order_id)})")
                        return bt_order_id
                    except ValueError:
                        self.logger.error(f"[{self.broker_id}] BT订单ID不是有效的整数: '{bt_order_id_str}'")
                        return None
                else:
                    self.logger.warning(f"[{self.broker_id}] 订单备注格式不正确，分割后长度不足: {parts}")
            else:
                self.logger.warning(f"[{self.broker_id}] 订单备注不以'BT_'开头或为空: '{order_remark}'")
            return None
        except Exception as e:
            self.logger.error(f"[{self.broker_id}] 提取BT订单ID失败: {str(e)}, order_remark: {order_remark}")
            return None

    def _extract_stock_code_from_remark(self, order_remark):
        """
        从订单备注中提取股票代码
        """
        try:
            # order_remark格式为 "BT_订单ID_股票代码"
            if order_remark and order_remark.startswith('BT_'):
                parts = order_remark.split('_')
                if len(parts) >= 3:
                    return parts[2]  # 返回股票代码部分
            return None
        except Exception as e:
            self.logger.error(f"提取股票代码失败: {str(e)}, order_remark: {order_remark}")
            return None

    def _calculate_commission(self, size, price, offset_flag):
        """
        计算手续费
        """
        try:
            # 这里使用简化的手续费计算，实际应该根据券商规则
            commission_rate = 5.0e-05  # 万三
            return 1 if abs(size * price * commission_rate) < 1 else abs(size * price * commission_rate)
        except:
            return 0.0

    def _notify_order_executed(self, order):
        """
        通知策略订单已执行
        """
        try:
            # 记录通知前的状态
            self._log_queue_operation("REMOVE", "_notify_order_executed准备清理", bt_order_id=getattr(order, 'ref', None))
            
            # 将订单添加到通知队列
            self.notifs.append(order)
            
            # 从待处理订单中移除
            if hasattr(order, 'ref') and order.ref in self.pending_orders:
                del self.pending_orders[order.ref]
                self._log_queue_operation("REMOVE", "从pending_orders移除", bt_order_id=order.ref)
            
            # 清理订单提交时间记录
            if hasattr(order, 'ref') and order.ref in self.order_submit_time:
                del self.order_submit_time[order.ref]
                self._log_queue_operation("REMOVE", "从order_submit_time移除", bt_order_id=order.ref)
                self.logger.debug(f"清理已完成订单的提交时间记录: BT订单ID={order.ref}")
                
        except Exception as e:
            self.logger.error(f"通知订单执行失败: {str(e)}")

    def _create_order(self, owner, data, isbuy, **kwargs):
        """
        手动创建订单对象（实盘模式专用）
        """
        from backtrader.order import BuyOrder, SellOrder
        
        # 过滤掉Order构造函数不支持的参数
        # 这些参数是backtrader内部使用的，但Order构造函数不接受
        filtered_kwargs = {}
        unsupported_params = {'plimit', 'trailamount', 'trailpercent', 'parent', 'transmit', 'oco'}
        
        for key, value in kwargs.items():
            if key not in unsupported_params:
                filtered_kwargs[key] = value
        
        # 创建订单对象
        if isbuy:
            order = BuyOrder(owner=owner, data=data, **filtered_kwargs)
        else:
            order = SellOrder(owner=owner, data=data, **filtered_kwargs)
        
        # 手动设置被过滤掉的重要属性
        if 'plimit' in kwargs:
            order.plimit = kwargs['plimit']
        if 'trailamount' in kwargs:
            order.trailamount = kwargs['trailamount']
        if 'trailpercent' in kwargs:
            order.trailpercent = kwargs['trailpercent']
        
        # 设置订单状态为Created
        order.status = order.Created
        
        # 分配订单ID
        order.ref = self._getorderid()
        
        # 添加到订单列表
        self.orders.append(order)
        
        # 移除立即通知策略的代码，避免重复创建订单
        # 订单状态变化将通过QMT回调来通知策略
        # self.notifs.append(order)  # 注释掉这行
        
        return order
    
    def _getorderid(self):
        """获取下一个订单ID"""
        if not hasattr(self, '_orderid'):
            self._orderid = 0
        self._orderid += 1
        return self._orderid

    def _generate_order_remark(self, bt_order, stock_code=None):
        """
        生成包含backtrader订单ID和股票代码的备注
        格式: BT_{订单ID}_{股票代码}
        """
        if stock_code is None:
            stock_code = self.stock_code or "UNKNOWN"
        return f"BT_{bt_order.ref}_{stock_code}"

    def cancel_pending_orders(self, stock_code=None, order_type=None):
        """
        撤销待处理订单
        :param stock_code: 股票代码，None表示所有股票
        :param order_type: 订单类型，None表示所有类型，23=买入，24=卖出
        :return: 撤销结果列表
        """
        if not self.is_live:
            self.logger.debug("回测模式，跳过撤单操作")
            return []
        
        try:
            # 查询可撤订单
            orders = self.xt_trader.query_stock_orders(self.account, cancelable_only=True)
            if not orders:
                self.logger.info("没有可撤订单")
                return []
            
            cancel_results = []
            for order in orders:
                # 过滤条件
                if stock_code and order.stock_code != stock_code:
                    continue
                if order_type and order.order_type != order_type:
                    continue
                
                self.logger.info(f"撤销订单: 股票={format_stock_code_with_name(order.stock_code)}, 类型={'买入' if order.order_type == 23 else '卖出'}, "
                               f"订单ID={order.order_id}, 数量={order.order_volume}")
                
                # 执行撤单
                cancel_result = self.xt_trader.cancel_order_stock(self.account, order.order_id)
                result_info = {
                    'order_id': order.order_id,
                    'stock_code': order.stock_code,
                    'order_type': order.order_type,
                    'order_volume': order.order_volume,
                    'cancel_result': cancel_result,
                    'success': cancel_result == 0
                }
                
                if cancel_result == 0:
                    self.logger.info(f"撤单成功: {format_stock_code_with_name(order.stock_code)} 订单ID={order.order_id}")
                elif cancel_result == -1:
                    self.logger.warning(f"撤单失败: {format_stock_code_with_name(order.stock_code)} 委托已完成")
                elif cancel_result == -2:
                    self.logger.warning(f"撤单失败: {format_stock_code_with_name(order.stock_code)} 未找到对应委托编号")
                elif cancel_result == -3:
                    self.logger.error(f"撤单失败: {format_stock_code_with_name(order.stock_code)} 账号未登录")
                else:
                    self.logger.error(f"撤单失败: {format_stock_code_with_name(order.stock_code)} 未知错误={cancel_result}")
                
                cancel_results.append(result_info)
            
            return cancel_results
            
        except Exception as e:
            self.logger.error(f"撤单操作异常: {str(e)}")
            return []

    def sync_qmt_orders_to_local(self):
        """
        同步QMT订单信息到本地记录
        用于程序中途启动时恢复订单提交时间等信息
        """
        if not self.is_live:
            return
        
        try:
            from datetime import datetime
            
            # 记录同步开始
            self._log_queue_operation("QUERY", "sync_qmt_orders_to_local开始同步")
            
            # 查询所有可撤订单
            qmt_orders = self.xt_trader.query_stock_orders(self.account, cancelable_only=True)
            if not qmt_orders:
                self.logger.debug("没有可撤订单需要同步")
                self._log_queue_operation("QUERY", "没有可撤订单需要同步")
                return
            
            sync_count = 0
            for qmt_order in qmt_orders:
                try:
                    # 只处理当前股票的订单
                    if self.stock_code and qmt_order.stock_code != self.stock_code:
                        self.logger.debug(f"跳过其他股票的订单同步: {format_stock_code_with_name(qmt_order.stock_code)} != {format_stock_code_with_name(self.stock_code)}")
                        continue
                    
                    # 从订单备注中提取BT订单ID
                    bt_order_id = self._extract_bt_order_id(qmt_order.order_remark)
                    if not bt_order_id:
                        self.logger.debug(f"订单备注中未找到BT订单ID: {qmt_order.order_remark}")
                        continue
                    
                    # 如果本地没有这个订单的提交时间记录，则从QMT订单时间恢复
                    if bt_order_id not in self.order_submit_time:
                        # order_time是时间戳（秒），转换为datetime对象
                        if hasattr(qmt_order, 'order_time') and qmt_order.order_time:
                            submit_time = datetime.fromtimestamp(qmt_order.order_time)
                            self.order_submit_time[bt_order_id] = submit_time
                            sync_count += 1
                            
                            # 记录同步添加操作
                            self._log_queue_operation("ADD", "同步添加订单提交时间", bt_order_id=bt_order_id, 
                                                    qmt_order_id=qmt_order.order_id, 
                                                    additional_info={'submit_time': submit_time})
                            
                            self.logger.debug(f"同步订单时间: BT订单ID={bt_order_id}, "
                                           f"QMT订单ID={qmt_order.order_id}, "
                                           f"股票={qmt_order.stock_code}, "
                                           f"提交时间={submit_time}")
                    
                    # 建立订单映射关系（如果不存在）
                    if bt_order_id not in self.bt_order_mapping:
                        self.bt_order_mapping[bt_order_id] = qmt_order.order_id
                        
                        # 记录映射添加操作
                        self._log_queue_operation("ADD", "同步添加订单映射", bt_order_id=bt_order_id, qmt_order_id=qmt_order.order_id)
                        
                        self.logger.debug(f"建立订单映射: BT订单ID={bt_order_id} -> QMT订单ID={qmt_order.order_id}")
                        
                except Exception as e:
                    self.logger.error(f"同步单个订单失败: {str(e)}, 订单ID={qmt_order.order_id}")
                    continue
            
            # 记录同步完成状态
            self._log_queue_operation("UPDATE", f"同步完成，同步了{sync_count}个订单", additional_info={'sync_count': sync_count})
            
            if sync_count > 0:
                self.logger.info(f"成功同步{sync_count}个当前股票({format_stock_code_with_name(self.stock_code)})的订单提交时间")
            else:
                self.logger.debug(f"没有需要同步的当前股票({format_stock_code_with_name(self.stock_code)})订单时间")
                
        except Exception as e:
            self.logger.error(f"同步QMT订单信息异常: {str(e)}")

    def check_and_cancel_timeout_orders(self, timeout_seconds=60):
        """
        检查并撤销超时订单
        :param timeout_seconds: 超时时间（秒）
        :return: 撤销的订单信息
        """
        if not self.is_live:
            return []
        
        try:
            from datetime import datetime, timedelta
            current_time = datetime.now()
            
            # 记录检查开始
            self._log_queue_operation("QUERY", "check_and_cancel_timeout_orders开始检查", 
                                    additional_info={'current_time': current_time, 'timeout_seconds': timeout_seconds})
            
            self.logger.debug(f"开始检查超时订单，当前时间: {current_time}, 超时阈值: {timeout_seconds}秒")
            self.logger.debug(f"当前待处理订单数量: {len(self.order_submit_time)}，待处理订单: {self.order_submit_time}")
            self.logger.debug(f"当前订单映射数量: {len(self.bt_order_mapping)}，订单映射: {self.bt_order_mapping}")
            
            # 首先同步QMT订单信息到本地（处理程序中途启动的情况）
            self.sync_qmt_orders_to_local()
            
            timeout_orders = []
            
            # 检查待处理订单的提交时间
            for bt_order_id, submit_time in list(self.order_submit_time.items()):
                timeout_duration = (current_time - submit_time).total_seconds()
                self.logger.debug(f"检查订单 BT_ID={bt_order_id}, 提交时间={submit_time}, 已等待={timeout_duration:.1f}秒")
                
                if timeout_duration > timeout_seconds:
                    # 检查是否有对应的QMT订单ID映射
                    qmt_order_id = self.bt_order_mapping.get(bt_order_id)
                    self.logger.debug(f"订单 BT_ID={bt_order_id} 超时，QMT映射ID={qmt_order_id}")
                    
                    if qmt_order_id:
                        timeout_orders.append({
                            'bt_order_id': bt_order_id,
                            'qmt_order_id': qmt_order_id,
                            'submit_time': submit_time,
                            'timeout_duration': timeout_duration
                        })
                    else:
                        # 即使没有QMT映射，也要记录超时订单，可能是异步回调延迟
                        self.logger.warning(f"订单 BT_ID={bt_order_id} 超时但没有QMT映射，可能是异步回调延迟")
                        # 清理本地记录
                        if bt_order_id in self.order_submit_time:
                            del self.order_submit_time[bt_order_id]
                            self._log_queue_operation("REMOVE", "清理超时无映射订单提交时间", bt_order_id=bt_order_id)
                        if bt_order_id in self.pending_orders:
                            del self.pending_orders[bt_order_id]
                            self._log_queue_operation("REMOVE", "清理超时无映射pending订单", bt_order_id=bt_order_id)
            
            if not timeout_orders:
                self.logger.debug("没有发现超时订单")
                self._log_queue_operation("QUERY", "没有发现超时订单")
                return []
            
            self.logger.info(f"发现{len(timeout_orders)}个超时订单")
            self._log_queue_operation("QUERY", f"发现{len(timeout_orders)}个超时订单", 
                                    additional_info={'timeout_orders_count': len(timeout_orders)})
            
            # 查询QMT可撤订单
            qmt_orders = self.xt_trader.query_stock_orders(self.account, cancelable_only=True)
            self.logger.debug(f"QMT中可撤订单数量: {len(qmt_orders) if qmt_orders else 0}")
            
            # 过滤出当前股票的订单
            if qmt_orders and self.stock_code:
                current_stock_orders = [order for order in qmt_orders if order.stock_code == self.stock_code]
                self.logger.debug(f"QMT中当前股票({format_stock_code_with_name(self.stock_code)})的可撤订单数量: {len(current_stock_orders)}")
                qmt_orders = current_stock_orders
            
            if not qmt_orders:
                self.logger.warning(f"QMT中没有当前股票({format_stock_code_with_name(self.stock_code)})的可撤订单")
                # 清理本地记录中的超时订单
                for timeout_order in timeout_orders:
                    bt_order_id = timeout_order['bt_order_id']
                    self.logger.debug(f"清理超时订单记录: BT_ID={bt_order_id}")
                    if bt_order_id in self.order_submit_time:
                        del self.order_submit_time[bt_order_id]
                        self._log_queue_operation("REMOVE", "清理无QMT订单的提交时间", bt_order_id=bt_order_id)
                    if bt_order_id in self.bt_order_mapping:
                        del self.bt_order_mapping[bt_order_id]
                        self._log_queue_operation("REMOVE", "清理无QMT订单的映射", bt_order_id=bt_order_id)
                    if bt_order_id in self.pending_orders:
                        del self.pending_orders[bt_order_id]
                        self._log_queue_operation("REMOVE", "清理无QMT订单的pending", bt_order_id=bt_order_id)
                return []
            
            cancelled_orders = []
            for timeout_order in timeout_orders:
                bt_order_id = timeout_order['bt_order_id']
                qmt_order_id = timeout_order['qmt_order_id']
                
                # 在QMT订单中查找对应的订单
                target_qmt_order = None
                for qmt_order in qmt_orders:
                    if qmt_order.order_id == qmt_order_id:
                        target_qmt_order = qmt_order
                        break
                
                # 如果通过订单ID没找到，尝试通过订单备注匹配
                if not target_qmt_order:
                    self.logger.debug(f"通过订单ID未找到，尝试通过备注匹配 BT_ID={bt_order_id}")
                    for qmt_order in qmt_orders:
                        extracted_bt_id = self._extract_bt_order_id(qmt_order.order_remark)
                        if extracted_bt_id == bt_order_id:
                            target_qmt_order = qmt_order
                            self.logger.debug(f"通过备注找到匹配订单: QMT_ID={qmt_order.order_id}")
                            # 更新映射关系
                            self.bt_order_mapping[bt_order_id] = qmt_order.order_id
                            self._log_queue_operation("UPDATE", "通过备注匹配更新映射", bt_order_id=bt_order_id, qmt_order_id=qmt_order.order_id)
                            break
                
                if not target_qmt_order:
                    self.logger.warning(f"QMT中未找到订单ID={qmt_order_id}，也无法通过备注匹配，可能已经成交或撤销")
                    # 清理本地记录
                    if bt_order_id in self.order_submit_time:
                        del self.order_submit_time[bt_order_id]
                        self._log_queue_operation("REMOVE", "清理未找到QMT订单的提交时间", bt_order_id=bt_order_id)
                    
                    if bt_order_id in self.bt_order_mapping:
                        del self.bt_order_mapping[bt_order_id]
                        self._log_queue_operation("REMOVE", "清理未找到QMT订单的映射", bt_order_id=bt_order_id)
                    ## 撤单回调方法中清理
                    # if bt_order_id in self.pending_orders:
                    #     del self.pending_orders[bt_order_id]
                    continue
                
                # 执行撤单
                self.logger.info(f"撤销超时订单: BT订单ID={bt_order_id}, QMT订单ID={qmt_order_id}, "
                               f"股票={format_stock_code_with_name(target_qmt_order.stock_code)}, "
                               f"超时时长={timeout_order['timeout_duration']:.1f}秒")
                
                cancel_result = self.xt_trader.cancel_order_stock(self.account, qmt_order_id)
                
                if cancel_result == 0:
                    self.logger.info(f"超时订单撤销成功: {format_stock_code_with_name(target_qmt_order.stock_code)}")
                    
                    # 更新backtrader订单状态（如果存在）
                    if bt_order_id in self.pending_orders:
                        bt_order = self.pending_orders[bt_order_id]
                        bt_order.status = bt_order.Canceled
                        self._log_queue_operation("UPDATE", "超时撤单更新订单状态", bt_order_id=bt_order_id)
                    
                    # 清理记录
                    if bt_order_id in self.order_submit_time:
                        del self.order_submit_time[bt_order_id]
                        self._log_queue_operation("REMOVE", "超时撤单清理提交时间", bt_order_id=bt_order_id)
                    ## 订单回调方法中清理
                    # if bt_order_id in self.pending_orders:
                    #     del self.pending_orders[bt_order_id]
                    if bt_order_id in self.bt_order_mapping:
                        del self.bt_order_mapping[bt_order_id]
                        self._log_queue_operation("REMOVE", "超时撤单清理映射", bt_order_id=bt_order_id)
                    
                    # 判断是否为买入订单（23=买入，24=卖出）
                    is_buy = target_qmt_order.order_type == 23
                    
                    cancelled_orders.append({
                        'bt_order_id': bt_order_id,
                        'qmt_order_id': qmt_order_id,
                        'stock_code': target_qmt_order.stock_code,
                        'order_type': target_qmt_order.order_type,
                        'is_buy': is_buy,
                        'timeout_duration': timeout_order['timeout_duration']
                    })
                else:
                    self.logger.error(f"超时订单撤销失败: {format_stock_code_with_name(target_qmt_order.stock_code)}, 错误码={cancel_result}")
            
            # 记录撤单完成状态
            self._log_queue_operation("UPDATE", f"撤单检查完成，成功撤销{len(cancelled_orders)}个订单", 
                                    additional_info={'cancelled_count': len(cancelled_orders)})
            
            return cancelled_orders
            
        except Exception as e:
            self.logger.error(f"检查超时订单异常: {str(e)}")
            return []

    def getcash(self):
        if self.is_live:
            res = self.xt_trader.query_stock_asset(self.account)
            self.cash = res.cash
            self.logger.debug(f"查询现金: {self.cash:.2f}")
            return super(QMTBroker, self).getcash()
        else:
            return super(QMTBroker, self).getcash()
    
    def getvalue(self, datas=None):
        if self.is_live:
            res = self.xt_trader.query_stock_asset(self.account)
            self.value = res.market_value
            self.logger.debug(f"查询市值: {self.value:.2f}")
            return super(QMTBroker, self).getvalue(datas)
        else:
            return super(QMTBroker, self).getvalue(datas)
    
    def getposition(self, data):
        if self.is_live:
            xt_position = self.xt_trader.query_stock_position(self.account, data._dataname)
            if xt_position:
                pos = Position(size=xt_position.volume, price=xt_position.open_price)
                self.logger.debug(f"""
                查询持仓:
                - 股票: {data._dataname}
                - 数量: {xt_position.volume}
                - 成本价: {xt_position.open_price}
                """)
            else:
                pos = Position(size=0)
                self.logger.debug(f"""
                查询持仓:
                - 股票: {data._dataname}
                - 数量: 未查询到
                """)
            return pos
        else:
            return super(QMTBroker, self).getposition(data)
    
    def get_notification(self):
        """获取订单通知"""
        # 如果是实盘模式，添加调试日志
        if self.is_live:
            queue_size = len(self.notifs) if hasattr(self, 'notifs') else 0
            self.logger.debug(f"检查通知队列，当前队列大小: {queue_size}")
            
            if queue_size > 0:
                self.logger.debug(f"通知队列中的订单: {[f'ID={order.ref}, 状态={order.status}' for order in self.notifs]}")
        
        # 先处理父类的通知
        notification = super(QMTBroker, self).get_notification()
        
        # 如果是实盘模式，添加调试日志
        if self.is_live and notification:
            self.logger.info(f"获取到订单通知: 订单ID={notification.ref}, 状态={notification.status}")
        elif self.is_live:
            self.logger.debug("未获取到订单通知")
        
        return notification
    
    def buy(self, owner, data, **kwargs):
        # 根据模式选择不同的处理方式
        if self.is_live:
            # 实盘模式：手动创建订单，不调用父类方法（避免立即完成）
            bt_order = self._create_order(owner, data, True, **kwargs)
        else:
            # 回测模式：调用父类方法
            bt_order = super(QMTBroker, self).buy(owner, data, **kwargs)
        
        # 记录买入订单创建
        self._log_queue_operation("ADD", "创建买入订单", bt_order_id=bt_order.ref, 
                                additional_info={'stock_code': data._dataname, 'size': kwargs.get('size', 10), 'price': kwargs.get('price', '最新价')})
        
        self.logger.info(f"""
        ====== 创建买入委托订单 ======
        BT订单ID: {bt_order.ref}
        股票: {data._dataname}
        数量: {kwargs.get('size', 10)}
        价格: {kwargs.get('price', '最新价')}
        """)
        
        buy_size = kwargs.get('size', 10)
        price_type = kwargs.get('price_type', xtconstant.LATEST_PRICE)
        price = kwargs.get('price', 0)  # 指定价,只有price_type为xtconstant.FIX_PRICE时有效
        # price_type
        # 最新价 - xtconstant.LATEST_PRICE
        # 指定价 - xtconstant.FIX_PRICE

        # 上交所/北交所 股票
        # 最优五档即时成交剩余撤销 - xtconstant.MARKET_SH_CONVERT_5_CANCEL
        # 最优五档即时成交剩转限价 - xtconstant.MARKET_SH_CONVERT_5_LIMIT
        # 对手方最优价格委托 - xtconstant.MARKET_PEER_PRICE_FIRST
        # 本方最优价格委托 - xtconstant.MARKET_MINE_PRICE_FIRST
        # 深交所 股票 期权
        # 对手方最优价格委托 - xtconstant.MARKET_PEER_PRICE_FIRST
        # 本方最优价格委托 - xtconstant.MARKET_MINE_PRICE_FIRST
        # 即时成交剩余撤销委托 - xtconstant.MARKET_SZ_INSTBUSI_RESTCANCEL
        # 最优五档即时成交剩余撤销 - xtconstant.MARKET_SZ_CONVERT_5_CANCEL
        # 全额成交或撤销委托 - xtconstant.MARKET_SZ_FULL_OR_CANCEL
        
        ## TODO：先使用xtconstant.LATEST_PRICE，后续更精细的操作后续再优化
        # 生成包含BT订单ID和股票代码的备注
        order_remark = self._generate_order_remark(bt_order, data._dataname)
        
        # 将订单添加到待处理列表
        self.pending_orders[bt_order.ref] = bt_order
        
        # 记录订单提交时间
        from datetime import datetime
        submit_time = datetime.now()
        self.order_submit_time[bt_order.ref] = submit_time
        
        # 记录队列添加操作
        self._log_queue_operation("ADD", "添加买入订单到pending_orders", bt_order_id=bt_order.ref, 
                                additional_info={'submit_time': submit_time})

        if self.is_live:
            try:
                order_go = self.xt_trader.order_stock_async(
                    self.account, 
                    data._dataname, 
                    xtconstant.STOCK_BUY, 
                    buy_size, 
                    price_type, 
                    price,
                    '三重共振',  # strategy_name
                    order_remark  # order_remark - 使用包含BT订单ID和股票代码的备注
                )
                # 实盘模式：不立即设置状态和通知，等待QMT回调
                self.logger.info(f"实盘买入订单提交结果: {order_go}, BT订单ID: {bt_order.ref}, 等待QMT回调更新状态")
                
                # 记录订单提交到QMT
                self._log_queue_operation("UPDATE", "买入订单已提交到QMT", bt_order_id=bt_order.ref, 
                                        additional_info={'qmt_result': order_go, 'order_remark': order_remark})
                
            except Exception as e:
                self.logger.error(f"实盘买入订单异常: {str(e)}")
                
                # 从待处理列表中移除失败的订单
                if bt_order.ref in self.pending_orders:
                    del self.pending_orders[bt_order.ref]
                    self._log_queue_operation("REMOVE", "买入失败移除pending_orders", bt_order_id=bt_order.ref)
                if bt_order.ref in self.order_submit_time:
                    del self.order_submit_time[bt_order.ref]
                    self._log_queue_operation("REMOVE", "买入失败移除order_submit_time", bt_order_id=bt_order.ref)
                    
                bt_order.status = bt_order.Rejected
                self.notifs.append(bt_order)  # 通知策略订单被拒绝
                raise
                
        return bt_order

    def sell(self, owner, data, **kwargs):
        # 根据模式选择不同的处理方式
        if self.is_live:
            # 实盘模式：手动创建订单，不调用父类方法（避免立即完成）
            bt_order = self._create_order(owner, data, False, **kwargs)
        else:
            # 回测模式：调用父类方法
            bt_order = super(QMTBroker, self).sell(owner, data, **kwargs)
        
        # 记录卖出订单创建
        self._log_queue_operation("ADD", "创建卖出订单", bt_order_id=bt_order.ref, 
                                additional_info={'stock_code': data._dataname, 'size': kwargs.get('size', 10), 'price': kwargs.get('price', '最新价')})
        
        self.logger.info(f"""
        ====== 创建卖出委托订单 ======
        BT订单ID: {bt_order.ref}
        股票: {data._dataname}
        数量: {kwargs.get('size', 10)}
        价格: {kwargs.get('price', '最新价')}
        """)
        
        sell_size = kwargs.get('size', 10)
        price_type = kwargs.get('price_type', xtconstant.LATEST_PRICE)
        price = kwargs.get('price', 0)  # 指定价,只有price_type为xtconstant.FIX_PRICE时有效

        # 生成包含BT订单ID和股票代码的备注
        order_remark = self._generate_order_remark(bt_order, data._dataname)
        
        # 将订单添加到待处理列表
        self.pending_orders[bt_order.ref] = bt_order
        
        # 记录订单提交时间
        from datetime import datetime
        submit_time = datetime.now()
        self.order_submit_time[bt_order.ref] = submit_time
        
        # 记录队列添加操作
        self._log_queue_operation("ADD", "添加卖出订单到pending_orders", bt_order_id=bt_order.ref, 
                                additional_info={'submit_time': submit_time})

        if self.is_live:
            try:
                ## 卖出前先将当前股票的买入单撤回
                ### 先获取当前股票的可撤买入单
                orders = self.xt_trader.query_stock_orders(self.account, cancelable_only=True)
                
                # 过滤出当前股票的订单
                current_stock_orders = []
                if orders:
                    current_stock_orders = [order for order in orders if order.stock_code == data._dataname]
                
                self.logger.info(f"当前股票({data._dataname})的可撤单数量: {len(current_stock_orders)}")

                # 遍历当前股票的订单，撤回所有订单, order_type: 23->买入, 24->卖出
                if current_stock_orders:
                    for order in current_stock_orders:
                        self.logger.info(f"当前股票的可撤单: {order.stock_code}, order_type: {order.order_type}")
                        # 不管买卖都撤，防止急跌，降低回撤
                        self.logger.info(f"股票代码: {data._dataname}, 撤回订单: {order.order_id}, 撤回数量: {order.order_volume}")
                        cancel_result = self.xt_trader.cancel_order_stock(self.account, order.order_id)
                        if cancel_result == 0:
                            self.logger.info(f"股票代码: {data._dataname}, 撤回订单: {order.order_id}, 撤回成功")
                        elif cancel_result == -1:
                            self.logger.error(f"股票代码: {data._dataname}, 委托已完成撤单失败")
                        elif cancel_result == -2:
                            self.logger.error(f"股票代码: {data._dataname}, 未找到对应委托编号撤单失败")
                        elif cancel_result == -3:
                            self.logger.error(f"股票代码: {data._dataname}, 账号未登陆撤单失败")
                        else:
                            self.logger.error(f"股票代码: {data._dataname}, 撤回订单: {order.order_id}, 撤回失败")
                        
                order_go = self.xt_trader.order_stock_async(
                    self.account, 
                    data._dataname, 
                    xtconstant.STOCK_SELL, 
                    sell_size, 
                    price_type, 
                    price,
                    '三重共振',  # strategy_name
                    order_remark  # order_remark - 使用包含BT订单ID和股票代码的备注
                )
                # 实盘模式：不立即设置状态和通知，等待QMT回调
                self.logger.info(f"实盘卖出订单提交结果: {order_go}, BT订单ID: {bt_order.ref}, 等待QMT回调更新状态")
                
                # 记录订单提交到QMT
                self._log_queue_operation("UPDATE", "卖出订单已提交到QMT", bt_order_id=bt_order.ref, 
                                        additional_info={'qmt_result': order_go, 'order_remark': order_remark})
                
            except Exception as e:
                self.logger.error(f"实盘卖出订单异常: {str(e)}")
                
                # 从待处理列表中移除失败的订单
                if bt_order.ref in self.pending_orders:
                    del self.pending_orders[bt_order.ref]
                    self._log_queue_operation("REMOVE", "卖出失败移除pending_orders", bt_order_id=bt_order.ref)
                if bt_order.ref in self.order_submit_time:
                    del self.order_submit_time[bt_order.ref]
                    self._log_queue_operation("REMOVE", "卖出失败移除order_submit_time", bt_order_id=bt_order.ref)
                    
                bt_order.status = bt_order.Rejected
                self.notifs.append(bt_order)  # 通知策略订单被拒绝
                raise
                
        return bt_order

    def process_order_callback(self, qmt_order):
        """
        统一处理委托回调，避免重复通知
        """
        try:
            self.logger.debug(f"[{self.broker_id}] === 进入process_order_callback ===")
            self.logger.debug(f"[{self.broker_id}] QMT订单信息: stock_code={qmt_order.stock_code}, order_status={qmt_order.order_status}, order_remark='{qmt_order.order_remark}'")
            self.logger.debug(f"[{self.broker_id}] QMT订单详细: order_id={getattr(qmt_order, 'order_id', 'NOT_FOUND')}, traded_price={qmt_order.traded_price}, traded_volume={qmt_order.traded_volume}")
            
            # 记录回调处理开始
            self._log_queue_operation("QUERY", "process_order_callback开始处理", 
                                    additional_info={'qmt_order_status': qmt_order.order_status, 'qmt_order_remark': qmt_order.order_remark})
            
            bt_order_id = self._extract_bt_order_id(qmt_order.order_remark)
            self.logger.debug(f"[{self.broker_id}] 提取的BT订单ID: {bt_order_id}")
            
            if not bt_order_id:
                self.logger.warning(f"[{self.broker_id}] 委托回调中未能提取BT订单ID: order_remark='{qmt_order.order_remark}'")
                return
            
            self.logger.debug(f"[{self.broker_id}] 当前pending_orders: {list(self.pending_orders.keys())}")
            
            if bt_order_id not in self.pending_orders:
                self.logger.warning(f"[{self.broker_id}] 委托回调中BT订单ID {bt_order_id} 不在pending_orders中")
                self._log_queue_operation("QUERY", "订单不在pending_orders中", bt_order_id=bt_order_id)
                return
            
            bt_order = self.pending_orders[bt_order_id]
            old_status = bt_order.status
            self.logger.debug(f"[{self.broker_id}] 找到BT订单: ID={bt_order_id}, 当前状态={old_status}")
            
            # 根据QMT订单状态更新backtrader订单状态
            new_status = None
            if qmt_order.order_status == 50:  # 已报
                new_status = bt_order.Submitted
            elif qmt_order.order_status == 55:  # 部分成交
                new_status = bt_order.Partial
            elif qmt_order.order_status == 56:  # 全部成交
                new_status = bt_order.Completed
            elif qmt_order.order_status in [54, 53]:  # 已撤， 部撤
                new_status = bt_order.Canceled
            elif qmt_order.order_status == 57:  # 拒绝
                new_status = bt_order.Rejected
            else:
                self.logger.warning(f"[{self.broker_id}] 未知的QMT订单状态: {qmt_order.order_status}")
                return
            
            bt_order.status = new_status
            self.logger.info(f"[{self.broker_id}] 委托回调处理: BT订单ID {bt_order_id}, {old_status} -> {bt_order.status} (QMT状态: {qmt_order.order_status})")
            
            # 记录状态更新
            self._log_queue_operation("UPDATE", "更新订单状态", bt_order_id=bt_order_id, 
                                    additional_info={'old_status': old_status, 'new_status': bt_order.status, 'qmt_status': qmt_order.order_status})
            
            # 如果有成交信息，更新执行信息
            if qmt_order.traded_price > 0 and qmt_order.traded_volume > 0:
                self.logger.debug(f"[{self.broker_id}] 更新执行信息: 成交价格={qmt_order.traded_price}, 成交数量={qmt_order.traded_volume}")
                
                # 创建或更新执行信息
                if not hasattr(bt_order, 'executed') or bt_order.executed is None:
                    from backtrader.order import OrderExecuted
                    bt_order.executed = OrderExecuted()
                    self.logger.debug(f"[{self.broker_id}] 创建新的执行信息对象")
                
                # 更新执行价格和数量
                bt_order.executed.price = qmt_order.traded_price
                bt_order.executed.size = qmt_order.traded_volume
                bt_order.executed.value = qmt_order.traded_price * qmt_order.traded_volume
                
                # 计算手续费
                bt_order.executed.comm = self._calculate_commission(
                    qmt_order.traded_volume, qmt_order.traded_price, 
                    48 if bt_order.isbuy() else 49  # 48买入, 49卖出
                )
                
                # 记录执行信息更新
                self._log_queue_operation("UPDATE", "更新执行信息", bt_order_id=bt_order_id, 
                                        additional_info={'executed_price': qmt_order.traded_price, 'executed_size': qmt_order.traded_volume, 'executed_value': bt_order.executed.value})
                
                self.logger.info(f"""
                [{self.broker_id}] ====== 委托回调执行信息更新 ======
                BT订单ID: {bt_order_id}
                执行价格: {bt_order.executed.price:.4f}
                执行数量: {bt_order.executed.size}
                执行金额: {bt_order.executed.value:.2f}
                手续费: {bt_order.executed.comm:.2f}
                """)
            else:
                self.logger.debug(f"[{self.broker_id}] 无成交信息: 成交价格={qmt_order.traded_price}, 成交数量={qmt_order.traded_volume}")
            
            # 只有状态发生变化时才通知策略
            if old_status != bt_order.status:
                self.logger.info(f"[{self.broker_id}] 添加订单通知到队列: BT订单ID {bt_order_id}, 状态: {bt_order.status}")
                self.notifs.append(bt_order)
                
                # 如果订单已完成或被拒绝/取消，从待处理列表中移除
                if bt_order.status in [bt_order.Completed, bt_order.Rejected, bt_order.Canceled]:
                    if bt_order_id in self.pending_orders:
                        del self.pending_orders[bt_order_id]
                        self._log_queue_operation("REMOVE", "委托回调移除pending_orders", bt_order_id=bt_order_id, 
                                                additional_info={'final_status': bt_order.status})
                        self.logger.debug(f"[{self.broker_id}] 从pending_orders中移除: {bt_order_id}")
                    if bt_order_id in self.order_submit_time:
                        del self.order_submit_time[bt_order_id]
                        self._log_queue_operation("REMOVE", "委托回调移除order_submit_time", bt_order_id=bt_order_id, 
                                                additional_info={'final_status': bt_order.status})
                        self.logger.debug(f"[{self.broker_id}] 从order_submit_time中移除: {bt_order_id}")
                    self.logger.debug(f"[{self.broker_id}] 订单已完成，清理记录: BT订单ID={bt_order_id}")
            else:
                self.logger.debug(f"[{self.broker_id}] 订单状态未变化，不发送通知: BT订单ID {bt_order_id}, 状态: {bt_order.status}")
            
            self.logger.debug(f"[{self.broker_id}] === 退出process_order_callback ===")
                
        except Exception as e:
            self.logger.error(f"[{self.broker_id}] 处理委托回调失败: {str(e)}")
            import traceback
            self.logger.error(f"[{self.broker_id}] 异常堆栈: {traceback.format_exc()}")

