import sqlite3
import logging
import datetime
import pandas as pd
import akshare as ak
import yaml
from typing import Optional, Dict, List, Set
import os

# 尝试导入xtdata，如果失败则记录警告
try:
    from xtquant import xtdata
    XTDATA_AVAILABLE = True
except ImportError:
    XTDATA_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("xtdata模块未安装，将使用默认的市场代码映射规则")

# 配置日志
logger = logging.getLogger(__name__)

class Stock:
    """可转债股票信息类"""
    
    def __init__(self, bond_code: str = None, bond_name: str = None, 
                 equity_code: str = None, equity_name: str = None,
                 issue_size: float = None, listing_date: str = None, 
                 credit_rating: str = None):
        """
        初始化Stock实例
        
        Args:
            bond_code: 债券代码
            bond_name: 债券简称
            equity_code: 正股代码
            equity_name: 正股简称
            issue_size: 发行规模(亿元)
            listing_date: 上市时间
            credit_rating: 信用评级
        """
        self.bond_code = bond_code
        self.bond_name = bond_name
        self.equity_code = equity_code
        self.equity_name = equity_name
        self.issue_size = issue_size
        self.listing_date = listing_date
        self.credit_rating = credit_rating
    
    def get_display_name(self, code: str) -> str:
        """
        根据股票代码获取对应的中文简称
        
        Args:
            code: 股票代码（可以是债券代码或正股代码）
            
        Returns:
            str: 对应的中文简称，如果找不到返回空字符串
        """
        if code == self.bond_code and self.bond_name:
            return self.bond_name
        elif code == self.equity_code and self.equity_name:
            return self.equity_name
        return ""
    
    def __str__(self):
        return f"Stock(bond_code={self.bond_code}, bond_name={self.bond_name}, equity_code={self.equity_code}, equity_name={self.equity_name})"
    
    def __repr__(self):
        return self.__str__()


class StockDataManager:
    """股票数据管理器"""
    
    def __init__(self, db_path: str = "stock_info.sqlite"):
        """
        初始化数据管理器
        
        Args:
            db_path: SQLite数据库文件路径
        """
        self.db_path = db_path
        self.stock_cache: Dict[str, Stock] = {}
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS stock_info (
                        bond_code TEXT PRIMARY KEY,
                        bond_name TEXT,
                        equity_code TEXT,
                        equity_name TEXT,
                        issue_size REAL,
                        listing_date TEXT,
                        credit_rating TEXT,
                        update_time TEXT
                    )
                ''')
                
                # 创建索引以提高查询性能
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_equity_code ON stock_info(equity_code)
                ''')
                
                conn.commit()
                logger.info("数据库初始化完成")
        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            raise
    
    def _get_xtdata_convertible_bonds(self) -> Set[str]:
        """
        从xtdata获取沪深可转债列表（带市场代码）
        
        Returns:
            Set[str]: 包含市场代码的可转债代码集合，如 {'113682.SH', '123456.SZ'}
        """
        if not XTDATA_AVAILABLE:
            logger.warning("xtdata不可用，无法获取准确的可转债列表")
            return set()
        
        try:
            # 获取沪深转债列表
            cb_list = xtdata.get_stock_list_in_sector('沪深转债')
            if cb_list:
                logger.info(f"从xtdata获取到 {len(cb_list)} 个可转债代码")
                return set(cb_list)
            else:
                logger.warning("xtdata返回空的可转债列表")
                return set()
        except Exception as e:
            logger.error(f"从xtdata获取可转债列表失败: {str(e)}")
            return set()
    
    def _build_bond_code_mapping(self) -> Dict[str, str]:
        """
        建立债券代码映射表：无市场代码 -> 带市场代码
        
        Returns:
            Dict[str, str]: 映射字典，如 {'113682': '113682.SH'}
        """
        mapping = {}
        
        # 获取xtdata的可转债列表
        xtdata_bonds = self._get_xtdata_convertible_bonds()
        
        if xtdata_bonds:
            # 使用xtdata的数据建立映射
            for bond_code_with_market in xtdata_bonds:
                if '.' in bond_code_with_market:
                    bond_code_without_market = bond_code_with_market.split('.')[0]
                    mapping[bond_code_without_market] = bond_code_with_market
            
            logger.info(f"基于xtdata建立了 {len(mapping)} 个债券代码映射")
        else:
            # 如果xtdata不可用，使用默认规则
            logger.info("使用默认的债券代码映射规则")
        
        return mapping
    
    def _map_bond_code_to_market(self, bond_code: str) -> str:
        """
        将债券代码映射到包含市场代码的格式
        
        Args:
            bond_code: 原始债券代码（如 113682）
            
        Returns:
            str: 包含市场代码的债券代码（如 113682.SH）
        """
        if not bond_code:
            return bond_code
            
        # 如果已经包含市场代码，直接返回
        if '.' in bond_code:
            return bond_code
        
        # 首先尝试使用xtdata的映射
        if not hasattr(self, '_bond_mapping_cache'):
            self._bond_mapping_cache = self._build_bond_code_mapping()
        
        if self._bond_mapping_cache and bond_code in self._bond_mapping_cache:
            return self._bond_mapping_cache[bond_code]
        
        # 如果xtdata映射中没有找到，使用默认规则
        # 沪市可转债：110xxx, 113xxx, 118xxx等
        # 深市可转债：123xxx, 127xxx, 128xxx等
        if bond_code.startswith(('110', '113', '118')):
            return f"{bond_code}.SH"
        elif bond_code.startswith(('123', '127', '128')):
            return f"{bond_code}.SZ"
        else:
            # 默认为沪市
            logger.warning(f"无法确定债券代码 {bond_code} 的市场，默认设为沪市")
            return f"{bond_code}.SH"
    
    def _map_equity_code_to_market(self, equity_code: str) -> str:
        """
        将正股代码映射到包含市场代码的格式
        
        Args:
            equity_code: 原始正股代码（如 000001）
            
        Returns:
            str: 包含市场代码的正股代码（如 000001.SZ）
        """
        if not equity_code:
            return equity_code
            
        # 如果已经包含市场代码，直接返回
        if '.' in equity_code:
            return equity_code
        
        # 根据代码规则判断市场
        # 沪市：600xxx, 601xxx, 603xxx, 605xxx等
        # 深市：000xxx, 001xxx, 002xxx, 003xxx等
        # 创业板：300xxx
        if equity_code.startswith(('600', '601', '603', '605', '688')):
            return f"{equity_code}.SH"
        elif equity_code.startswith(('000', '001', '002', '003', '300')):
            return f"{equity_code}.SZ"
        else:
            # 默认为深市
            logger.warning(f"无法确定正股代码 {equity_code} 的市场，默认设为深市")
            return f"{equity_code}.SZ"
    
    def update_stock_basic_info(self) -> bool:
        """
        从akshare获取最新的可转债基本信息并更新到数据库
        在离线回测模式下（is_live=false）将跳过更新
        
        Returns:
            bool: 更新是否成功
        """
        # 读取配置文件判断是否为离线回测模式
        try:
            with open('config.yaml', 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                is_live = config.get('backtest', {}).get('is_live', False)
        except Exception as e:
            logger.error(f"读取配置文件失败: {str(e)}")
            # 如果读取配置失败，默认为离线模式以避免意外的网络请求
            is_live = False
        
        if not is_live:
            logger.info("离线回测模式（is_live=false），跳过股票基本信息更新")
            return True
            
        try:
            logger.info("实盘模式，开始从akshare获取可转债基本信息...")
            
            # 获取xtdata的可转债列表用于过滤
            xtdata_bonds = self._get_xtdata_convertible_bonds()
            if xtdata_bonds:
                # 创建无市场代码的集合用于匹配
                xtdata_bonds_no_market = {bond.split('.')[0] for bond in xtdata_bonds if '.' in bond}
                logger.info(f"xtdata中有 {len(xtdata_bonds_no_market)} 个可转债代码用于过滤")
            else:
                xtdata_bonds_no_market = set()
                logger.warning("未获取到xtdata可转债列表，将处理所有akshare数据")
            
            # 调用akshare接口获取可转债数据
            df = ak.bond_zh_cov()
            
            if df is None or df.empty:
                logger.warning("akshare返回空数据，跳过更新")
                return False
            
            logger.info(f"获取到 {len(df)} 条可转债数据")
            
            # 处理数据并存储到数据库
            update_count = 0
            filtered_count = 0
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for _, row in df.iterrows():
                    try:
                        # 提取需要的字段
                        bond_code = str(row.get('债券代码', '')).strip()
                        bond_name = str(row.get('债券简称', '')).strip()
                        equity_code = str(row.get('正股代码', '')).strip()
                        equity_name = str(row.get('正股简称', '')).strip()
                        issue_size = row.get('发行规模', None)
                        listing_date = str(row.get('上市时间', '')).strip()
                        credit_rating = str(row.get('信用评级', '')).strip()
                        
                        # 跳过无效数据
                        if not bond_code or bond_code == 'nan':
                            continue
                        
                        # 如果有xtdata数据，过滤掉不在xtdata中的债券
                        if xtdata_bonds_no_market and bond_code not in xtdata_bonds_no_market:
                            filtered_count += 1
                            logger.debug(f"过滤掉不在xtdata中的债券: {bond_code} {bond_name}")
                            continue
                        
                        # 映射市场代码
                        bond_code_with_market = self._map_bond_code_to_market(bond_code)
                        equity_code_with_market = self._map_equity_code_to_market(equity_code) if equity_code and equity_code != 'nan' else equity_code
                        
                        # 处理发行规模
                        if pd.isna(issue_size):
                            issue_size = None
                        
                        # 处理上市时间
                        if listing_date == 'nan' or listing_date == 'NaT':
                            listing_date = None
                        
                        # 处理信用评级
                        if credit_rating == 'nan':
                            credit_rating = None
                        
                        # 插入或更新数据
                        cursor.execute('''
                            INSERT OR REPLACE INTO stock_info 
                            (bond_code, bond_name, equity_code, equity_name, issue_size, listing_date, credit_rating, update_time)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            bond_code_with_market, bond_name, equity_code_with_market, equity_name,
                            issue_size, listing_date, credit_rating, datetime.datetime.now().isoformat()
                        ))
                        
                        update_count += 1
                        
                    except Exception as e:
                        logger.error(f"处理数据行时出错: {str(e)}, 数据: {row.to_dict()}")
                        continue
                
                conn.commit()
            
            if filtered_count > 0:
                logger.info(f"过滤掉 {filtered_count} 个不在xtdata中的债券")
            logger.info(f"成功更新 {update_count} 条股票基本信息到数据库")
            return True
            
        except Exception as e:
            logger.error(f"更新股票基本信息失败: {str(e)}")
            return False
    
    def get_stock_name(self, stock_code: str) -> Optional[str]:
        """
        根据股票代码获取对应的中文简称
        
        Args:
            stock_code: 股票代码（债券代码或正股代码）
            
        Returns:
            Optional[str]: 对应的中文简称，如果找不到返回None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 首先尝试作为债券代码查询
                cursor.execute('SELECT bond_name FROM stock_info WHERE bond_code = ?', (stock_code,))
                result = cursor.fetchone()
                if result and result[0]:
                    return result[0]
                
                # 然后尝试作为正股代码查询
                cursor.execute('SELECT equity_name FROM stock_info WHERE equity_code = ?', (stock_code,))
                result = cursor.fetchone()
                if result and result[0]:
                    return result[0]
                
                return None
                
        except Exception as e:
            logger.error(f"查询股票名称失败: {str(e)}")
            return None
    
    def load_stock_by_code(self, stock_code: str) -> Optional[Stock]:
        """
        根据股票代码从数据库加载Stock实例
        
        Args:
            stock_code: 股票代码（债券代码或正股代码）
            
        Returns:
            Optional[Stock]: Stock实例，如果找不到返回None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 首先尝试作为债券代码查询
                cursor.execute('''
                    SELECT bond_code, bond_name, equity_code, equity_name, issue_size, listing_date, credit_rating
                    FROM stock_info WHERE bond_code = ?
                ''', (stock_code,))
                result = cursor.fetchone()
                
                if not result:
                    # 然后尝试作为正股代码查询
                    cursor.execute('''
                        SELECT bond_code, bond_name, equity_code, equity_name, issue_size, listing_date, credit_rating
                        FROM stock_info WHERE equity_code = ?
                    ''', (stock_code,))
                    result = cursor.fetchone()
                
                if result:
                    return Stock(
                        bond_code=result[0],
                        bond_name=result[1],
                        equity_code=result[2],
                        equity_name=result[3],
                        issue_size=result[4],
                        listing_date=result[5],
                        credit_rating=result[6]
                    )
                
                return None
                
        except Exception as e:
            logger.error(f"加载股票信息失败: {str(e)}")
            return None
    
    def initialize_stock_cache(self, stock_codes: List[str]) -> Dict[str, Stock]:
        """
        初始化股票缓存，为选股列表中的股票创建Stock实例
        
        Args:
            stock_codes: 股票代码列表
            
        Returns:
            Dict[str, Stock]: 股票代码到Stock实例的映射
        """
        logger.info(f"开始初始化 {len(stock_codes)} 只股票的缓存")
        
        self.stock_cache.clear()
        success_count = 0
        
        for stock_code in stock_codes:
            stock = self.load_stock_by_code(stock_code)
            if stock:
                self.stock_cache[stock_code] = stock
                success_count += 1
                logger.debug(f"成功加载股票 {stock_code}: {stock.get_display_name(stock_code)}")
            else:
                logger.warning(f"未找到股票 {stock_code} 的信息")
        
        logger.info(f"股票缓存初始化完成: 成功 {success_count}/{len(stock_codes)} 只")
        return self.stock_cache
    
    def get_stock_from_cache(self, stock_code: str) -> Optional[Stock]:
        """
        从缓存获取Stock实例
        
        Args:
            stock_code: 股票代码
            
        Returns:
            Optional[Stock]: Stock实例，如果缓存中没有返回None
        """
        return self.stock_cache.get(stock_code)
    
    def format_stock_code_with_name(self, stock_code: str) -> str:
        """
        格式化股票代码，如果有中文简称则一起显示
        
        Args:
            stock_code: 股票代码
            
        Returns:
            str: 格式化后的字符串，如 "113682(益丰转债)" 或 "113682"
        """
        # 首先尝试从缓存获取
        stock = self.get_stock_from_cache(stock_code)
        if stock:
            name = stock.get_display_name(stock_code)
            if name:
                return f"{stock_code}({name})"
        
        # 如果缓存中没有，尝试从数据库查询
        name = self.get_stock_name(stock_code)
        if name:
            return f"{stock_code}({name})"
        
        # 如果都没有，只返回代码
        return stock_code


# 全局数据管理器实例
_stock_data_manager = None

def get_stock_data_manager() -> StockDataManager:
    """获取全局股票数据管理器实例"""
    global _stock_data_manager
    if _stock_data_manager is None:
        _stock_data_manager = StockDataManager()
    return _stock_data_manager

def format_stock_code_with_name(stock_code: str) -> str:
    """
    便捷函数：格式化股票代码，如果有中文简称则一起显示
    
    Args:
        stock_code: 股票代码
        
    Returns:
        str: 格式化后的字符串
    """
    return get_stock_data_manager().format_stock_code_with_name(stock_code) 