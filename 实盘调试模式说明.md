# 实盘调试模式说明

## 功能概述

实盘调试模式是为了在实盘环境中进行策略测试而设计的功能。在此模式下，策略会使用固定的小仓位进行交易，以降低测试风险。

## 配置参数

在 `config.yaml` 文件的 `backtest` 部分添加以下配置：

```yaml
backtest:
  is_live: true                # 实盘模式开关
  is_live_debug: true          # 实盘调试模式开关
  live_debug_size: 10          # 实盘调试模式固定仓位大小
```

### 参数说明

- **is_live**: 实盘模式总开关
  - `true`: 启用实盘模式
  - `false`: 回测模式

- **is_live_debug**: 实盘调试模式开关
  - `true`: 启用实盘调试模式（仅在 `is_live=true` 时生效）
  - `false`: 实盘正式模式

- **live_debug_size**: 实盘调试模式下的固定仓位大小
  - 默认值: 10
  - 单位: 股数

## 模式说明

### 1. 回测模式 (`is_live=false`)
- 使用历史数据进行策略回测
- 按照策略配置的仓位比例计算买入数量
- **实盘调试配置不生效**

### 2. 实盘正式模式 (`is_live=true`, `is_live_debug=false`)
- 在真实市场环境中运行策略
- 按照策略配置的仓位比例计算买入数量
- 发送微信交易通知

### 3. 实盘调试模式 (`is_live=true`, `is_live_debug=true`)
- 在真实市场环境中运行策略
- **使用固定的小仓位进行交易**
- 发送微信交易通知
- 适合策略验证和风险控制

## 使用场景

### 策略验证
- 新策略上线前的小资金验证
- 策略参数调优时的风险控制
- 市场环境变化后的策略适应性测试

### 风险控制
- 限制单次交易的最大损失
- 降低策略错误的影响
- 保护账户资金安全

## 代码实现

### KDJ策略中的实现

```python
# 初始化时获取配置
self.is_live = CONFIG['backtest'].get('is_live', False)
self.is_live_debug = CONFIG['backtest'].get('is_live_debug', False) and self.is_live
self.live_debug_size = CONFIG['backtest'].get('live_debug_size', 10)

# 买入时的仓位计算
if self.is_live_debug:
    # 实盘调试模式：使用固定仓位大小
    size = self.live_debug_size
    self.log(f'实盘调试模式 - 使用固定仓位: {size}', level=logging.INFO)
else:
    # 正常模式：按比例计算仓位
    equity = self.broker.getvalue()
    position_value = equity * self.p.current_position_size
    size = int(position_value / (self.dataclose[0] * 10)) * 10
    size = max(size, 10)
```

### ConnorsRSI策略中的实现

```python
# 买入时检查实盘调试模式
if self.is_live_debug:
    # 实盘调试模式：使用固定仓位大小
    size = self.live_debug_size
    self.log(f'实盘调试模式 - 使用固定仓位: {size}', level=logging.INFO)
```

## 日志输出

策略会在日志中明确显示当前运行模式：

```
策略模式: 实盘调试模式 (固定仓位: 10)
策略模式: 实盘正式模式
策略模式: 回测模式
```

在状态报告中也会显示当前模式：

```
当前模式: 实盘调试模式 (固定仓位: 10)
```

## 注意事项

1. **安全性**: 实盘调试模式只在实盘环境下生效，回测模式下会被忽略
2. **仓位限制**: 调试模式下仓位固定，不会根据资金比例动态调整
3. **风险控制**: 建议在正式使用前先用调试模式验证策略有效性
4. **配置管理**: 正式运行时记得关闭调试模式（`is_live_debug=false`）

## 测试验证

可以使用 `test_live_debug_config.py` 脚本验证配置是否正确：

```bash
python test_live_debug_config.py
```

该脚本会显示当前配置状态和模拟的仓位计算结果。 