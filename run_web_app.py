#!/usr/bin/env python3
"""
启动Web应用程序的主入口

这个脚本将启动Streamlit Web界面，并初始化所有必要的组件
"""
import os
import sys
import logging
import subprocess
import time
from datetime import datetime

# 配置日志
logs_dir = "logs"
os.makedirs(logs_dir, exist_ok=True)
log_file = os.path.join(logs_dir, f"web_app_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("web_app")

def init_system():
    """初始化系统，确保配置正确"""
    logger.info("初始化Web应用系统...")
    
    # 导入配置初始化模块
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from quant_app.core.import_config import import_config_from_yaml
        
        # 确保配置已经导入到数据库
        yaml_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.yaml')
        if os.path.exists(yaml_path):
            logger.info(f"检测到YAML配置文件: {yaml_path}")
            # 如果数据库中没有配置，则导入
            from quant_app.core.db_config_manager import db_config_manager
            if not db_config_manager.get_sections():
                logger.info("数据库中没有配置，从YAML导入...")
                import_config_from_yaml(yaml_path, clear_existing=True)
                logger.info("配置导入完成")
            else:
                logger.info("数据库配置已存在，跳过导入")
        else:
            logger.warning(f"未找到YAML配置文件: {yaml_path}")
            
    except Exception as e:
        logger.error(f"初始化配置失败: {str(e)}")
    
    # 检查数据目录
    data_dirs = [
        "quant_app/data",
        "quant_app/data/market_data",
        "quant_app/data/exports",
        "quant_app/data/logs"
    ]
    
    for directory in data_dirs:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"确保目录存在: {directory}")

def start_streamlit():
    """启动Streamlit Web应用"""
    logger.info("启动Streamlit Web应用...")
    
    # 主页文件路径
    app_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                         "quant_app", "app", "Home.py")
    
    if not os.path.exists(app_file):
        logger.error(f"主页文件不存在: {app_file}")
        return False
    
    # 运行Streamlit命令
    try:
        cmd = [
            "streamlit", "run", app_file,
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.serverAddress", "localhost",
            "--theme.base", "light"
        ]
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        # 使用subprocess启动Streamlit
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1
        )
        
        logger.info(f"Streamlit进程已启动，PID: {process.pid}")
        
        # 输出进程日志
        for line in process.stdout:
            logger.info(f"Streamlit: {line.strip()}")
            
        return True
        
    except Exception as e:
        logger.error(f"启动Streamlit失败: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("=" * 50)
    logger.info("启动量化交易系统Web界面")
    logger.info("=" * 50)
    
    # 初始化系统
    init_system()
    
    # 启动Streamlit
    success = start_streamlit()
    
    if not success:
        logger.error("启动失败，退出程序")
        sys.exit(1)
    
    logger.info("Web应用已关闭") 