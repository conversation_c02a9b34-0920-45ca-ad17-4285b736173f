#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易进程监控工具
实时显示交易进程状态和最新日志
"""

import os
import time
import glob
from datetime import datetime
from simple_trade_manager import SimpleTradeManager
from stock_data_manager import format_stock_code_with_name

def get_latest_log_content(stock_code, log_type='strategy', lines=5):
    """获取最新日志内容"""
    try:
        # 查找最新的日志文件
        pattern = f"logs/{log_type}_{stock_code}_*.log"
        log_files = glob.glob(pattern)
        
        if not log_files:
            return f"未找到 {log_type} 日志文件"
        
        # 获取最新的日志文件
        latest_log = max(log_files, key=os.path.getctime)
        
        # 读取最后几行
        with open(latest_log, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) >= lines else all_lines
            
        return ''.join(recent_lines).strip()
    except Exception as e:
        return f"读取日志失败: {str(e)}"

def monitor_trading():
    """监控交易进程"""
    manager = SimpleTradeManager()
    
    print("=" * 80)
    print("[监控] 交易进程监控工具")
    print("=" * 80)
    print("按 Ctrl+C 退出监控")
    print()
    
    try:
        while True:
            # 清屏（Windows）
            os.system('cls' if os.name == 'nt' else 'clear')
            
            print("=" * 80)
            print(f"[监控] 交易进程监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 80)
            
            # 获取活跃股票
            active_stocks = manager.get_active_stocks()
            
            if not active_stocks:
                print("[状态] 当前没有活跃的交易进程")
                print("\n[提示] 使用 'python simple_trade_manager.py --init' 启动交易进程")
            else:
                print(f"[信息] 活跃股票数量: {len(active_stocks)}")
                print()
                
                for i, stock_code in enumerate(active_stocks, 1):
                    formatted_code = format_stock_code_with_name(stock_code)
                    print(f"【{i}】{formatted_code}")
                    print("-" * 60)
                    
                    # 获取进程状态
                    status = manager.get_stock_status(stock_code)
                    if status['status'] != 'not_trading':
                        print(f"[运行] 进程状态: 运行中 (进程数: {status.get('process_count', 'N/A')})")
                        if status.get('processes'):
                            for proc in status['processes']:
                                print(f"   PID: {proc['pid']}")
                    else:
                        print("[停止] 进程状态: 未运行")
                    
                    # 显示最新的策略日志
                    print("\n[日志] 最新策略日志:")
                    strategy_log = get_latest_log_content(stock_code, 'strategy', 3)
                    if strategy_log:
                        for line in strategy_log.split('\n'):
                            if line.strip():
                                print(f"   {line}")
                    else:
                        print("   无日志内容")
                    
                    # 显示最新的经纪商日志
                    print("\n[经纪] 最新经纪商日志:")
                    broker_log = get_latest_log_content(stock_code, 'qmtbroker', 2)
                    if broker_log:
                        for line in broker_log.split('\n'):
                            if line.strip():
                                print(f"   {line}")
                    else:
                        print("   无日志内容")
                    
                    print()
            
            print("=" * 80)
            print("[提示] 管理命令:")
            print("   python trade_cli.py list                    # 查看活跃股票")
            print("   python trade_cli.py status <股票代码>        # 查看详细状态")
            print("   python trade_cli.py stop-all               # 停止所有交易")
            print("=" * 80)
            
            # 等待5秒后刷新
            time.sleep(5)
            
    except KeyboardInterrupt:
        print("\n\n[退出] 监控已停止")
    except Exception as e:
        print(f"\n[错误] 监控出错: {str(e)}")

if __name__ == "__main__":
    monitor_trading() 