#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易系统启动脚本
用于自动初始化和运行股票交易进程
"""

import sys
import time
import signal
import os
import yaml
from trade_manager import TradeManager
from stock_data_manager import format_stock_code_with_name
from datetime import datetime
import logging

def setup_start_trading_logger():
    """设置启动脚本日志"""
    try:
        # 加载配置
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        log_config = config.get('logging', {})
    except Exception:
        # 如果配置文件不存在或有问题，使用默认配置
        log_config = {
            'level': 'INFO',
            'log_dir': 'logs',
            'file_level': 'DEBUG',
            'console_level': 'INFO',
            'file_format': '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            'console_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        }
    
    logger = logging.getLogger("StartTrading")
    logger.setLevel(getattr(logging, log_config.get('level', 'INFO')))
    
    # 清除已有的handlers，避免重复
    if logger.handlers:
        logger.handlers.clear()
    
    # 确保日志目录存在
    log_dir = log_config.get('log_dir', 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 添加文件处理器
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f'{log_dir}/start_trading_{timestamp}.log'
    fh = logging.FileHandler(log_filename, encoding='utf-8')
    fh.setLevel(getattr(logging, log_config.get('file_level', 'DEBUG')))
    
    # 添加控制台处理器
    ch = logging.StreamHandler()
    ch.setLevel(getattr(logging, log_config.get('console_level', 'INFO')))
    
    # 创建formatter
    file_format = log_config.get('file_format', 
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s')
    console_format = log_config.get('console_format', 
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    file_formatter = logging.Formatter(file_format, datefmt='%Y-%m-%d %H:%M:%S')
    console_formatter = logging.Formatter(console_format, datefmt='%Y-%m-%d %H:%M:%S')
    
    fh.setFormatter(file_formatter)
    ch.setFormatter(console_formatter)
    
    # 添加handlers
    logger.addHandler(fh)
    logger.addHandler(ch)
    
    # 防止日志向上传播，避免重复输出
    logger.propagate = False
    
    return logger

class TradingSystem:
    def __init__(self):
        """初始化交易系统"""
        self.manager = TradeManager()
        self.running = False
        self.logger = logging.getLogger("StartTrading")
        
        # 设置信号处理器，用于优雅退出
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.logger.info("交易系统初始化完成")
        
    def _signal_handler(self, signum, frame):
        """信号处理器，用于优雅退出"""
        self.logger.info(f"收到退出信号 {signum}")
        print(f"\n收到退出信号 {signum}，正在停止交易系统...")
        self.stop()
        
    def start(self):
        """启动交易系统"""
        self.logger.info("开始启动交易系统")
        print("=" * 60)
        print("[启动] 股票交易系统")
        print("=" * 60)
        
        # 从配置文件初始化
        self.logger.info("从配置文件初始化交易进程")
        success = self.manager.init_from_config()
        if not success:
            self.logger.error("初始化失败")
            print("[失败] 初始化失败，退出程序")
            return False
            
        self.running = True
        active_stocks = self.manager.get_active_stocks()
        active_formatted = [format_stock_code_with_name(stock) for stock in active_stocks]
        
        self.logger.info(f"交易系统启动成功，活跃股票: {', '.join(active_formatted)}")
        print(f"[成功] 交易系统启动成功！")
        print(f"[信息] 当前交易股票: {', '.join(active_formatted)}")
        print(f"[时间] 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        return True
        
    def monitor(self):
        """监控交易进程状态"""
        if not self.running:
            self.logger.warning("系统未运行，无法开始监控")
            return
            
        self.logger.info("开始监控交易进程状态")
        print("[监控] 开始监控交易进程状态...")
        print("按 Ctrl+C 停止交易系统")
        print("-" * 60)
        
        try:
            while self.running:
                # 检查所有股票的状态
                active_stocks = self.manager.get_active_stocks()
                
                if not active_stocks:
                    self.logger.warning("没有活跃的交易进程")
                    print("[警告] 没有活跃的交易进程")
                    break
                
                # 每30秒检查一次状态
                for i in range(30):
                    if not self.running:
                        break
                    time.sleep(1)
                
                # 显示状态信息
                current_time = datetime.now().strftime('%H:%M:%S')
                self.logger.debug(f"监控状态检查: 活跃股票 {len(active_stocks)} 只")
                print(f"[{current_time}] 监控中... 活跃股票: {len(active_stocks)} 只")
                
                # 检查进程健康状态
                unhealthy_stocks = []
                for stock_code in active_stocks:
                    status = self.manager.get_stock_status(stock_code)
                    if not status['data_process_alive'] or not status['trading_process_alive']:
                        unhealthy_stocks.append(stock_code)
                
                if unhealthy_stocks:
                    unhealthy_formatted = [format_stock_code_with_name(stock) for stock in unhealthy_stocks]
                    self.logger.warning(f"发现异常进程: {', '.join(unhealthy_formatted)}")
                    print(f"[警告] 发现异常进程: {', '.join(unhealthy_formatted)}")
                    # 这里可以添加自动重启逻辑
                    
        except KeyboardInterrupt:
            self.logger.info("收到键盘中断信号")
            print("\n收到中断信号...")
        except Exception as e:
            self.logger.error(f"监控过程中发生错误: {str(e)}", exc_info=True)
            print(f"[错误] 监控过程中发生错误: {str(e)}")
            
    def stop(self):
        """停止交易系统"""
        if not self.running:
            self.logger.info("系统未运行，无需停止")
            return
            
        self.logger.info("开始停止交易系统")
        print("\n[停止] 正在停止交易系统...")
        self.running = False
        
        # 获取当前活跃股票
        active_stocks = self.manager.get_active_stocks()
        if active_stocks:
            active_formatted = [format_stock_code_with_name(stock) for stock in active_stocks]
            self.logger.info(f"停止 {len(active_stocks)} 只股票的交易进程: {', '.join(active_formatted)}")
            print(f"[信息] 停止 {len(active_stocks)} 只股票的交易进程...")
            
        # 清理所有资源
        self.manager.cleanup()
        
        self.logger.info("交易系统已安全停止")
        print("[完成] 交易系统已安全停止")
        print(f"[时间] 停止时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    def restart_stock(self, stock_code):
        """重启指定股票的交易进程"""
        formatted_code = format_stock_code_with_name(stock_code)
        print(f"[重启] 重启股票 {formatted_code} 的交易进程...")
        
        # 先停止
        self.manager.remove_stock(stock_code)
        time.sleep(2)  # 等待进程完全停止
        
        # 再启动
        success = self.manager.add_stock(stock_code)
        if success:
            print(f"[成功] 股票 {formatted_code} 重启成功")
        else:
            print(f"[失败] 股票 {formatted_code} 重启失败")
            
        return success

def main():
    """主函数"""
    # 设置日志
    logger = setup_start_trading_logger()
    logger.info("启动交易系统脚本")
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == '--help' or command == '-h':
            print("用法:")
            print("  python start_trading.py          # 启动交易系统")
            print("  python start_trading.py --help   # 显示帮助信息")
            logger.info("显示帮助信息后退出")
            return
    
    # 创建交易系统实例
    logger.info("创建交易系统实例")
    trading_system = TradingSystem()
    
    try:
        # 启动系统
        logger.info("尝试启动交易系统")
        if trading_system.start():
            logger.info("交易系统启动成功，开始监控")
            # 开始监控
            trading_system.monitor()
        else:
            logger.error("交易系统启动失败")
        
    except Exception as e:
        logger.error(f"系统运行出错: {str(e)}", exc_info=True)
        print(f"[错误] 系统运行出错: {str(e)}")
        
    finally:
        # 确保系统正确停止
        logger.info("确保系统正确停止")
        trading_system.stop()
        logger.info("交易系统脚本退出")

if __name__ == '__main__':
    main() 