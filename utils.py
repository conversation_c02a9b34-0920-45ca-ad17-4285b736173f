import requests
import logging
import os
import datetime
from stock_data_manager import format_stock_code_with_name

# 配置基础日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("Utils")

class WechatNotifier:
    """企业微信通知工具类"""
    
    # 企业微信机器人Webhook地址
    WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=79d8e46b-d817-4342-bf5c-61efcbdc3182"
    
    @staticmethod
    def send_notification(content):
        """发送企业微信通知
        
        Args:
            content: 通知内容，支持markdown格式
        
        Returns:
            bool: 发送是否成功
        """
        try:
            headers = {"Content-Type": "application/json"}
            data = {
                "msgtype": "markdown",
                "markdown": {
                    "content": content
                }
            }
            response = requests.post(WechatNotifier.WEBHOOK_URL, json=data, headers=headers)
            logger.info(f"企业微信消息发送状态: {response.status_code}")
            return response.status_code == 200
        except Exception as e:
            logger.error(f"发送企业微信通知失败: {str(e)}")
            return False
    
    @staticmethod
    def send_trade_notification(trade_type, stock_code, price, quantity, value, commission=0, 
                               buy_price=None, pnl=None, cash=None, total_value=None, order_status=None):
        """发送交易通知
        
        Args:
            trade_type: 交易类型，'BUY'或'SELL'
            stock_code: 股票代码
            price: 交易价格
            quantity: 交易数量
            value: 交易金额
            commission: 手续费
            buy_price: 买入价格(卖出时使用)
            pnl: 收益金额(卖出时使用)
            cash: 可用资金
            total_value: 总资产
            order_status: 订单状态，如'已成交'、'部分成交'等
            
        Returns:
            bool: 发送是否成功
        """
        if trade_type.upper() == 'BUY':
            content = f"### 🔴 买入交易通知\n\n"
            content += f"**股票代码**: {format_stock_code_with_name(stock_code)}\n\n"
            
            # 添加订单状态
            if order_status is not None:
                content += f"**订单状态**: {order_status}\n\n"
            
            content += f"**买入价格**: {price:.4f}\n\n"
            content += f"**买入数量**: {quantity}\n\n"
            content += f"**买入金额**: {value:.2f}\n\n"
            content += f"**手续费**: {commission:.2f}\n\n"
            
            if cash is not None:
                content += f"**可用资金**: {cash:.2f}\n\n"
            
            if total_value is not None:
                content += f"**总资产**: {total_value:.2f}"
        
        elif trade_type.upper() == 'SELL':
            content = f"### 🟢 卖出交易通知\n\n"
            content += f"**股票代码**: {format_stock_code_with_name(stock_code)}\n\n"
            
            # 添加订单状态
            if order_status is not None:
                content += f"**订单状态**: {order_status}\n\n"
            
            content += f"**卖出价格**: {price:.4f}\n\n"
            content += f"**卖出数量**: {quantity}\n\n"
            content += f"**卖出金额**: {value:.2f}\n\n"
            
            if buy_price is not None:
                content += f"**买入价格**: {buy_price:.4f}\n\n"
                pnl_percent = pnl / (buy_price * abs(quantity)) * 100
                content += f"**收益率**: {pnl_percent:.2f}%\n\n"
            
            if pnl is not None:
                content += f"**净收益**: {float(pnl):.2f}\n\n"
            
            if cash is not None:
                content += f"**可用资金**: {cash:.2f}\n\n"
            
            if total_value is not None:
                content += f"**总资产**: {total_value:.2f}"
        
        else:
            logger.error(f"未知的交易类型: {trade_type}")
            return False
        
        return WechatNotifier.send_notification(content)
    
    @staticmethod
    def send_backtest_notification(backtest_type, date_range=None, success_count=None, total_count=None, 
                                  results_dir=None, stats=None, elapsed_time=None):
        """发送回测完成通知
        
        Args:
            backtest_type: 回测类型，'single'或'batch'
            date_range: 日期范围，可以是单个日期或开始-结束日期元组
            success_count: 成功处理的天数
            total_count: 总天数
            results_dir: 结果保存目录
            stats: 统计数据字典
            elapsed_time: 回测耗时(秒)
            
        Returns:
            bool: 发送是否成功
        """
        if backtest_type.lower() == 'single':
            content = f"### 单日回测完成通知 📊\n\n"
            content += f"**回测日期**: {date_range}\n\n"
        else:  # batch
            content = f"### 批量回测完成通知 📈\n\n"
            if isinstance(date_range, tuple) and len(date_range) == 2:
                content += f"**回测日期范围**: {date_range[0]} 至 {date_range[1]}\n\n"
            
            if success_count is not None and total_count is not None:
                content += f"**成功处理天数**: {success_count}/{total_count}\n\n"
        
        # 添加回测用时
        if elapsed_time is not None:
            if elapsed_time < 60:
                time_str = f"{elapsed_time:.2f}秒"
            elif elapsed_time < 3600:
                minutes = int(elapsed_time // 60)
                seconds = elapsed_time % 60
                time_str = f"{minutes}分{seconds:.2f}秒"
            else:
                hours = int(elapsed_time // 3600)
                minutes = int((elapsed_time % 3600) // 60)
                seconds = elapsed_time % 60
                time_str = f"{hours}小时{minutes}分{seconds:.2f}秒"
                
            content += f"**回测用时**: {time_str}\n\n"
        
        if results_dir:
            content += f"**结果保存目录**: {results_dir}\n\n"
        
        if stats:
            content += "**回测统计**:\n"
            for key, value in stats.items():
                if isinstance(value, float):
                    content += f"- {key}: {value:.2f}\n"
                else:
                    content += f"- {key}: {value}\n"
        
        return WechatNotifier.send_notification(content)
    
    @staticmethod
    def send_stock_selection_notification(stock_codes, is_live=False, target_date=None):
        """发送选股结果通知
        
        Args:
            stock_codes: 选出的股票代码列表
            is_live: 是否为实盘模式
            target_date: 目标日期，格式为YYYYMMDD，不提供则使用当前时间
            
        Returns:
            bool: 发送是否成功
        """
        if is_live:
            content = f"### 实盘选股通知 📊\n\n"
        else:
            content = f"### 回测选股通知 📋\n\n"
        
        # 使用目标日期或当前时间
        if target_date:
            try:
                # 将YYYYMMDD格式转换为日期显示
                date_obj = datetime.datetime.strptime(target_date, '%Y%m%d')
                date_str = date_obj.strftime('%Y-%m-%d')
                content += f"**选股日期**: {date_str}\n\n"
            except:
                # 如果转换失败，直接使用原始字符串
                content += f"**选股日期**: {target_date}\n\n"
        else:
            # 使用当前时间
            content += f"**选股时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        content += f"**选出股票列表**:\n"
        
        for code in stock_codes:
            content += f"- {format_stock_code_with_name(code)}\n"
        
        content += f"\n**总计**: {len(stock_codes)}只股票"
        
        return WechatNotifier.send_notification(content)
    
    @staticmethod
    def send_strategy_summary(stock_code, start_cash, end_value, trade_count, win_count, loss_count):
        """发送策略汇总通知
        
        Args:
            stock_code: 股票代码
            start_cash: 初始资金
            end_value: 最终资产
            trade_count: 交易次数
            win_count: 盈利次数
            loss_count: 亏损次数
            
        Returns:
            bool: 发送是否成功
        """
        content = f"### 📉 策略结束通知\n\n"
        content += f"**股票代码**: {format_stock_code_with_name(stock_code)}\n\n"
        content += f"**结束时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        content += f"**期初资金**: {start_cash:.2f}\n\n"
        content += f"**期末资金**: {end_value:.2f}\n\n"
        
        total_return = (end_value / start_cash - 1) * 100
        content += f"**总收益率**: {total_return:.2f}%\n\n"
        
        content += f"**总交易次数**: {trade_count}\n\n"
        content += f"**盈利次数**: {win_count}\n\n"
        content += f"**亏损次数**: {loss_count}\n\n"
        
        if trade_count > 0:
            win_rate = win_count / trade_count * 100
            content += f"**胜率**: {win_rate:.2f}%"
        
        return WechatNotifier.send_notification(content)


class FileUtils:
    """文件操作工具类"""
    
    @staticmethod
    def ensure_dir_exists(directory):
        """确保目录存在，如果不存在则创建
        
        Args:
            directory: 目录路径
            
        Returns:
            bool: 目录是否存在或创建成功
        """
        try:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"创建目录: {directory}")
            return True
        except Exception as e:
            logger.error(f"创建目录 {directory} 失败: {str(e)}")
            return False
    
    @staticmethod
    def get_latest_file(directory, pattern=None, count=1):
        """获取目录中最新的文件
        
        Args:
            directory: 目录路径
            pattern: 文件名匹配模式，如*.csv
            count: 返回的文件数量
            
        Returns:
            list: 最新文件的路径列表
        """
        try:
            files = []
            for filename in os.listdir(directory):
                if pattern is None or (pattern and filename.endswith(pattern)):
                    filepath = os.path.join(directory, filename)
                    if os.path.isfile(filepath):
                        files.append((filepath, os.path.getctime(filepath)))
            
            # 按创建时间排序
            files.sort(key=lambda x: x[1], reverse=True)
            
            # 返回指定数量的最新文件
            return [f[0] for f in files[:count]]
        except Exception as e:
            logger.error(f"获取目录 {directory} 中的最新文件失败: {str(e)}")
            return []


class DateUtils:
    """日期工具类"""
    
    @staticmethod
    def is_trading_day(date_str):
        """检查是否为交易日(简化版，仅供参考)
        
        Args:
            date_str: 日期字符串，格式为YYYYMMDD
            
        Returns:
            bool: 是否为交易日
        """
        try:
            # 将日期字符串转换为datetime对象
            date = datetime.datetime.strptime(date_str, "%Y%m%d")
            
            # 周末不是交易日
            if date.weekday() >= 5:  # 5是周六，6是周日
                return False
            
            # 这里可以添加节假日判断逻辑
            # ...
            
            return True
        except Exception as e:
            logger.error(f"检查日期 {date_str} 是否为交易日时出错: {str(e)}")
            return False
    
    @staticmethod
    def get_previous_trading_day(date_str, days=1):
        """获取指定日期前N个交易日的日期
        
        Args:
            date_str: 日期字符串，格式为YYYYMMDD
            days: 前推的交易日数量
            
        Returns:
            str: 前N个交易日的日期字符串，格式为YYYYMMDD
        """
        try:
            # 将日期字符串转换为datetime对象
            date = datetime.datetime.strptime(date_str, "%Y%m%d")
            
            trading_days_found = 0
            while trading_days_found < days:
                date = date - datetime.timedelta(days=1)
                if DateUtils.is_trading_day(date.strftime("%Y%m%d")):
                    trading_days_found += 1
            
            return date.strftime("%Y%m%d")
        except Exception as e:
            logger.error(f"获取日期 {date_str} 前 {days} 个交易日时出错: {str(e)}")
            return date_str


class ConfigUtils:
    """配置工具类"""
    
    @staticmethod
    def load_yaml_config(config_file):
        """加载YAML配置文件
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            dict: 配置字典，加载失败则返回空字典
        """
        try:
            import yaml
            with open(config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件 {config_file} 失败: {str(e)}")
            return {}
    
    @staticmethod
    def save_yaml_config(config, config_file):
        """保存配置到YAML文件
        
        Args:
            config: 配置字典
            config_file: 配置文件路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            import yaml
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            logger.info(f"配置已保存到 {config_file}")
            return True
        except Exception as e:
            logger.error(f"保存配置到 {config_file} 失败: {str(e)}")
            return False 