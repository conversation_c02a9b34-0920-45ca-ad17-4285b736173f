# 量化交易系统配置管理说明

本文档介绍了量化交易系统的配置管理机制，系统使用SQLite数据库进行统一配置管理，同时保持与YAML配置文件的兼容性。

## 1. 配置系统架构

系统采用多层配置架构：

1. **SQLite数据库**：核心配置存储位置，位于`quant_app/data/system.db`
2. **YAML配置文件**：用于初始化和备份，位于项目根目录的`config.yaml`
3. **Web界面**：通过系统设置页面可视化管理配置

配置数据在SQLite数据库和YAML文件之间自动同步，确保一致性。

## 2. 配置管理组件

系统包含以下配置管理组件：

- **`db_config_manager.py`**：基于SQLite的配置管理器，提供底层API
- **`config_manager.py`**：向上兼容的配置管理接口，内部使用db_config_manager
- **`import_config.py`**：用于配置导入导出的工具脚本

## 3. 配置分类

配置项按功能分为以下几类：

1. **交易接口 (qmt)**
   - 账户设置
   - 连接参数
   - 交易模式

2. **风险管理 (risk)**
   - 止盈止损设置
   - 仓位控制
   - 交易频率限制

3. **回测设置 (backtest)**
   - 初始资金
   - 交易成本
   - 回测周期
   - 数据设置

4. **系统参数 (system)**
   - 日志设置
   - 界面配置
   - 性能选项

## 4. Web界面配置

通过系统设置页面（`http://localhost:8501/⚙️_系统设置`）可以访问配置管理界面：

- **交易接口**：设置QMT连接参数和账户信息
- **风险管理**：设置风控参数和交易限制
- **回测设置**：设置回测和模拟交易参数
- **系统参数**：设置通用系统参数
- **配置管理**：导入/导出/重置配置

## 5. 配置同步机制

配置数据在各组件间的同步流程：

1. **启动时**：
   - 检查SQLite数据库是否存在
   - 如果数据库为空，从YAML文件导入初始配置
   - 如果YAML文件不存在，使用默认配置

2. **配置修改**：
   - Web界面修改配置 → 写入SQLite数据库
   - 可以手动触发导出到YAML文件（备份）

3. **配置导出**：
   - 使用`python -m quant_app.core.import_config --export`导出到YAML

## 6. 编程接口

在Python代码中使用配置的方法：

```python
# 方法1：使用db_config_manager（推荐）
from quant_app.core.db_config_manager import db_config_manager

# 获取配置
value = db_config_manager.get_config('qmt', 'account_id', 'default_value')

# 设置配置
db_config_manager.set_config('risk', 'stop_loss_ratio', 0.05)

# 获取整个配置段
risk_settings = db_config_manager.get_section('risk')

# 方法2：使用向后兼容的config_manager
from quant_app.core.config_manager import config_manager

# 旧的API方式
value = config_manager.get_config('account_id', 'default_value')
config_manager.set_config('stop_loss_ratio', 0.05)
```

## 7. 命令行工具

系统提供以下命令行工具管理配置：

```bash
# 从YAML导入配置到数据库
python -m quant_app.core.import_config --yaml config.yaml

# 从数据库导出配置到YAML
python -m quant_app.core.import_config --export

# 清空数据库中现有配置并导入
python -m quant_app.core.import_config --yaml config.yaml --clear

# 显示当前配置
python -m quant_app.core.import_config --show
```

## 8. 配置备份和恢复

配置备份方法：

1. **自动备份**：每次执行导出操作，系统会自动备份原YAML文件
2. **手动备份**：从Web界面导出配置ZIP文件
3. **数据库备份**：直接复制`quant_app/data/system.db`文件

配置恢复方法：

1. **从YAML恢复**：`python -m quant_app.core.import_config --yaml backup.yaml --clear`
2. **从Web界面**：在配置管理页面上传备份的YAML或ZIP文件

## 9. 故障排除

常见问题：

1. **配置不生效**：确保使用正确的配置API，检查键名是否正确
2. **配置丢失**：检查数据库文件权限，确保数据库未损坏
3. **同步失败**：手动执行导入导出命令，检查错误信息

数据库修复：

```bash
# 如果数据库损坏，可以从YAML重建
python -m quant_app.core.import_config --yaml config.yaml --rebuild
```

## 10. 最佳实践

配置管理建议：

1. **定期备份**：定期导出配置到YAML文件
2. **分环境配置**：为不同环境（开发、测试、生产）创建不同配置
3. **配置验证**：修改关键配置后进行验证测试
4. **使用Web界面**：优先通过Web界面修改配置，确保格式正确
5. **保持简洁**：避免在配置中存储大量数据或复杂结构 