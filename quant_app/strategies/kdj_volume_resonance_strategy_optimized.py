import backtrader as bt
from datetime import time, datetime, timedelta
import numpy as np
import logging
import os
import yaml
import requests
from utils import WechatNotifier
from stock_data_manager import format_stock_code_with_name

def load_config():
    """加载配置文件"""
    with open('config.yaml', 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

# 加载配置
CONFIG = load_config()

# 配置日志
def setup_strategy_logger(stock_code=None):
    """配置策略日志系统"""
    # 创建logs目录（如果不存在）
    if not os.path.exists(CONFIG['logging']['log_dir']):
        os.makedirs(CONFIG['logging']['log_dir'])
    
    # 日志文件名包含当前日期时间和股票代码
    # 使用与RSI策略一致的命名格式：strategy_{stock_code}_{timestamp}.log
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if stock_code and isinstance(stock_code, str):
        log_filename = f'{CONFIG["logging"]["log_dir"]}/strategy_{stock_code}_{timestamp}.log'
        logger_name = f'strategy_{stock_code}'
    else:
        log_filename = f'{CONFIG["logging"]["log_dir"]}/strategy_{timestamp}.log'
        logger_name = 'strategy'
    
    # 配置logger
    logger = logging.getLogger(logger_name)
    logger.setLevel(getattr(logging, CONFIG['logging']['level']))
    
    # 清除已有的handlers
    if logger.handlers:
        logger.handlers.clear()
    
    # 添加文件处理器
    fh = logging.FileHandler(log_filename, encoding='utf-8')
    # 临时将文件日志级别设为DEBUG，以便诊断问题
    fh.setLevel(logging.DEBUG)
    
    # 添加控制台处理器
    ch = logging.StreamHandler()
    ch.setLevel(getattr(logging, CONFIG['logging']['console_level']))
    
    # 创建formatter
    file_formatter = logging.Formatter(CONFIG['logging']['file_format'])
    console_formatter = logging.Formatter(CONFIG['logging']['console_format'])
    fh.setFormatter(file_formatter)
    ch.setFormatter(console_formatter)
    
    # 添加handlers
    logger.addHandler(fh)
    logger.addHandler(ch)
    
    return logger


class OptimizedKDJ(bt.Indicator):
    """
    优化的KDJ指标实现 - 修复计算问题，增加稳定性
    """
    lines = ('k', 'd', 'j', 'rsv')
    params = (
        ('period', 9),
        ('k_period', 3),
        ('d_period', 3),
    )

    def __init__(self):
        super(OptimizedKDJ, self).__init__()
        # 设置最小周期
        self.addminperiod(self.p.period + max(self.p.k_period, self.p.d_period))
        
        # 计算RSV (Raw Stochastic Value)
        self.highest = bt.indicators.Highest(self.data.high, period=self.p.period)
        self.lowest = bt.indicators.Lowest(self.data.low, period=self.p.period)
        
        # 获取策略实例的logger
        try:
            self.logger = logging.getLogger(f'KDJ_Optimized_Strategy_{self.data._dataname}')
        except:
            self.logger = logging.getLogger('KDJ_Optimized_Strategy')
        
        # 初始化K和D值
        self.k_values = []
        self.d_values = []
        
        self.logger.debug(f"优化KDJ指标初始化: period={self.p.period}, k_period={self.p.k_period}, d_period={self.p.d_period}")

    def next(self):
        try:
            # 计算RSV
            high_val = self.highest[0]
            low_val = self.lowest[0]
            close_val = self.data.close[0]
            
            # 避免除零错误
            if abs(high_val - low_val) < 1e-8:
                rsv = 50.0  # 当高低价相等时，设为中性值
            else:
                rsv = (close_val - low_val) / (high_val - low_val) * 100
            
            # 确保RSV在合理范围内
            rsv = max(0, min(100, rsv))
            self.lines.rsv[0] = rsv
            
            # 计算K值 (使用EMA平滑)
            if len(self.k_values) == 0:
                # 初始化K值
                k_val = rsv
            else:
                # K = (2/3) * K_prev + (1/3) * RSV
                k_val = (2.0 / 3.0) * self.k_values[-1] + (1.0 / 3.0) * rsv
            
            # 确保K值在合理范围内
            k_val = max(0, min(100, k_val))
            self.lines.k[0] = k_val
            self.k_values.append(k_val)
            
            # 保持合理的历史长度
            if len(self.k_values) > 100:
                self.k_values = self.k_values[-50:]
            
            # 计算D值 (K值的EMA平滑)
            if len(self.d_values) == 0:
                # 初始化D值
                d_val = k_val
            else:
                # D = (2/3) * D_prev + (1/3) * K
                d_val = (2.0 / 3.0) * self.d_values[-1] + (1.0 / 3.0) * k_val
            
            # 确保D值在合理范围内
            d_val = max(0, min(100, d_val))
            self.lines.d[0] = d_val
            self.d_values.append(d_val)
            
            # 保持合理的历史长度
            if len(self.d_values) > 100:
                self.d_values = self.d_values[-50:]
            
            # 计算J值
            j_val = 3 * k_val - 2 * d_val
            # J值可以超出0-100范围，但限制在合理范围内
            j_val = max(-50, min(150, j_val))
            self.lines.j[0] = j_val
            
            # 每50个周期输出一次调试信息
            if len(self) % 50 == 0:
                self.logger.debug(f"KDJ计算: RSV={rsv:.2f}, K={k_val:.2f}, D={d_val:.2f}, J={j_val:.2f}")
            
        except Exception as e:
            self.logger.error(f"KDJ计算错误: {str(e)}")
            # 设置安全的默认值
            self.lines.rsv[0] = 50.0
            self.lines.k[0] = 50.0
            self.lines.d[0] = 50.0
            self.lines.j[0] = 50.0


class EnhancedVolumeAnalysis(bt.Indicator):
    """
    增强的成交量分析指标
    """
    lines = ('vol_ma', 'vol_ratio', 'vol_trend', 'vol_strength')
    params = dict(
        period=10,  # 增加周期以获得更稳定的均值
        trend_period=5  # 成交量趋势周期
    )

    def __init__(self):
        super(EnhancedVolumeAnalysis, self).__init__()
        self.lines.vol_ma = bt.ind.SMA(self.data.volume, period=self.p.period)
        self.lines.vol_ratio = bt.DivByZero(self.data.volume, self.lines.vol_ma, zero=1.0)
        
        # 成交量趋势 (短期均量 vs 长期均量)
        self.vol_ma_short = bt.ind.SMA(self.data.volume, period=self.p.trend_period)
        self.lines.vol_trend = bt.DivByZero(self.vol_ma_short, self.lines.vol_ma, zero=1.0)
        
        # 成交量强度 (当前量相对于最近最大量的比例)
        self.vol_max = bt.ind.Highest(self.data.volume, period=self.p.period)
        self.lines.vol_strength = bt.DivByZero(self.data.volume, self.vol_max, zero=0.0)
        
        # 获取策略实例的logger
        try:
            self.logger = logging.getLogger(f'KDJ_Optimized_Strategy_{self.data._dataname}')
        except:
            self.logger = logging.getLogger('KDJ_Optimized_Strategy')

    def next(self):
        try:
            if len(self) >= self.p.period:
                # 每100个周期输出一次调试信息
                if len(self) % 100 == 0:
                    self.logger.debug(f"成交量分析: 当前量={self.data.volume[0]}, 均量={self.lines.vol_ma[0]:.0f}, "
                                    f"比率={self.lines.vol_ratio[0]:.2f}, 趋势={self.lines.vol_trend[0]:.2f}, "
                                    f"强度={self.lines.vol_strength[0]:.2f}")
        except Exception as e:
            self.logger.error(f"成交量分析错误: {str(e)}")


class TrendFilter(bt.Indicator):
    """
    趋势过滤器 - 用于判断市场趋势
    """
    lines = ('trend_signal', 'trend_strength')
    params = dict(
        fast_period=5,
        slow_period=20,
        signal_period=10
    )

    def __init__(self):
        super(TrendFilter, self).__init__()
        
        # 快慢均线
        self.ma_fast = bt.ind.SMA(self.data.close, period=self.p.fast_period)
        self.ma_slow = bt.ind.SMA(self.data.close, period=self.p.slow_period)
        
        # 趋势信号 (1=上涨, 0=震荡, -1=下跌)
        self.ma_diff = self.ma_fast - self.ma_slow
        self.ma_diff_ma = bt.ind.SMA(self.ma_diff, period=self.p.signal_period)
        
        # 趋势强度
        self.lines.trend_strength = bt.DivByZero(abs(self.ma_diff), self.ma_slow, zero=0.0)

    def next(self):
        # 计算趋势信号
        if self.ma_fast[0] > self.ma_slow[0] and self.ma_diff_ma[0] > 0:
            self.lines.trend_signal[0] = 1  # 上涨趋势
        elif self.ma_fast[0] < self.ma_slow[0] and self.ma_diff_ma[0] < 0:
            self.lines.trend_signal[0] = -1  # 下跌趋势
        else:
            self.lines.trend_signal[0] = 0  # 震荡


class OptimizedKDJVolumeStrategy(bt.Strategy):
    """
    优化的KDJ+成交量共振策略
    """
    params = (
        # 基础参数
        ('kdj_period', 9),  # KDJ周期
        ('volume_ma_period', 10),  # 成交量均线周期
        
        # 信号参数
        ('j_oversold', 20),  # J值超卖线 (进一步放宽)
        ('j_overbought', 80),  # J值超买线 (进一步放宽)
        ('k_oversold', 30),  # K值超卖线 (放宽)
        ('k_overbought', 70),  # K值超买线 (放宽)
        ('volume_amplify_ratio', 1.1),  # 成交量放大倍数 (大幅降低要求)
        ('volume_shrink_ratio', 0.9),  # 成交量萎缩倍数
        
        # 趋势过滤
        ('use_trend_filter', False),  # 暂时关闭趋势过滤，增加交易机会
        ('min_trend_strength', 0.005),  # 降低最小趋势强度要求
        
        # 仓位管理
        ('initial_position_size', 0.1),  # 首单仓位10%
        ('max_position_size', 0.3),  # 最大仓位30%
        ('position_increment', 0.05),  # 仓位递增
        
        # 止盈止损
        ('profit_target_1', 0.02),  # 第一档止盈2%
        ('profit_target_2', 0.05),  # 第二档止盈5%
        ('profit_target_3', 0.08),  # 第三档止盈8%
        ('stop_loss', 0.02),  # 单笔止损2%
        ('trailing_stop', 0.015),  # 跟踪止损1.5%
        ('max_consecutive_losses', 3),  # 最大连续亏损次数
        
        # 交易时间
        ('trade_time_start', time.fromisoformat(CONFIG['strategy']['trade_time_start'])),
        ('trade_time_end', time.fromisoformat(CONFIG['strategy']['trade_time_end'])),
        ('clear_position_time', time.fromisoformat(CONFIG['strategy']['clear_position_time'])),
        
        # 其他参数
        ('commission', CONFIG['backtest']['commission']['commission']),
    )

    def __init__(self):
        """初始化策略"""
        # 获取股票代码
        self.stock_code = str(self.data._name) if hasattr(self.data, '_name') else None
        
        # 尝试获取外部已创建的策略日志，如果没有则创建新的
        strategy_logger_name = f'strategy_{self.stock_code}'
        self.logger = logging.getLogger(strategy_logger_name)
        
        # 如果外部没有配置logger，则使用内部函数创建
        if not self.logger.handlers:
            self.logger = setup_strategy_logger(self.stock_code)
        
        self.logger.info(f"初始化优化KDJ+成交量共振策略... 股票代码: {format_stock_code_with_name(self.stock_code)}")
        
        # 基础数据
        self.dataclose = self.datas[0].close
        self.datahigh = self.datas[0].high
        self.datalow = self.datas[0].low
        self.dataopen = self.datas[0].open
        self.datavolume = self.datas[0].volume
        
        # 订单和持仓跟踪
        self.order = None
        self.buyprice = None
        self.buycomm = 0.0
        self.has_position = False
        self.position_cost_line = 0
        self.highest_price = 0  # 持仓期间最高价，用于跟踪止损
        
        # 新增：买入信号跟踪
        self.buy_signal_reason = None  # 记录买入触发信号
        
        # 交易统计
        self.trade_count = 0
        self.win_count = 0
        self.loss_count = 0
        self.consecutive_losses = 0
        self.total_profit = 0
        self.total_loss = 0
        self.max_profit = 0
        self.max_loss = 0
        self.trade_history = []
        self.strategy_paused = False
        
        # 动态仓位管理
        self.current_position_size = self.p.initial_position_size
        
        # 添加计数器用于定期输出
        self.bar_count = 0
        
        # 配置文件中获取是否是实盘模式和实盘调试模式
        self.is_live = CONFIG['backtest'].get('is_live', False)
        self.is_live_debug = CONFIG['backtest'].get('is_live_debug', False) and self.is_live
        self.live_debug_size = CONFIG['backtest'].get('live_debug_size', 10)
        
        # 输出策略模式信息
        if self.is_live:
            if self.is_live_debug:
                self.logger.info(f"策略模式: 实盘调试模式 (固定仓位: {self.live_debug_size})")
            else:
                self.logger.info(f"策略模式: 实盘正式模式")
        else:
            self.logger.info(f"策略模式: 回测模式")
        
        # 初始化指标
        self._init_indicators()
        
        self.logger.info("优化KDJ+成交量共振策略初始化完成")

    def _init_indicators(self):
        """初始化技术指标"""
        self.logger.debug("初始化技术指标...")
        
        # 优化的KDJ指标
        self.kdj = OptimizedKDJ(self.data, period=self.p.kdj_period)
        
        # 增强的成交量分析
        self.volume_analysis = EnhancedVolumeAnalysis(self.data, period=self.p.volume_ma_period)
        
        # 趋势过滤器
        if self.p.use_trend_filter:
            self.trend_filter = TrendFilter(self.data)
        
        # ATR用于动态止损
        self.atr = bt.indicators.ATR(self.data, period=14)
        
        # 均线系统
        self.sma5 = bt.indicators.SMA(self.dataclose, period=5)
        self.sma10 = bt.indicators.SMA(self.dataclose, period=10)
        self.sma20 = bt.indicators.SMA(self.dataclose, period=20)
        
        # 价格通道 (用于支撑阻力判断)
        self.price_channel_high = bt.indicators.Highest(self.datahigh, period=20)
        self.price_channel_low = bt.indicators.Lowest(self.datalow, period=20)
        
        self.logger.debug("技术指标初始化完成")

    def log(self, txt, dt=None, level=logging.INFO):
        dt = dt or self.datas[0].datetime.datetime(0)
        msg = f'{dt.strftime("%Y-%m-%d %H:%M:%S")} - {txt}'
        if level == logging.DEBUG:
            self.logger.debug(msg)
        elif level == logging.INFO:
            self.logger.info(msg)
        elif level == logging.WARNING:
            self.logger.warning(msg)
        elif level == logging.ERROR:
            self.logger.error(msg)

    def log_trade_status(self):
        """记录当前持仓状态"""
        if self.has_position:
            current_value = self.pos.size * self.dataclose[0]
            cost_basis = self.pos.size * self.buyprice
            current_return = (self.dataclose[0] - self.buyprice) / self.buyprice * 100
            
            self.log(f'当前持仓状态:', level=logging.INFO)
            self.log(f'持仓数量: {self.pos.size}', level=logging.INFO)
            self.log(f'买入价格: {self.buyprice:.4f}', level=logging.INFO)
            self.log(f'当前价格: {self.dataclose[0]:.4f}', level=logging.INFO)
            self.log(f'最高价格: {self.highest_price:.4f}', level=logging.INFO)
            self.log(f'持仓市值: {current_value:.2f}', level=logging.INFO)
            self.log(f'持仓成本: {cost_basis:.2f}', level=logging.INFO)
            self.log(f'当前收益率: {current_return:.2f}%', level=logging.INFO)
            self.log(f'可用资金: {self.broker.getcash():.2f}', level=logging.INFO)
            self.log(f'总资产: {self.broker.getvalue():.2f}', level=logging.INFO)

    def log_trade_statistics(self):
        """记录交易统计信息"""
        if self.trade_count > 0:
            win_rate = self.win_count / self.trade_count * 100
            avg_profit = self.total_profit / self.win_count if self.win_count > 0 else 0
            avg_loss = self.total_loss / self.loss_count if self.loss_count > 0 else 0
            
            self.log(f'\n=== 交易统计 ===', level=logging.INFO)
            self.log(f'总交易次数: {self.trade_count}', level=logging.INFO)
            self.log(f'盈利次数: {self.win_count}', level=logging.INFO)
            self.log(f'亏损次数: {self.loss_count}', level=logging.INFO)
            self.log(f'连续亏损次数: {self.consecutive_losses}', level=logging.INFO)
            self.log(f'胜率: {win_rate:.2f}%', level=logging.INFO)
            self.log(f'平均盈利: {avg_profit:.2f}%', level=logging.INFO)
            self.log(f'平均亏损: {avg_loss:.2f}%', level=logging.INFO)
            self.log(f'最大盈利: {self.max_profit:.2f}%', level=logging.INFO)
            self.log(f'最大亏损: {self.max_loss:.2f}%', level=logging.INFO)
            self.log(f'当前仓位大小: {self.current_position_size:.1%}', level=logging.INFO)
            self.log(f'策略状态: {"暂停" if self.strategy_paused else "正常"}', level=logging.INFO)

    def get_order_status_text(self, order_status):
        """将订单状态转换为中文描述"""
        status_map = {
            0: '已创建', 1: '已提交', 2: '已接受', 3: '部分成交',
            4: '已成交', 5: '已取消', 6: '已过期', 7: '保证金不足', 8: '已拒绝'
        }
        return status_map.get(order_status, f'未知状态({order_status})')

    def notify_order(self, order):
        """订单状态更新通知"""
        data_name = order.data._name
        
        if order.status in [order.Submitted, order.Accepted]:
            return

        if order.status in [order.Completed]:
            order_type = 'BUY' if order.isbuy() else 'SELL'
            self.log(f'{order_type} 订单执行, 股票: {format_stock_code_with_name(data_name)}, 价格: {order.executed.price:.4f}, '
                   f'数量: {order.executed.size}, 成本: {order.executed.value:.2f}, '
                   f'手续费: {order.executed.comm:.2f}', level=logging.INFO)
            
            if order.isbuy():
                self.buyprice = order.executed.price
                self.buycomm = order.executed.comm
                self.position_cost_line = self.buyprice
                self.highest_price = self.buyprice  # 初始化最高价
                
                trade_record = {
                    'time': self.data.datetime.datetime(0),
                    'type': 'BUY',
                    'price': self.buyprice,
                    'size': order.executed.size,
                    'value': order.executed.value,
                    'commission': self.buycomm,
                    'return': 0.0,
                    'signal': self.buy_signal_reason if hasattr(self, 'buy_signal_reason') and self.buy_signal_reason else '未知信号'
                }
                self.trade_history.append(trade_record)
                
            else:  # 卖出
                if hasattr(self, 'buyprice') and self.buyprice > 0:
                    pnl_pct = (order.executed.price - self.buyprice) / self.buyprice
                    pnl_value = order.executed.size * (order.executed.price - self.buyprice)
                    buy_commission = self.buycomm if self.buycomm is not None else 0.0
                    net_pnl = pnl_value - order.executed.comm - buy_commission
                    
                    self.log(f'交易结束. 毛收益: {pnl_value:.2f} ({pnl_pct:.2%}), '
                           f'净收益: {net_pnl:.2f} ({net_pnl / (self.buyprice * order.executed.size):.2%})', 
                           level=logging.INFO)
                    
                    # 更新交易统计
                    self.trade_count += 1
                    if pnl_pct > 0:
                        self.win_count += 1
                        self.consecutive_losses = 0
                        self.total_profit += pnl_pct * 100
                        self.max_profit = max(self.max_profit, pnl_pct * 100)
                        
                        # 策略有效，适当增加仓位
                        if self.current_position_size < self.p.max_position_size:
                            self.current_position_size = min(self.p.max_position_size, 
                                                           self.current_position_size + self.p.position_increment)
                            self.log(f'策略有效，调整仓位至: {self.current_position_size:.1%}', level=logging.INFO)
                    else:
                        self.loss_count += 1
                        self.consecutive_losses += 1
                        self.total_loss += abs(pnl_pct * 100)
                        self.max_loss = max(self.max_loss, abs(pnl_pct * 100))
                        
                        # 策略失效，减少仓位
                        if self.current_position_size > self.p.initial_position_size:
                            self.current_position_size = max(self.p.initial_position_size,
                                                           self.current_position_size - self.p.position_increment)
                            self.log(f'策略失效，调整仓位至: {self.current_position_size:.1%}', level=logging.INFO)
                        
                        # 检查是否需要暂停策略
                        if (abs(pnl_pct) >= self.p.stop_loss or 
                            self.consecutive_losses >= self.p.max_consecutive_losses):
                            self.strategy_paused = True
                            self.log(f'触发暂停条件: 单笔亏损{abs(pnl_pct):.2%} 或连续亏损{self.consecutive_losses}次', 
                                   level=logging.WARNING)
                    
                    trade_record = {
                        'time': self.data.datetime.datetime(0),
                        'type': 'SELL',
                        'price': order.executed.price,
                        'size': order.executed.size,
                        'value': order.executed.value,
                        'commission': order.executed.comm,
                        'return': pnl_pct * 100,
                        'pnl': net_pnl
                    }
                    self.trade_history.append(trade_record)
                
                self.bar_executed = len(self)

        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log(f'订单被取消/保证金不足/拒绝, 原因: {order.status}', level=logging.WARNING)

        self.order = None

    def check_buy_signal(self):
        """检查买入信号"""
        try:
            # 基础条件检查
            if len(self) < max(self.p.kdj_period, self.p.volume_ma_period) + 10:
                return False, "数据不足"
            
            # 1. KDJ金叉信号 (K上穿D)
            kdj_golden_cross = (self.kdj.k[0] > self.kdj.d[0] and 
                               self.kdj.k[-1] <= self.kdj.d[-1])
            
            # 2. J值从超卖区回升 (放宽条件)
            j_oversold_recovery = (self.kdj.j[-1] < self.p.j_oversold and 
                                 self.kdj.j[0] > self.kdj.j[-1])
            
            # 3. K值在超卖区域
            k_oversold = self.kdj.k[0] < self.p.k_oversold
            
            # 4. 成交量放大
            volume_amplified = self.volume_analysis.vol_ratio[0] >= self.p.volume_amplify_ratio
            
            # 5. 成交量趋势向上
            volume_trend_up = self.volume_analysis.vol_trend[0] > 1.0
            
            # 简化买入信号 - 只要满足KDJ条件之一即可买入
            buy_signal = (kdj_golden_cross or j_oversold_recovery or k_oversold)
            
            if buy_signal:
                reasons = []
                if kdj_golden_cross:
                    reasons.append("KDJ金叉")
                if j_oversold_recovery:
                    reasons.append("J值回升")
                if k_oversold:
                    reasons.append("K值超卖")
                if volume_amplified:
                    reasons.append("成交量放大")
                if volume_trend_up:
                    reasons.append("成交量趋势向上")
                
                return True, ", ".join(reasons)
            
            return False, "无买入信号"
            
        except Exception as e:
            self.logger.error(f"检查买入信号时出错: {str(e)}")
            return False, f"检查买入信号出错: {str(e)}"

    def check_sell_signal(self):
        """检查卖出信号"""
        try:
            # 1. KDJ死叉信号 (K下穿D)
            kdj_death_cross = (self.kdj.k[0] < self.kdj.d[0] and 
                              self.kdj.k[-1] >= self.kdj.d[-1])
            
            # 2. J值从超买区回落
            j_overbought_fall = (self.kdj.j[-1] > self.p.j_overbought and 
                               self.kdj.j[0] < self.kdj.j[-1])
            
            # 3. K值在超买区域
            k_overbought = self.kdj.k[0] > self.p.k_overbought
            
            # 4. 成交量萎缩
            volume_shrinking = self.volume_analysis.vol_ratio[0] < self.p.volume_shrink_ratio
            
            # 5. 成交量趋势向下
            volume_trend_down = self.volume_analysis.vol_trend[0] < 0.9
            
            # 6. 趋势转弱
            trend_weak = False
            if self.p.use_trend_filter and hasattr(self, 'trend_filter'):
                trend_weak = (self.trend_filter.trend_signal[0] <= 0 or  # 非上涨趋势
                             self.trend_filter.trend_strength[0] < self.p.min_trend_strength)
            
            # 综合卖出信号
            sell_signal = (
                (kdj_death_cross or j_overbought_fall or k_overbought) and  # KDJ信号之一
                (volume_shrinking or volume_trend_down or trend_weak)  # 量能或趋势转弱
            )
            
            if sell_signal:
                reasons = []
                if kdj_death_cross:
                    reasons.append("KDJ死叉")
                if j_overbought_fall:
                    reasons.append("J值回落")
                if k_overbought:
                    reasons.append("K值超买")
                if volume_shrinking:
                    reasons.append("成交量萎缩")
                if volume_trend_down:
                    reasons.append("成交量趋势向下")
                if trend_weak:
                    reasons.append("趋势转弱")
                
                return True, ", ".join(reasons)
            
            return False, "无卖出信号"
            
        except Exception as e:
            self.logger.error(f"检查卖出信号时出错: {str(e)}")
            return False, f"检查卖出信号出错: {str(e)}"

    def next(self):
        # 检查是否有待处理订单
        if self.order:
            return

        # 检查策略是否暂停
        if self.strategy_paused:
            return

        # 获取当前时间
        current_time = self.data.datetime.time(0)
        
        # 增加计数器
        self.bar_count += 1
        
        # 获取当前持仓
        self.pos = self.getposition(self.data)
        self.has_position = self.pos.size > 0
        
        # 更新最高价 (用于跟踪止损)
        if self.has_position:
            self.highest_price = max(self.highest_price, self.dataclose[0])
        
        # 每100个bar输出一次指标状态
        if self.bar_count % 100 == 0:
            self.log(f'\n=== 优化KDJ指标状态报告 ===', level=logging.INFO)
            self.log(f'股票代码: {self.stock_code}', level=logging.INFO)
            self.log(f'当前时间: {self.data.datetime.datetime(0)}', level=logging.INFO)
            self.log(f'当前价格: {self.dataclose[0]:.4f}', level=logging.INFO)
            self.log(f'KDJ - K: {self.kdj.k[0]:.2f}, D: {self.kdj.d[0]:.2f}, J: {self.kdj.j[0]:.2f}, RSV: {self.kdj.rsv[0]:.2f}', level=logging.INFO)
            self.log(f'成交量分析 - 比率: {self.volume_analysis.vol_ratio[0]:.2f}, 趋势: {self.volume_analysis.vol_trend[0]:.2f}, 强度: {self.volume_analysis.vol_strength[0]:.2f}', level=logging.INFO)
            if self.p.use_trend_filter and hasattr(self, 'trend_filter'):
                self.log(f'趋势过滤 - 信号: {self.trend_filter.trend_signal[0]:.0f}, 强度: {self.trend_filter.trend_strength[0]:.4f}', level=logging.INFO)
            if self.has_position:
                self.log_trade_status()
            self.log(f'当前仓位大小: {self.current_position_size:.1%}', level=logging.INFO)
            self.log(f'策略状态: {"暂停" if self.strategy_paused else "正常"}', level=logging.INFO)
            self.log(f'===================\n', level=logging.INFO)
        
        # 每个交易日收盘前清仓
        if current_time >= self.p.clear_position_time:
            self.log_trade_statistics()
            if self.has_position:
                self.log(f'收盘前清仓, 数量: {self.pos.size}', level=logging.INFO)
                self.order = self.sell(size=self.pos.size, price=self.dataclose[0])
                return

        # 检查是否在交易时间内
        if not (self.p.trade_time_start <= current_time <= self.p.trade_time_end):
            return

        # 检查止盈止损条件
        if self.has_position:
            current_return = (self.dataclose[0] - self.buyprice) / self.buyprice
            
            # 添加持仓状态调试日志，每20个bar输出一次
            if self.bar_count % 20 == 0:
                self.log(f'持仓状态检查: 收益率={current_return*100:.2f}%, 价格={self.dataclose[0]:.4f}, 买入价={self.buyprice:.4f}', level=logging.DEBUG)
            
            # 1. 止损检查
            if current_return <= -self.p.stop_loss:
                self.log(f'[SIGNAL] 触发固定止损信号: 收益率 {current_return*100:.2f}%, 数量: {self.pos.size}', level=logging.INFO)
                self.order = self.sell(size=self.pos.size, price=self.dataclose[0])
                return
            
            # 2. 跟踪止损检查
            trailing_stop_price = self.highest_price * (1 - self.p.trailing_stop)
            if self.dataclose[0] <= trailing_stop_price and current_return > 0:
                self.log(f'[SIGNAL] 触发跟踪止损信号: 价格 {self.dataclose[0]:.4f}, 止损价: {trailing_stop_price:.4f}, 收益率: {current_return*100:.2f}%, 数量: {self.pos.size}', level=logging.INFO)
                self.order = self.sell(size=self.pos.size, price=self.dataclose[0])
                return
            
            # 3. 分档止盈
            if current_return >= self.p.profit_target_3:
                # 第三档止盈 - 全部卖出
                self.log(f'[SIGNAL] 触发第三档止盈信号: 收益率 {current_return*100:.2f}%, 数量: {self.pos.size}', level=logging.INFO)
                self.order = self.sell(size=self.pos.size, price=self.dataclose[0])
                return
            elif current_return >= self.p.profit_target_2 and self.pos.size >= 30:
                # 第二档止盈 - 卖出2/3
                sell_size = int(self.pos.size * 2 / 3)
                self.log(f'[SIGNAL] 触发第二档止盈信号: 收益率 {current_return*100:.2f}%, 卖出数量: {sell_size}', level=logging.INFO)
                self.order = self.sell(size=sell_size, price=self.dataclose[0])
                return
            elif current_return >= self.p.profit_target_1 and self.pos.size >= 20:
                # 第一档止盈 - 卖出1/3
                sell_size = int(self.pos.size / 3)
                self.log(f'[SIGNAL] 触发第一档止盈信号: 收益率 {current_return*100:.2f}%, 卖出数量: {sell_size}', level=logging.INFO)
                self.order = self.sell(size=sell_size, price=self.dataclose[0])
                return

            # 4. 技术信号卖出
            sell_signal, sell_reason = self.check_sell_signal()
            
            # 添加调试日志
            if self.bar_count % 20 == 0:
                self.log(f'卖出信号检查: 信号={sell_signal}, 原因={sell_reason}', level=logging.DEBUG)
            
            if sell_signal:
                estimated_pnl = self.pos.size * (self.dataclose[0] - self.buyprice)
                self.log(f'[SIGNAL] 触发技术卖出信号: {sell_reason}, 收益率: {current_return*100:.2f}%, 数量: {self.pos.size}, 估计收益: {estimated_pnl:.2f}', level=logging.INFO)
                self.order = self.sell(size=self.pos.size, price=self.dataclose[0])
                return

        # 买入条件检查
        if not self.has_position:
            buy_signal, buy_reason = self.check_buy_signal()
            
            # 添加调试日志
            self.log(f'买入信号检查: 信号={buy_signal}, 原因={buy_reason}', level=logging.DEBUG)
            
            if buy_signal:
                # 记录买入触发信号
                self.buy_signal_reason = buy_reason
                
                # 计算成交量分析数据
                vol_3_avg = sum([self.datavolume[-i] for i in range(min(3, len(self)))]) / min(3, len(self))
                vol_5_avg = sum([self.datavolume[-i] for i in range(min(5, len(self)))]) / min(5, len(self))
                vol_10_avg = sum([self.datavolume[-i] for i in range(min(10, len(self)))]) / min(10, len(self))
                
                # 整合所有指标信息到一条详细日志记录中
                indicators_detail = (
                    f"买入信号触发详情 - "
                    f"价格: {self.dataclose[0]:.4f} | "
                    f"成交量: 当前={self.datavolume[0]:,.0f}, 均线={self.volume_analysis.vol_ma[0]:,.0f}, "
                    f"比率={self.volume_analysis.vol_ratio[0]:.2f}, 趋势={self.volume_analysis.vol_trend[0]:.2f}, "
                    f"强度={self.volume_analysis.vol_strength[0]:.2f}, "
                    f"近3bar均量={vol_3_avg:,.0f}, 近5bar均量={vol_5_avg:,.0f}, 近10bar均量={vol_10_avg:,.0f} | "
                    f"KDJ: K={self.kdj.k[0]:.2f}, D={self.kdj.d[0]:.2f}, J={self.kdj.j[0]:.2f}, RSV={self.kdj.rsv[0]:.2f}, "
                    f"前值K={self.kdj.k[-1]:.2f}, 前值D={self.kdj.d[-1]:.2f}, 前值J={self.kdj.j[-1]:.2f} | "
                    f"均线系统: SMA5={self.sma5[0]:.4f}, SMA10={self.sma10[0]:.4f}, SMA20={self.sma20[0]:.4f} | "
                    f"价格通道: 高={self.price_channel_high[0]:.4f}, 低={self.price_channel_low[0]:.4f} | "
                    f"ATR: {self.atr[0]:.4f}"
                )
                
                # 计算仓位
                if self.is_live_debug:
                    size = self.live_debug_size
                    self.log(f'实盘调试模式 - 使用固定仓位: {size}', level=logging.INFO)
                else:
                    equity = self.broker.getvalue()
                    position_value = equity * self.current_position_size
                    size = int(position_value / (self.dataclose[0] * 10)) * 10
                    size = max(size, 10)
                
                if size > 0:
                    buy_value = size * self.dataclose[0]
                    # 确保买入信号日志被输出 - 使用与RSI策略一致的格式
                    self.log(f'[SIGNAL] 触发买入信号: {self.buy_signal_reason}， [SIGNAL_DETAIL] {indicators_detail}', level=logging.INFO)
                    self.log(f'创建买入订单, 价格: {self.dataclose[0]:.4f}, 数量: {size}, 金额: {buy_value:.2f}', 
                           level=logging.INFO)
                    
                    self.order = self.buy(size=size, price=self.dataclose[0])
                else:
                    self.log(f'买入信号触发但仓位计算为0，跳过买入', level=logging.WARNING)
            else:
                # 添加调试信息，每50个bar输出一次买入条件检查
                if self.bar_count % 50 == 0:
                    self.log(f'买入条件检查: {buy_reason}', level=logging.DEBUG)

    def stop(self):
        """策略结束时调用的方法"""
        self.log_trade_statistics()
        
        if hasattr(self, 'trade_history') and self.trade_history:
            self.log("\n=== 交易明细 ===", level=logging.INFO)
            for trade in self.trade_history:
                self.log(f"时间: {trade['time']}, 类型: {trade['type']}, "
                       f"价格: {trade['price']:.4f}, 数量: {trade['size']}, "
                       f"收益率: {trade['return']:.2f}%", level=logging.INFO)
        
        self.log("=== 优化KDJ+成交量共振策略结束 ===", level=logging.INFO) 