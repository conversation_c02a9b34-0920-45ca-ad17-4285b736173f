import backtrader as bt
from datetime import time, datetime, timedelta
import numpy as np
import logging
import os
import yaml
import requests
from utils import WechatNotifier
from stock_data_manager import format_stock_code_with_name

def load_config():
    """加载配置文件"""
    with open('config.yaml', 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

# 加载配置
CONFIG = load_config()

# 配置日志
def setup_strategy_logger(stock_code=None):
    """配置策略日志系统"""
    # 创建logs目录（如果不存在）
    if not os.path.exists(CONFIG['logging']['log_dir']):
        os.makedirs(CONFIG['logging']['log_dir'])
    
    # 日志文件名包含当前日期时间和股票代码
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if stock_code and isinstance(stock_code, str):
        log_filename = f'{CONFIG["logging"]["log_dir"]}/kdj_strategy_{stock_code}_{timestamp}.log'
        logger_name = f'KDJ_Volume_Strategy_{stock_code}'
    else:
        log_filename = f'{CONFIG["logging"]["log_dir"]}/kdj_strategy_{timestamp}.log'
        logger_name = 'KDJ_Volume_Strategy'
    
    # 配置logger
    logger = logging.getLogger(logger_name)
    logger.setLevel(getattr(logging, CONFIG['logging']['level']))
    
    # 清除已有的handlers
    if logger.handlers:
        logger.handlers.clear()
    
    # 添加文件处理器
    fh = logging.FileHandler(log_filename, encoding='utf-8')
    fh.setLevel(getattr(logging, CONFIG['logging']['file_level']))
    
    # 添加控制台处理器
    ch = logging.StreamHandler()
    ch.setLevel(getattr(logging, CONFIG['logging']['console_level']))
    
    # 创建formatter
    file_formatter = logging.Formatter(CONFIG['logging']['file_format'])
    console_formatter = logging.Formatter(CONFIG['logging']['console_format'])
    fh.setFormatter(file_formatter)
    ch.setFormatter(console_formatter)
    
    # 添加handlers
    logger.addHandler(fh)
    logger.addHandler(ch)
    
    return logger


class KDJ(bt.Indicator):
    """
    KDJ指标实现
    """
    lines = ('k', 'd', 'j')
    params = (
        ('period', 9),
        ('period_dfast', 3),
        ('period_dslow', 3),
    )

    def __init__(self):
        super(KDJ, self).__init__()
        self.addminperiod(self.p.period + max(self.p.period_dfast, self.p.period_dslow))
        
        # 计算RSV (Raw Stochastic Value)
        self.highest = bt.indicators.Highest(self.data.high, period=self.p.period)
        self.lowest = bt.indicators.Lowest(self.data.low, period=self.p.period)
        
        # 获取策略实例的logger
        self.logger = logging.getLogger(f'KDJ_Volume_Strategy_{self.data._dataname}')
        self.logger.debug(f"KDJ指标初始化: period={self.p.period}")

    def next(self):
        try:
            # 计算RSV
            high_low_diff = self.highest[0] - self.lowest[0]
            if high_low_diff > 0.0000001:  # 避免除零
                rsv = (self.data.close[0] - self.lowest[0]) / high_low_diff * 100
            else:
                rsv = 50.0  # 默认值
            
            # 计算K值 (使用简单移动平均)
            if len(self) == self.p.period:
                self.lines.k[0] = rsv  # 初始化
            else:
                self.lines.k[0] = (2 * self.lines.k[-1] + rsv) / 3
            
            # 计算D值 (K值的移动平均)
            if len(self) == self.p.period:
                self.lines.d[0] = self.lines.k[0]  # 初始化
            else:
                self.lines.d[0] = (2 * self.lines.d[-1] + self.lines.k[0]) / 3
            
            # 计算J值
            self.lines.j[0] = 3 * self.lines.k[0] - 2 * self.lines.d[0]
            
            self.logger.debug(f"KDJ计算: K={self.lines.k[0]:.2f}, D={self.lines.d[0]:.2f}, J={self.lines.j[0]:.2f}")
            
        except Exception as e:
            self.logger.error(f"KDJ计算错误: {str(e)}")
            # 设置默认值
            self.lines.k[0] = 50.0
            self.lines.d[0] = 50.0
            self.lines.j[0] = 50.0


class VolumeAnalysis(bt.Indicator):
    """
    成交量分析指标
    """
    lines = ('vol_ma', 'vol_ratio')
    params = dict(period=5)

    def __init__(self):
        super(VolumeAnalysis, self).__init__()
        self.lines.vol_ma = bt.ind.SMA(self.data.volume, period=self.p.period)
        self.lines.vol_ratio = bt.DivByZero(self.data.volume, self.lines.vol_ma, zero=1.0)
        
        # 获取策略实例的logger
        self.logger = logging.getLogger(f'KDJ_Volume_Strategy_{self.data._dataname}')

    def next(self):
        if len(self) >= self.p.period:
            self.logger.debug(f"成交量分析: 当前量={self.data.volume[0]}, 均量={self.lines.vol_ma[0]:.0f}, 比率={self.lines.vol_ratio[0]:.2f}")


class KDJVolumeResonanceStrategy(bt.Strategy):
    """
    KDJ+成交量共振策略
    """
    params = (
        # 基础参数
        ('kdj_period_1m', 9),  # 1分钟KDJ周期
        ('kdj_period_5m', 9),  # 5分钟KDJ周期
        ('volume_ma_period', 5),  # 成交量均线周期
        
        # 信号参数
        ('j_oversold', 20),  # J值超卖线
        ('j_overbought', 80),  # J值超买线
        ('volume_amplify_ratio', 1.5),  # 成交量放大倍数
        ('volume_shrink_ratio', 0.7),  # 成交量萎缩倍数
        
        # 仓位管理
        ('initial_position_size', 0.05),  # 首单仓位5%
        ('max_position_size', 0.20),  # 最大仓位20%
        ('current_position_size', 0.05),  # 当前仓位大小
        
        # 止盈止损
        ('profit_target_1', 0.005),  # 第一档止盈0.5%
        ('profit_target_2', 0.015),  # 第二档止盈1.5%
        ('stop_loss', 0.003),  # 单笔止损0.3%
        ('max_consecutive_losses', 3),  # 最大连续亏损次数
        
        # 交易时间
        ('trade_time_start', time.fromisoformat(CONFIG['strategy']['trade_time_start'])),
        ('trade_time_end', time.fromisoformat(CONFIG['strategy']['trade_time_end'])),
        ('clear_position_time', time.fromisoformat(CONFIG['strategy']['clear_position_time'])),
        
        # 其他参数
        ('commission', CONFIG['backtest']['commission']['commission']),
    )

    def __init__(self):
        """初始化策略"""
        # 获取股票代码
        self.stock_code = str(self.data._name) if hasattr(self.data, '_name') else None
        
        # 为每个股票创建单独的日志
        self.logger = setup_strategy_logger(self.stock_code)
        
        self.logger.info(f"初始化KDJ+成交量共振策略... 股票代码: {format_stock_code_with_name(self.stock_code)}")
        
        # 基础数据
        self.dataclose = self.datas[0].close
        self.datahigh = self.datas[0].high
        self.datalow = self.datas[0].low
        self.dataopen = self.datas[0].open
        self.datavolume = self.datas[0].volume
        
        # 订单和持仓跟踪
        self.order = None
        self.buyprice = None
        self.buycomm = 0.0  # 初始化为0.0而不是None
        self.has_position = False
        self.position_cost_line = 0  # 成本线
        
        # 交易统计
        self.trade_count = 0
        self.win_count = 0
        self.loss_count = 0
        self.consecutive_losses = 0  # 连续亏损次数
        self.total_profit = 0
        self.total_loss = 0
        self.max_profit = 0
        self.max_loss = 0
        self.trade_history = []
        self.strategy_paused = False  # 策略暂停标志
        
        # 添加计数器用于定期输出
        self.bar_count = 0
        
        # 配置文件中获取是否是实盘模式和实盘调试模式
        self.is_live = CONFIG['backtest'].get('is_live', False)
        self.is_live_debug = CONFIG['backtest'].get('is_live_debug', False) and self.is_live  # 只有在实盘模式下才能开启调试模式
        self.live_debug_size = CONFIG['backtest'].get('live_debug_size', 10)
        
        # 输出策略模式信息
        if self.is_live:
            if self.is_live_debug:
                self.logger.info(f"策略模式: 实盘调试模式 (固定仓位: {self.live_debug_size})")
            else:
                self.logger.info(f"策略模式: 实盘正式模式")
        else:
            self.logger.info(f"策略模式: 回测模式")
        
        # 初始化指标
        self._init_indicators()
        
        self.logger.info("KDJ+成交量共振策略初始化完成")

    def _init_indicators(self):
        """初始化技术指标"""
        self.logger.debug("初始化技术指标...")
        
        # KDJ指标 (假设使用1分钟数据)
        self.kdj_1m = KDJ(self.data, period=self.p.kdj_period_1m)
        
        # 对于5分钟KDJ，这里简化处理，实际应该使用5分钟数据
        # 在实际应用中，您可能需要传入5分钟数据作为第二个数据源
        self.kdj_5m = KDJ(self.data, period=self.p.kdj_period_5m * 5)  # 简化处理
        
        # 成交量分析
        self.volume_analysis = VolumeAnalysis(self.data, period=self.p.volume_ma_period)
        
        # ATR用于动态止损
        self.atr = bt.indicators.ATR(self.data, period=14)
        
        # 均线系统
        self.sma5 = bt.indicators.SMA(self.dataclose, period=5)
        self.sma10 = bt.indicators.SMA(self.dataclose, period=10)
        self.sma20 = bt.indicators.SMA(self.dataclose, period=20)
        
        self.logger.debug("技术指标初始化完成")

    def log(self, txt, dt=None, level=logging.INFO):
        dt = dt or self.datas[0].datetime.datetime(0)
        msg = f'{dt.strftime("%Y-%m-%d %H:%M:%S")} - {txt}'
        if level == logging.DEBUG:
            self.logger.debug(msg)
        elif level == logging.INFO:
            self.logger.info(msg)
        elif level == logging.WARNING:
            self.logger.warning(msg)
        elif level == logging.ERROR:
            self.logger.error(msg)

    def log_trade_status(self):
        """记录当前持仓状态"""
        if self.has_position:
            current_value = self.pos.size * self.dataclose[0]
            cost_basis = self.pos.size * self.buyprice
            current_return = (self.dataclose[0] - self.buyprice) / self.buyprice * 100
            
            self.log(f'当前持仓状态:', level=logging.INFO)
            self.log(f'持仓数量: {self.pos.size}', level=logging.INFO)
            self.log(f'买入价格: {self.buyprice:.4f}', level=logging.INFO)
            self.log(f'当前价格: {self.dataclose[0]:.4f}', level=logging.INFO)
            self.log(f'持仓市值: {current_value:.2f}', level=logging.INFO)
            self.log(f'持仓成本: {cost_basis:.2f}', level=logging.INFO)
            self.log(f'当前收益率: {current_return:.2f}%', level=logging.INFO)
            self.log(f'可用资金: {self.broker.getcash():.2f}', level=logging.INFO)
            self.log(f'总资产: {self.broker.getvalue():.2f}', level=logging.INFO)

    def log_trade_statistics(self):
        """记录交易统计信息"""
        if self.trade_count > 0:
            win_rate = self.win_count / self.trade_count * 100
            avg_profit = self.total_profit / self.win_count if self.win_count > 0 else 0
            avg_loss = self.total_loss / self.loss_count if self.loss_count > 0 else 0
            
            self.log(f'\n=== 交易统计 ===', level=logging.INFO)
            self.log(f'总交易次数: {self.trade_count}', level=logging.INFO)
            self.log(f'盈利次数: {self.win_count}', level=logging.INFO)
            self.log(f'亏损次数: {self.loss_count}', level=logging.INFO)
            self.log(f'连续亏损次数: {self.consecutive_losses}', level=logging.INFO)
            self.log(f'胜率: {win_rate:.2f}%', level=logging.INFO)
            self.log(f'平均盈利: {avg_profit:.2f}%', level=logging.INFO)
            self.log(f'平均亏损: {avg_loss:.2f}%', level=logging.INFO)
            self.log(f'最大盈利: {self.max_profit:.2f}%', level=logging.INFO)
            self.log(f'最大亏损: {self.max_loss:.2f}%', level=logging.INFO)
            self.log(f'策略状态: {"暂停" if self.strategy_paused else "正常"}', level=logging.INFO)

    def get_order_status_text(self, order_status):
        """将订单状态转换为中文描述"""
        status_map = {
            0: '已创建',      # Created
            1: '已提交',      # Submitted  
            2: '已接受',      # Accepted
            3: '部分成交',    # Partial
            4: '已成交',      # Completed
            5: '已取消',      # Canceled
            6: '已过期',      # Expired
            7: '保证金不足',  # Margin
            8: '已拒绝'       # Rejected
        }
        return status_map.get(order_status, f'未知状态({order_status})')

    def notify_order(self, order):
        """订单状态更新通知"""
        # 获取当前数据名称（股票代码）
        data_name = order.data._name
        
        if order.status in [order.Submitted, order.Accepted]:
            # 订单已提交或已接受，无需操作
            return

        # 检查订单是否完成
        if order.status in [order.Completed]:
            # 记录执行价格和数量
            order_type = 'BUY' if order.isbuy() else 'SELL'
            self.log(f'{order_type} 订单执行, 股票: {format_stock_code_with_name(data_name)}, 价格: {order.executed.price:.4f}, '
                   f'数量: {order.executed.size}, 成本: {order.executed.value:.2f}, '
                   f'手续费: {order.executed.comm:.2f}', level=logging.INFO)
            
            # 如果是实盘模式，发送交易通知
            if self.is_live:
                if order.isbuy():
                    # 发送买入通知
                    WechatNotifier.send_trade_notification(
                        trade_type='BUY',
                        stock_code=data_name,
                        price=order.executed.price,
                        quantity=order.executed.size,
                        value=order.executed.value,
                        commission=order.executed.comm,
                        cash=self.broker.getcash(),
                        total_value=self.broker.getvalue(),
                        order_status=self.get_order_status_text(order.status)
                    )
                else:
                    # 计算收益
                    if hasattr(self, 'buyprice') and self.buyprice > 0:
                        # 计算收益率
                        pnl_pct = (order.executed.price - self.buyprice) / self.buyprice
                        # 计算收益金额
                        pnl_value = order.executed.size * (order.executed.price - self.buyprice)
                        # 减去手续费后的净收益 - 安全处理buycomm可能为None的情况
                        buy_commission = self.buycomm if self.buycomm is not None else 0.0
                        net_pnl = pnl_value - order.executed.comm - buy_commission
                        
                        # 发送卖出通知
                        WechatNotifier.send_trade_notification(
                            trade_type='SELL',
                            stock_code=data_name,
                            price=order.executed.price,
                            quantity=order.executed.size,
                            value=order.executed.value,
                            commission=order.executed.comm,
                            buy_price=self.buyprice,
                            pnl=net_pnl,
                            cash=self.broker.getcash(),
                            total_value=self.broker.getvalue(),
                            order_status=self.get_order_status_text(order.status)
                        )
            
            if order.isbuy():
                self.buyprice = order.executed.price
                self.buycomm = order.executed.comm
                self.position_cost_line = self.buyprice  # 设置成本线
                
                # 记录交易
                trade_record = {
                    'time': self.data.datetime.datetime(0),
                    'type': 'BUY',
                    'price': self.buyprice,
                    'size': order.executed.size,
                    'value': order.executed.value,
                    'commission': self.buycomm,
                    'return': 0.0
                }
                self.trade_history.append(trade_record)
                
            else:  # 卖出
                # 计算收益
                if hasattr(self, 'buyprice') and self.buyprice > 0:
                    # 计算收益率
                    pnl_pct = (order.executed.price - self.buyprice) / self.buyprice
                    # 计算收益金额
                    pnl_value = order.executed.size * (order.executed.price - self.buyprice)
                    # 减去手续费后的净收益 - 安全处理buycomm可能为None的情况
                    buy_commission = self.buycomm if self.buycomm is not None else 0.0
                    net_pnl = pnl_value - order.executed.comm - buy_commission
                    
                    # 记录交易结果
                    self.log(f'交易结束. 毛收益: {pnl_value:.2f} ({pnl_pct:.2%}), '
                           f'净收益: {net_pnl:.2f} ({net_pnl / (self.buyprice * order.executed.size):.2%})', 
                           level=logging.INFO)
                    
                    # 更新交易统计
                    self.trade_count += 1
                    if pnl_pct > 0:
                        self.win_count += 1
                        self.consecutive_losses = 0  # 重置连续亏损
                        self.total_profit += pnl_pct * 100
                        self.max_profit = max(self.max_profit, pnl_pct * 100)
                        
                        # 策略有效，可以考虑加仓
                        if self.p.current_position_size < self.p.max_position_size:
                            self.p.current_position_size = min(self.p.max_position_size, 
                                                             self.p.current_position_size + 0.05)
                            self.log(f'策略有效，调整仓位至: {self.p.current_position_size:.1%}', level=logging.INFO)
                    else:
                        self.loss_count += 1
                        self.consecutive_losses += 1
                        self.total_loss += abs(pnl_pct * 100)
                        self.max_loss = max(self.max_loss, abs(pnl_pct * 100))
                        
                        # 检查是否需要暂停策略
                        if (abs(pnl_pct) >= self.p.stop_loss or 
                            self.consecutive_losses >= self.p.max_consecutive_losses):
                            self.strategy_paused = True
                            self.log(f'触发暂停条件: 单笔亏损{abs(pnl_pct):.2%} 或连续亏损{self.consecutive_losses}次', 
                                   level=logging.WARNING)
                    
                    # 记录交易
                    trade_record = {
                        'time': self.data.datetime.datetime(0),
                        'type': 'SELL',
                        'price': order.executed.price,
                        'size': order.executed.size,
                        'value': order.executed.value,
                        'commission': order.executed.comm,
                        'return': pnl_pct * 100,
                        'pnl': net_pnl
                    }
                    self.trade_history.append(trade_record)
                
                self.bar_executed = len(self)

        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log(f'订单被取消/保证金不足/拒绝, 原因: {order.status}', level=logging.WARNING)

        # 无待处理订单
        self.order = None

    def next(self):
        # 检查是否有待处理订单
        if self.order:
            return

        # 检查策略是否暂停
        if self.strategy_paused:
            return

        # 获取当前时间
        current_time = self.data.datetime.time(0)
        
        # 增加计数器
        self.bar_count += 1
        
        # 获取当前持仓
        self.pos = self.getposition(self.data)
        self.has_position = self.pos.size > 0
        
        # 每200个bar输出一次指标状态
        if self.bar_count % 200 == 0:
            self.log(f'\n=== KDJ指标状态报告 ===', level=logging.INFO)
            self.log(f'股票代码: {self.stock_code}', level=logging.INFO)
            self.log(f'当前时间: {self.data.datetime.datetime(0)}', level=logging.INFO)
            self.log(f'当前价格: {self.dataclose[0]:.4f}', level=logging.INFO)
            self.log(f'当前成交量: {self.datavolume[0]}', level=logging.INFO)
            self.log(f'1分钟KDJ - K: {self.kdj_1m.k[0]:.2f}, D: {self.kdj_1m.d[0]:.2f}, J: {self.kdj_1m.j[0]:.2f}', level=logging.INFO)
            self.log(f'5分钟KDJ - K: {self.kdj_5m.k[0]:.2f}, D: {self.kdj_5m.d[0]:.2f}, J: {self.kdj_5m.j[0]:.2f}', level=logging.INFO)
            self.log(f'成交量分析 - 当前量: {self.datavolume[0]}, 均量: {self.volume_analysis.vol_ma[0]:.0f}, 比率: {self.volume_analysis.vol_ratio[0]:.2f}', level=logging.INFO)
            if self.has_position:
                self.log_trade_status()
            if self.is_live_debug:
                self.log(f'当前模式: 实盘调试模式 (固定仓位: {self.live_debug_size})', level=logging.INFO)
            else:
                self.log(f'当前仓位大小: {self.p.current_position_size:.1%}', level=logging.INFO)
            self.log(f'策略状态: {"暂停" if self.strategy_paused else "正常"}', level=logging.INFO)
            self.log(f'===================\n', level=logging.INFO)
        
        # 每个交易日收盘前输出统计信息并清仓
        if current_time >= self.p.clear_position_time:
            # 检查是否启用清仓时撤单功能
            if CONFIG['strategy'].get('cancel_on_clear_position', True):
                self.log('收盘前撤销所有待处理订单', level=logging.INFO)
                if hasattr(self.broker, 'cancel_pending_orders'):
                    cancel_results = self.broker.cancel_pending_orders()
                    if cancel_results:
                        self.log(f'撤销了{len(cancel_results)}个订单', level=logging.INFO)
                        for result in cancel_results:
                            if result['success']:
                                self.log(f"撤单成功: {result['stock_code']} {'买入' if result['order_type'] == 23 else '卖出'}", level=logging.INFO)
                            else:
                                self.log(f"撤单失败: {result['stock_code']} 错误码={result['cancel_result']}", level=logging.WARNING)
            
            self.log_trade_statistics()
            if self.has_position:
                self.log(f'收盘前清仓, 数量: {self.pos.size}', level=logging.INFO)
                self.order = self.sell(size=self.pos.size, price=self.dataclose[0])
                return

        # 检查是否在交易时间内
        if not (self.p.trade_time_start <= current_time <= self.p.trade_time_end):
            return
        
        # 检查并撤销超时订单
        if CONFIG['strategy'].get('enable_auto_cancel', True):
            timeout_seconds = CONFIG['strategy'].get('order_timeout_seconds', 60)
            if hasattr(self.broker, 'check_and_cancel_timeout_orders'):
                cancelled_orders = self.broker.check_and_cancel_timeout_orders(timeout_seconds)
                if cancelled_orders:
                    for cancelled in cancelled_orders:
                        self.log(f"撤销超时订单: {cancelled['stock_code']} {'买入' if cancelled['is_buy'] else '卖出'}, "
                               f"超时{cancelled['timeout_duration']:.1f}秒", level=logging.INFO)
                        
                        # 如果是卖出单被撤销，需要重新提交卖出订单
                        if not cancelled['is_buy'] and self.has_position:
                            self.log(f"卖出单被撤销，重新提交卖出订单", level=logging.INFO)
                            self.order = self.sell(size=self.pos.size, price=self.dataclose[0])
                            return

        # 检查止盈止损条件
        if self.has_position:
            # 计算当前收益率
            current_return = (self.dataclose[0] - self.buyprice) / self.buyprice
            
            # 1. 检查单笔止损条件
            if current_return <= -self.p.stop_loss:
                self.log(f'触发单笔止损, 收益率: {current_return*100:.2f}%, 数量: {self.pos.size}', level=logging.INFO)
                self.order = self.sell(size=self.pos.size, price=self.dataclose[0])
                return
            
            # 2. 动态止盈管理
            if current_return >= self.p.profit_target_1:
                # 盈利达到0.5%后，上移止损至成本线
                if self.dataclose[0] <= self.position_cost_line:
                    self.log(f'触发成本线止损, 收益率: {current_return*100:.2f}%, 数量: {self.pos.size}', level=logging.INFO)
                    self.order = self.sell(size=self.pos.size, price=self.dataclose[0])
                    return
            
            if current_return >= self.p.profit_target_2:
                # 盈利超过1.5%，分批止盈（卖出一半）
                if self.pos.size >= 20:  # 确保有足够数量分批
                    sell_size = self.pos.size // 2
                    self.log(f'触发分批止盈, 收益率: {current_return*100:.2f}%, 卖出数量: {sell_size}', level=logging.INFO)
                    self.order = self.sell(size=sell_size, price=self.dataclose[0])
                    return

            # 3. 检查5分钟KDJ死叉卖出信号
            kdj_5m_death_cross = (self.kdj_5m.k[0] < self.kdj_5m.d[0] and 
                                 self.kdj_5m.k[-1] >= self.kdj_5m.d[-1])
            
            # 4. 检查J值进入超买区后拐头向下
            j_overbought_turn_down = (self.kdj_5m.j[0] > self.p.j_overbought and 
                                    self.kdj_5m.j[0] < self.kdj_5m.j[-1] and 
                                    self.kdj_5m.j[-1] < self.kdj_5m.j[-2])
            
            # 5. 检查成交量萎缩
            volume_shrinking = self.volume_analysis.vol_ratio[0] < self.p.volume_shrink_ratio
            
            # 综合卖出信号
            sell_signal = (
                kdj_5m_death_cross or  # 5分钟KDJ死叉
                (j_overbought_turn_down and volume_shrinking)  # J值超买拐头且成交量萎缩
            )
            
            if sell_signal:
                reason = []
                if kdj_5m_death_cross:
                    reason.append("5分钟KDJ死叉")
                if j_overbought_turn_down and volume_shrinking:
                    reason.append("J值超买拐头且成交量萎缩")
                
                size = self.pos.size
                estimated_pnl = size * (self.dataclose[0] - self.buyprice)
                self.log(f'触发卖出信号: {", ".join(reason)}, 收益率: {current_return*100:.2f}%, '
                       f'数量: {size}, 估计收益: {estimated_pnl:.2f}', level=logging.INFO)
                
                self.order = self.sell(size=size, price=self.dataclose[0])
                return

        # 买入条件检查
        if not self.has_position:
            # 1. 1分钟KDJ金叉（K上穿D）
            kdj_1m_golden_cross = (self.kdj_1m.k[0] > self.kdj_1m.d[0] and 
                                  self.kdj_1m.k[-1] <= self.kdj_1m.d[-1])
            
            # 2. J值从超卖区回升
            j_oversold_recovery = (self.kdj_1m.j[-2] < self.p.j_oversold and 
                                 self.kdj_1m.j[-1] < self.p.j_oversold and 
                                 self.kdj_1m.j[0] > self.kdj_1m.j[-1])
            
            # 3. 成交量较前5分钟均值放大1.5倍以上
            volume_amplified = self.volume_analysis.vol_ratio[0] >= self.p.volume_amplify_ratio
            
            # 综合买入信号
            buy_signal = kdj_1m_golden_cross and j_oversold_recovery and volume_amplified
            
            if buy_signal:
                # 计算仓位
                if self.is_live_debug:
                    # 实盘调试模式：使用固定仓位大小
                    size = self.live_debug_size
                    self.log(f'实盘调试模式 - 使用固定仓位: {size}', level=logging.INFO)
                else:
                    # 正常模式：按比例计算仓位
                    equity = self.broker.getvalue()
                    position_value = equity * self.p.current_position_size
                    size = int(position_value / (self.dataclose[0] * 10)) * 10
                    size = max(size, 10)
                
                if size > 0:
                    buy_value = size * self.dataclose[0]
                    self.log(f'创建买入订单 - KDJ金叉+J值回升+成交量放大', level=logging.INFO)
                    self.log(f'买入参数: 价格={self.dataclose[0]:.4f}, 数量={size}, 金额={buy_value:.2f}', level=logging.INFO)
                    self.log(f'KDJ状态: K={self.kdj_1m.k[0]:.2f}, D={self.kdj_1m.d[0]:.2f}, J={self.kdj_1m.j[0]:.2f}', level=logging.INFO)
                    self.log(f'成交量比率: {self.volume_analysis.vol_ratio[0]:.2f}', level=logging.INFO)
                    
                    self.order = self.buy(size=size, price=self.dataclose[0])

    def stop(self):
        """策略结束时调用的方法"""
        # 输出最终统计信息
        self.log_trade_statistics()
        
        # 记录交易详情到日志
        if hasattr(self, 'trade_history') and self.trade_history:
            self.log("\n=== 交易明细 ===", level=logging.INFO)
            for trade in self.trade_history:
                self.log(f"时间: {trade['time']}, 类型: {trade['type']}, "
                       f"价格: {trade['price']:.4f}, 数量: {trade['size']}, "
                       f"收益率: {trade['return']:.2f}%", level=logging.INFO)
        
        # 如果是实盘模式，发送策略结束通知
        if self.is_live:
            WechatNotifier.send_strategy_summary(
                stock_code=self.stock_code,
                start_cash=self.broker.startingcash,
                end_value=self.broker.getvalue(),
                trade_count=self.trade_count,
                win_count=self.win_count,
                loss_count=self.loss_count
            )
        
        self.log("=== KDJ+成交量共振策略结束 ===", level=logging.INFO) 