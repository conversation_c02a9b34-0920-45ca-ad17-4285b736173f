import backtrader as bt
from datetime import time, datetime, timedelta
import numpy as np
import logging
import os
import yaml
import requests
from utils import WechatNotifier
from stock_data_manager import format_stock_code_with_name

def load_config():
    """加载配置文件"""
    with open('config.yaml', 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

# 加载配置
CONFIG = load_config()

# 注释掉策略独立的日志创建，使用外部传入的日志
# def setup_strategy_logger(stock_code=None):
#     """配置策略日志系统"""
#     # 创建logs目录（如果不存在）
#     if not os.path.exists(CONFIG['logging']['log_dir']):
#         os.makedirs(CONFIG['logging']['log_dir'])
#     
#     # 日志文件名包含当前日期时间和股票代码
#     timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
#     if stock_code and isinstance(stock_code, str):
#         log_filename = f'{CONFIG["logging"]["log_dir"]}/strategy_{stock_code}_{timestamp}.log'
#         logger_name = f'ConnorsRSI_Strategy_{stock_code}'
#     else:
#         log_filename = f'{CONFIG["logging"]["log_dir"]}/strategy_{timestamp}.log'
#         logger_name = 'ConnorsRSI_Strategy'
#     
#     # 配置logger
#     logger = logging.getLogger(logger_name)
#     logger.setLevel(getattr(logging, CONFIG['logging']['level']))
#     
#     # 清除已有的handlers
#     if logger.handlers:
#         logger.handlers.clear()
#     
#     # 添加文件处理器
#     fh = logging.FileHandler(log_filename, encoding='utf-8')
#     fh.setLevel(getattr(logging, CONFIG['logging']['file_level']))
#     
#     # 添加控制台处理器
#     ch = logging.StreamHandler()
#     ch.setLevel(getattr(logging, CONFIG['logging']['console_level']))
#     
#     # 创建formatter
#     file_formatter = logging.Formatter(CONFIG['logging']['file_format'])
#     console_formatter = logging.Formatter(CONFIG['logging']['console_format'])
#     fh.setFormatter(file_formatter)
#     ch.setFormatter(console_formatter)
#     
#     # 添加handlers
#     logger.addHandler(fh)
#     logger.addHandler(ch)
#     
#     return logger

class Streak(bt.ind.PeriodN):
    '''
    Keeps a counter of the current upwards/downwards/neutral streak
    '''
    lines = ('streak',)
    params = dict(period=2)  # need prev/cur days (2) for comparisons

    curstreak = 0

    def next(self):
        d0, d1 = self.data[0], self.data[-1]

        if d0 > d1:
            self.l.streak[0] = self.curstreak = max(1, self.curstreak + 1)
        elif d0 < d1:
            self.l.streak[0] = self.curstreak = min(-1, self.curstreak - 1)
        else:
            self.l.streak[0] = self.curstreak = 0


class ConnorsRSI(bt.Indicator):
    '''
    Calculates the ConnorsRSI as:
        - (RSI(per_rsi) + RSI(Streak, per_streak) + PctRank(per_rank)) / 3
    '''
    lines = ('crsi',)
    params = dict(prsi=3, pstreak=2, prank=100)  # 扩大PercentRank周期提高稳定性

    def __init__(self):
        # Calculate the components
        rsi = bt.ind.RSI(self.data, period=self.p.prsi, safediv=True)
        streak = Streak(self.data)
        rsi_streak = bt.ind.RSI(streak.data, period=self.p.pstreak, safediv=True)
        prank = bt.ind.PercentRank(self.data, period=self.p.prank)

        # Apply the formula
        self.l.crsi = (rsi + rsi_streak + prank) / 3.0


class AvgLines(bt.Indicator):
    lines = ('avg_vol_short', 'avg_vol_long', 'fast_ma', 'slow_ma', 'vol_ratio')
    params = dict(short_period=3, long_period=20, fast_ma_period=5, slow_ma_period=20)

    def __init__(self):
        super(AvgLines, self).__init__()
        
        # 使用SMA计算成交量均值
        self.lines.avg_vol_short = bt.ind.SMA(self.data.volume, period=self.p.short_period)
        self.lines.avg_vol_long = bt.ind.SMA(self.data.volume, period=self.p.long_period)
        
        # 添加快慢均线
        self.lines.fast_ma = bt.ind.EMA(self.data.close, period=self.p.fast_ma_period)
        self.lines.slow_ma = bt.ind.EMA(self.data.close, period=self.p.slow_ma_period)
        
        # 成交量比率 - 使用DivByZero避免除零错误
        self.lines.vol_ratio = bt.DivByZero(self.lines.avg_vol_short, self.lines.avg_vol_long, zero=1.0)

    def next(self):
        # 确保数据已经准备好
        if len(self) < max(self.p.long_period, self.p.slow_ma_period):
            return
        



class ADX(bt.Indicator):
    """
    Average Directional Index - 用于判断趋势强度
    """
    lines = ('adx',)
    # params = dict(period=14)
    params = dict(period=10)
    
    def __init__(self):
        self.adx = bt.indicators.AverageDirectionalMovementIndex(self.data, period=self.p.period)
        self.lines.adx = self.adx.adx


class KAMA(bt.Indicator):
    """
    考夫曼自适应移动平均线 (Kaufman Adaptive Moving Average)
    参数:
        period: 计算周期
        fast_period: 快速EMA周期
        slow_period: 慢速EMA周期
    """
    lines = ('kama',)
    params = (
        ('period', 10),
        ('fast_period', 2),
        ('slow_period', 30),
    )

    def __init__(self):
        super(KAMA, self).__init__()
        self.addminperiod(self.p.period + 2)
        
        # 尝试获取策略的logger
        strategy_logger_name = f'strategy_{self.data._dataname}'
        self.logger = logging.getLogger(strategy_logger_name)
        
        # 如果策略logger不存在，使用根logger
        if not self.logger.handlers:
            self.logger = logging.getLogger()
        
        # 确保logger级别适合输出调试信息
        if self.logger.level > logging.DEBUG:
            self.logger.setLevel(logging.DEBUG)
        
        self.logger.debug(f"KAMA指标初始化: period={self.p.period}, fast_period={self.p.fast_period}, slow_period={self.p.slow_period}")
        self.logger.debug(f"KAMA使用的logger: {self.logger.name} (handlers: {len(self.logger.handlers)})")

    def next(self):
        try:
            # 计算ER
            change = abs(self.data[0] - self.data[-self.p.period])
            self.logger.debug(f"KAMA计算 - 价格变化: {change}")
            
            # 添加安全检查，确保不会出现除零错误
            volatility = 0.0
            for i in range(self.p.period):
                volatility += abs(self.data[-i] - self.data[-i-1])
            self.logger.debug(f"KAMA计算 - 波动率: {volatility}")
                
            # 安全除法
            if volatility > 0.0000001:  # 避免除以非常小的数
                er = change / volatility
            else:
                er = 0.0
            self.logger.debug(f"KAMA计算 - 效率比率(ER): {er}")

            # 计算平滑系数
            fast_sc = 2.0 / (self.p.fast_period + 1)
            slow_sc = 2.0 / (self.p.slow_period + 1)
            sc = (er * (fast_sc - slow_sc) + slow_sc) ** 2
            self.logger.debug(f"KAMA计算 - 平滑系数: {sc}")

            # 计算KAMA
            if len(self) == self.p.period + 2:
                self.lines.kama[0] = self.data[0]  # 初始化
                self.logger.debug(f"KAMA初始化值: {self.lines.kama[0]}")
            else:
                self.lines.kama[0] = self.lines.kama[-1] + sc * (self.data[0] - self.lines.kama[-1])
                self.logger.debug(f"KAMA计算值: {self.lines.kama[0]}")
        except Exception as e:
            self.logger.error(f"KAMA计算错误: {str(e)}")
            self.lines.kama[0] = self.data[0]  # 发生错误时使用当前价格


class VolumePriceAnalysis(bt.Indicator):
    """
    量价关系分析指标
    计算:
    1. 量价相关性
    2. 成交量趋势
    3. 价格动量
    4. 量价背离
    """
    lines = ('vp_correlation', 'vol_trend', 'price_momentum', 'divergence')
    params = dict(period=20)

    def __init__(self):
        super(VolumePriceAnalysis, self).__init__()
        self.addminperiod(self.p.period + 1)
        self.price_change = bt.indicators.PercentChange(self.data.close, period=1)
        self.volume_change = bt.indicators.PercentChange(self.data.volume, period=1)
        self.vol_trend = bt.indicators.SMA(self.data.volume, period=self.p.period)
        self.price_momentum = bt.indicators.ROC(self.data.close, period=self.p.period)
        self.price_ma = bt.indicators.SMA(self.data.close, period=self.p.period)
        self.volume_ma = bt.indicators.SMA(self.data.volume, period=self.p.period)
        # 获取策略实例的logger
        self.logger = logging.getLogger(f'ConnorsRSI_Strategy_{self.data._dataname}')

    def next(self):
        # 量价相关性
        try:
            if len(self) > self.p.period:
                price_arr = [self.price_change[-i] for i in range(self.p.period)]
                volume_arr = [self.volume_change[-i] for i in range(self.p.period)]
                
                # 确保数组不为空且有效
                if len(price_arr) > 0 and len(volume_arr) > 0:
                    try:
                        corr = np.corrcoef(price_arr, volume_arr)[0, 1]
                        if np.isnan(corr) or np.isinf(corr):
                            corr = 0
                    except:
                        corr = 0
                else:
                    corr = 0
                
                self.lines.vp_correlation[0] = corr
                self.logger.debug(f"量价相关性: {corr:.4f}")
            else:
                self.lines.vp_correlation[0] = 0
        except Exception as e:
            self.logger.error(f"计算量价相关性时出错: {str(e)}")
            self.lines.vp_correlation[0] = 0

        # 量价趋势、动量、背离
        self.lines.vol_trend[0] = self.vol_trend[0]
        self.lines.price_momentum[0] = self.price_momentum[0]
        
        # 安全计算背离度，确保不会出现除零错误
        try:
            if self.price_ma[0] > 0.0000001:  # 避免除以非常小的数
                price_deviation = (self.data.close[0] - self.price_ma[0]) / self.price_ma[0]
            else:
                price_deviation = 0
        except:
            price_deviation = 0
            
        try:
            if self.volume_ma[0] > 0.0000001:  # 避免除以非常小的数
                volume_deviation = (self.data.volume[0] - self.volume_ma[0]) / self.volume_ma[0]
            else:
                volume_deviation = 0
        except:
            volume_deviation = 0
            
        self.lines.divergence[0] = price_deviation - volume_deviation
        self.logger.debug(f"量价背离度: {self.lines.divergence[0]:.4f}")


class TripleResonanceStrategy(bt.Strategy):
    """
    三重共振策略
    基于三个条件：
    1. MACD零轴上金叉
    2. ROC连续增长
    3. 成交量突破
    """
    params = (
        ('muti_num', CONFIG['strategy']['muti_num']),
        ('macd_fast', CONFIG['strategy']['macd_fast'] * CONFIG['strategy']['muti_num']),
        ('macd_slow', CONFIG['strategy']['macd_slow'] * CONFIG['strategy']['muti_num']),
        ('macd_signal', CONFIG['strategy']['macd_signal'] * CONFIG['strategy']['muti_num']),
        ('macd_fast_long', CONFIG['strategy']['macd_fast_long'] * CONFIG['strategy']['muti_num']),
        ('macd_slow_long', CONFIG['strategy']['macd_slow_long'] * CONFIG['strategy']['muti_num']),
        ('macd_signal_long', CONFIG['strategy']['macd_signal_long'] * CONFIG['strategy']['muti_num']),
        ('roc_period', CONFIG['strategy']['roc_period'] * CONFIG['strategy']['muti_num']),
        ('vol_ma_period', CONFIG['strategy']['vol_ma_period'] * CONFIG['strategy']['muti_num']),
        ('profit_target', CONFIG['strategy']['profit_target']),
        ('stop_loss', CONFIG['strategy']['stop_loss']),
        ('position_size', CONFIG['strategy']['position_size']),
        ('use_dynamic_position', CONFIG['strategy']['use_dynamic_position']),
        ('risk_per_trade', CONFIG['strategy']['risk_per_trade']),
        ('atr_stop_multiplier', CONFIG['strategy']['atr_stop_multiplier']),
        ('trade_time_start', time.fromisoformat(CONFIG['strategy']['trade_time_start'])),
        ('trade_time_end', time.fromisoformat(CONFIG['strategy']['trade_time_end'])),
        ('clear_position_time', time.fromisoformat(CONFIG['strategy']['clear_position_time'])),
        ('commission', CONFIG['backtest']['commission']['commission']),
        # 添加新的参数，控制卖出信号灵敏度
        ('min_profit_to_sell', CONFIG['strategy'].get('min_profit_to_sell', 0.005)),  # 最小利润率才考虑卖出，默认0.5%
        ('roc_weakening_threshold', CONFIG['strategy'].get('roc_weakening_threshold', 0.8)),  # ROC减弱阈值，默认是0.8
        ('volume_shrink_threshold', CONFIG['strategy'].get('volume_shrink_threshold', 0.7)),  # 成交量萎缩阈值，默认0.7
        ## 新增分批动态止盈
        ('batch_profit_target_1', CONFIG['strategy'].get('batch_profit_target_1', 0.005)),  # 分批止盈阈值，默认0.5%
        ('batch_profit_target_2', CONFIG['strategy'].get('batch_profit_target_2', 0.01)),  # 分批止盈阈值，默认0.5%
        ('batch_profit_target_3', CONFIG['strategy'].get('batch_profit_target_3', 0.015)),  # 分批止盈阈值，默认0.5%
        ('batch_loss_target_1', CONFIG['strategy'].get('batch_loss_target_1', 0.005)),  # 分批止损阈值，默认0.5%
        ('batch_loss_target_2', CONFIG['strategy'].get('batch_loss_target_2', 0.01)),  # 分批止损阈值，默认1%

        # 新增：仓位限制参数
        ('max_single_trade_amount', CONFIG['strategy']['position_limits']['max_single_trade_amount']),
        ('min_single_trade_amount', CONFIG['strategy']['position_limits']['min_single_trade_amount']),
        ('max_single_stock_total_amount', CONFIG['strategy']['position_limits']['max_single_stock_total_amount']),
        ('enable_amount_limits', CONFIG['strategy']['position_limits']['enable_amount_limits']),
        
        # 新增：风险控制参数
        ('max_consecutive_losses_per_stock', CONFIG['strategy']['risk_control']['max_consecutive_losses_per_stock']),
        ('enable_risk_control', CONFIG['strategy']['risk_control']['enable_risk_control']),
    )

    def __init__(self):
        """初始化策略"""
        # 直接从数据获取股票代码，更简洁
        self.stock_code = self.data._name if hasattr(self.data, '_name') and self.data._name else 'UNKNOWN'
        
        # 获取run_backtest.py中创建的策略logger别名
        logger_name = f'strategy_{self.stock_code}'
        self.logger = logging.getLogger(logger_name)
        
        # 如果没有找到对应的logger（handlers为空），则使用根logger作为备选
        if not self.logger.handlers:
            # 实盘模式下可能没有预设的策略logger，需要创建一个
            self.logger = self._create_fallback_logger()
        
        # 确保logger级别设置正确，能够输出INFO级别的日志
        if self.logger.level > logging.INFO:
            self.logger.setLevel(logging.INFO)
        
        self.logger.info(f"策略初始化开始... 股票代码: {format_stock_code_with_name(self.stock_code)}")
        self.logger.info(f"使用的logger: {self.logger.name} (handlers: {len(self.logger.handlers)})")
        
        # 基础数据
        self.dataclose = self.datas[0].close
        self.datahigh = self.datas[0].high
        self.datalow = self.datas[0].low
        self.dataopen = self.datas[0].open
        self.datavolume = self.datas[0].volume
        self.dataamount = self.datas[0].amount
        
        # 订单和持仓跟踪
        self.order = None
        self.buyprice = 0.0
        self.buycomm = 0.0  # 初始化为0.0而不是None
        self.has_position = False
        
        # 新增：已处理的部分成交订单ID集合
        self.processed_orders = set()
        
        # 新增：买入信号跟踪
        self.buy_signal_reason = None  # 记录买入触发信号
        
        # 交易统计
        self.trade_count = 0
        self.win_count = 0
        self.loss_count = 0
        self.total_profit = 0
        self.total_loss = 0
        self.max_profit = 0
        self.max_loss = 0
        self.trade_history = []
        
        # 添加计数器用于定期输出
        self.bar_count = 0
        
        # 新增：风险控制相关变量
        self.stock_consecutive_losses = {}  # 股票连续亏损次数
        self.permanently_stopped_stocks = set()  # 永久停止交易的股票列表
        
        # 配置文件中获取是否是实盘模式和实盘调试模式
        self.is_live = CONFIG['backtest'].get('is_live', False)
        self.is_live_debug = CONFIG['backtest'].get('is_live_debug', False) and self.is_live  # 只有在实盘模式下才能开启调试模式
        self.live_debug_size = CONFIG['backtest'].get('live_debug_size', 10)
        
        # 输出策略模式信息
        if self.is_live:
            if self.is_live_debug:
                self.logger.info(f"策略模式: 实盘调试模式 (固定仓位: {self.live_debug_size})")
            else:
                self.logger.info(f"策略模式: 实盘正式模式")
        else:
            self.logger.info(f"策略模式: 回测模式")
        
        # 初始化指标
        self._init_indicators()
        
        self.logger.info("策略初始化完成")

    def _create_fallback_logger(self):
        """创建备用logger，主要用于实盘模式"""
        fallback_logger_name = f'strategy_fallback_{self.stock_code}'
        fallback_logger = logging.getLogger(fallback_logger_name)
        
        # 如果backup logger也没有handlers，创建基本的handlers
        if not fallback_logger.handlers:
            # 创建logs目录（如果不存在）
            log_dir = CONFIG['logging']['log_dir']
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)
            
            # 生成日志文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_filename = f'{log_dir}/strategy_{self.stock_code}_{timestamp}.log'
            
            # 设置日志级别
            fallback_logger.setLevel(getattr(logging, CONFIG['logging']['level']))
            
            # 添加文件处理器
            fh = logging.FileHandler(log_filename, encoding='utf-8')
            fh.setLevel(getattr(logging, CONFIG['logging']['file_level']))
            
            # 添加控制台处理器
            ch = logging.StreamHandler()
            ch.setLevel(getattr(logging, CONFIG['logging']['console_level']))
            
            # 创建formatter
            file_formatter = logging.Formatter(CONFIG['logging']['file_format'])
            console_formatter = logging.Formatter(CONFIG['logging']['console_format'])
            fh.setFormatter(file_formatter)
            ch.setFormatter(console_formatter)
            
            # 添加handlers
            fallback_logger.addHandler(fh)
            fallback_logger.addHandler(ch)
            
            # 设置propagate=False避免传播到根logger造成重复输出
            fallback_logger.propagate = False
            
            fallback_logger.info(f"创建策略备用logger: {fallback_logger_name}")
        
        return fallback_logger

    def _init_indicators(self):
        """初始化技术指标"""
        self.logger.debug("初始化技术指标...")
        
        # MACD
        self.macd = bt.indicators.MACD(
            self.dataclose,
            period_me1=self.p.macd_fast,
            period_me2=self.p.macd_slow,
            period_signal=self.p.macd_signal
        )

        # MACD_long
        self.macd_long = bt.indicators.MACD(
            self.dataclose,
            period_me1=self.p.macd_fast_long,
            period_me2=self.p.macd_slow_long,
            period_signal=self.p.macd_signal_long
        )
        
        # ROC (变动率)
        self.roc = bt.indicators.ROC(self.dataclose, period=self.p.roc_period)
        
        # 成交量均线
        self.volume_ma = bt.indicators.SMA(self.datavolume, period=self.p.vol_ma_period)
        
        # 布林带
        self.bollinger = bt.indicators.BollingerBands(self.dataclose, period=20 * self.p.muti_num)
        
        # ATR用于动态止损
        self.atr = bt.indicators.ATR(self.data, period=14 * self.p.muti_num)
        
        # 均线系统
        self.sma5 = bt.indicators.SMA(self.dataclose, period=5 * self.p.muti_num)
        self.sma10 = bt.indicators.SMA(self.dataclose, period=10 * self.p.muti_num)
        self.sma20 = bt.indicators.SMA(self.dataclose, period=20 * self.p.muti_num)

        # 成交额均线
        self.amount_ma_3 = bt.indicators.SMA(self.dataamount, period=3)
        self.amount_ma_5 = bt.indicators.SMA(self.dataamount, period=5)
        self.amount_ma_10 = bt.indicators.SMA(self.dataamount, period=10)
        self.amount_ma_20 = bt.indicators.SMA(self.dataamount, period=20)
        
        # 追踪止损价格
        self.trailing_stop_price = 0
        
        self.logger.debug("技术指标初始化完成")

    def _update_stock_stats(self, stock_code, is_loss):
        """更新股票统计数据"""
        if stock_code not in self.stock_consecutive_losses:
            self.stock_consecutive_losses[stock_code] = 0
        
        if is_loss:
            self.stock_consecutive_losses[stock_code] += 1
            self.logger.warning(f"股票 {format_stock_code_with_name(stock_code)} 连续亏损次数: {self.stock_consecutive_losses[stock_code]}")
            
            # 检查是否达到停止阈值
            if self.stock_consecutive_losses[stock_code] >= self.p.max_consecutive_losses_per_stock:
                self.permanently_stopped_stocks.add(stock_code)
                self.logger.error(f"股票 {format_stock_code_with_name(stock_code)} 连续亏损 {self.stock_consecutive_losses[stock_code]} 次，"
                                f"达到阈值 {self.p.max_consecutive_losses_per_stock}，永久停止交易")
        else:
            # 盈利时重置连续亏损计数
            self.stock_consecutive_losses[stock_code] = 0
            self.logger.info(f"股票 {format_stock_code_with_name(stock_code)} 盈利，连续亏损次数已重置")

    def _check_stock_risk_control(self, stock_code):
        """检查单股风险控制"""
        if not self.p.enable_risk_control:
            return True
        
        # 检查股票是否已被永久停止交易
        if stock_code in self.permanently_stopped_stocks:
            self.logger.warning(f"股票 {format_stock_code_with_name(stock_code)} 已被永久停止交易")
            return False
        
        return True

    def _check_and_adjust_position_limits(self, stock_code, size, price):
        """检查并调整仓位限制，返回调整后的合适仓位大小"""
        if not self.p.enable_amount_limits:
            return size
            
        trade_value = size * price
        adjusted_size = size
        
        # 检查单笔交易金额上限
        if trade_value > self.p.max_single_trade_amount:
            adjusted_size = int(self.p.max_single_trade_amount / price)
            # 调整为10的倍数（1手=10张可转债）
            adjusted_size = (adjusted_size // 10) * 10
            adjusted_trade_value = adjusted_size * price
            self.logger.info(f"单笔交易金额 {trade_value:.2f} 超过上限 {self.p.max_single_trade_amount}，"
                           f"调整为 {adjusted_size} 张（{adjusted_size//10}手），金额 {adjusted_trade_value:.2f}")
            trade_value = adjusted_trade_value
        
        # 检查单笔交易金额下限
        if trade_value < self.p.min_single_trade_amount:
            min_required_size = int(self.p.min_single_trade_amount / price) + 1
            # 调整为10的倍数（向上取整到最近的手数）
            min_required_size = ((min_required_size + 9) // 10) * 10
            if min_required_size * price <= self.p.max_single_trade_amount:
                adjusted_size = min_required_size
                adjusted_trade_value = adjusted_size * price
                self.logger.info(f"单笔交易金额 {trade_value:.2f} 低于下限 {self.p.min_single_trade_amount}，"
                               f"调整为 {adjusted_size} 张（{adjusted_size//10}手），金额 {adjusted_trade_value:.2f}")
                trade_value = adjusted_trade_value
            else:
                self.logger.warning(f"无法满足最小交易金额要求 {self.p.min_single_trade_amount}，取消交易")
                return 0
        
        # 检查单股总持仓金额限制
        current_stock_position_value = 0
        for data in self.datas:
            if hasattr(data, '_name') and data._name == stock_code:
                pos = self.getposition(data)
                if pos.size > 0:
                    current_stock_position_value += pos.size * pos.price
        
        total_stock_position_value = current_stock_position_value + trade_value
        
        if total_stock_position_value > self.p.max_single_stock_total_amount:
            # 计算可用的剩余持仓金额
            available_amount = self.p.max_single_stock_total_amount - current_stock_position_value
            if available_amount >= self.p.min_single_trade_amount:
                adjusted_size = int(available_amount / price)
                # 调整为10的倍数（向下取整到最近的手数）
                adjusted_size = (adjusted_size // 10) * 10
                adjusted_trade_value = adjusted_size * price
                self.logger.info(f"股票 {format_stock_code_with_name(stock_code)} 总持仓金额将超过上限，"
                               f"调整为 {adjusted_size} 张（{adjusted_size//10}手），金额 {adjusted_trade_value:.2f}")
                trade_value = adjusted_trade_value
            else:
                self.logger.warning(f"股票 {format_stock_code_with_name(stock_code)} 剩余可用持仓金额 {available_amount:.2f} "
                                  f"不足最小交易金额 {self.p.min_single_trade_amount}，取消交易")
                return 0
        
        # 最终确保调整后的数量是10的倍数
        if adjusted_size % 10 != 0:
            adjusted_size = (adjusted_size // 10) * 10
            self.logger.info(f"最终调整可转债数量为10的倍数: {adjusted_size} 张（{adjusted_size//10}手）")
        
        self.logger.debug(f"仓位检查完成 - 调整后单笔: {trade_value:.2f}, "
                         f"股票总持仓: {current_stock_position_value + trade_value:.2f}/{self.p.max_single_stock_total_amount}")
        
        return adjusted_size

    def log(self, txt, dt=None, level=logging.INFO):
        dt = dt or self.datas[0].datetime.datetime(0)
        msg = f'{dt.strftime("%Y-%m-%d %H:%M:%S")} - {txt}'
        if level == logging.DEBUG:
            self.logger.debug(msg)
        elif level == logging.INFO:
            self.logger.info(msg)
        elif level == logging.WARNING:
            self.logger.warning(msg)
        elif level == logging.ERROR:
            self.logger.error(msg)

    def log_trade_status(self):
        """记录当前持仓状态"""
        if self.has_position:
            current_value = self.pos.size * self.dataclose[0]
            cost_basis = self.pos.size * self.buyprice
            current_return = (self.dataclose[0] - self.buyprice) / self.buyprice * 100
            
            self.log(f'当前持仓状态:', level=logging.INFO)
            self.log(f'持仓数量: {self.pos.size}', level=logging.INFO)
            self.log(f'买入价格: {self.buyprice:.4f}', level=logging.INFO)
            self.log(f'当前价格: {self.dataclose[0]:.4f}', level=logging.INFO)
            self.log(f'持仓市值: {current_value:.2f}', level=logging.INFO)
            self.log(f'持仓成本: {cost_basis:.2f}', level=logging.INFO)
            self.log(f'当前收益率: {current_return:.2f}%', level=logging.INFO)
            self.log(f'可用资金: {self.broker.getcash():.2f}', level=logging.INFO)
            self.log(f'总资产: {self.broker.getvalue():.2f}', level=logging.INFO)

    def get_order_status_text(self, order_status):
        """将订单状态转换为中文描述"""
        status_map = {
            0: '已创建',      # Created
            1: '已提交',      # Submitted  
            2: '已接受',      # Accepted
            3: '部分成交',    # Partial
            4: '已成交',      # Completed
            5: '已取消',      # Canceled
            6: '已过期',      # Expired
            7: '保证金不足',  # Margin
            8: '已拒绝'       # Rejected
        }
        return status_map.get(order_status, f'未知状态({order_status})')

    def log_trade_statistics(self):
        """记录交易统计信息"""
        if self.trade_count > 0:
            win_rate = self.win_count / self.trade_count * 100
            avg_profit = self.total_profit / self.win_count if self.win_count > 0 else 0
            avg_loss = self.total_loss / self.loss_count if self.loss_count > 0 else 0
            
            self.log(f'\n=== 交易统计 ===', level=logging.INFO)
            self.log(f'总交易次数: {self.trade_count}', level=logging.INFO)
            self.log(f'盈利次数: {self.win_count}', level=logging.INFO)
            self.log(f'亏损次数: {self.loss_count}', level=logging.INFO)
            self.log(f'胜率: {win_rate:.2f}%', level=logging.INFO)
            self.log(f'平均盈利: {avg_profit:.2f}%', level=logging.INFO)
            self.log(f'平均亏损: {avg_loss:.2f}%', level=logging.INFO)
            self.log(f'最大盈利: {self.max_profit:.2f}%', level=logging.INFO)
            self.log(f'最大亏损: {self.max_loss:.2f}%', level=logging.INFO)
            

    def notify_order(self, order):
        """订单状态更新通知"""
        # 获取当前数据名称（股票代码）
        data_name = order.data._name
        ## 打印当前订单状态
        self.logger.info(f"订单状态: {order.status}, 订单id: {order.ref}")
        if order.status in [order.Submitted, order.Accepted]:
            # 订单已提交或已接受，无需操作
            return


        if order.status in [order.Partial] and order.isbuy() and order.executed.size > 0 and order.executed.price > 0.0000001:
            # 部分成交，更新买入价格用于追踪止盈止损
            self.log(f'买入部分成交, 股票: {format_stock_code_with_name(data_name)}, 价格: {order.executed.price:.4f}, 数量: {order.executed.size}, 订单ID: {order.ref}', level=logging.INFO)
            self.buyprice = order.executed.price
            # 设置初始追踪止损
            if self.p.use_dynamic_position:
                ## 单次交易计算止盈止损
                self.trailing_stop_price = self.buyprice * (1 - self.p.stop_loss)
            return

        # 检查订单是否完成, 部撤也算完成
        if (order.status in [order.Completed]) or (order.status in [order.Canceled] and order.executed.size > 0):
            # 检查订单ID是否已经处理过
            if order.ref in self.processed_orders:
                self.logger.debug(f'订单ID {order.ref} 已处理过成交，跳过重复处理')
                return
                        
            # 将订单ID添加到已处理集合中
            self.processed_orders.add(order.ref)

            # 记录执行价格和数量
            order_type = 'BUY' if order.isbuy() else 'SELL'
            self.log(f'{order_type} 订单执行, 股票: {format_stock_code_with_name(data_name)}, 价格: {order.executed.price:.4f}, '
                   f'数量: {order.executed.size}, 成本: {order.executed.value:.2f}, '
                   f'手续费: {order.executed.comm:.2f}', level=logging.INFO)
            
            # 如果是实盘模式，发送交易通知
            if self.is_live:
                if order.isbuy():
                    # 发送买入通知
                    WechatNotifier.send_trade_notification(
                        trade_type='BUY',
                        stock_code=data_name,
                        price=order.executed.price,
                        quantity=order.executed.size,
                        value=order.executed.value,
                        commission=order.executed.comm,
                        cash=self.broker.getcash(),
                        total_value=self.broker.getvalue(),
                        order_status=self.get_order_status_text(order.status)
                    )
                else:
                    # 计算收益 - 安全检查避免除零错误
                    if hasattr(self, 'buyprice') and self.buyprice > 0.0000001:
                        # 计算收益率
                        pnl_pct = (order.executed.price - self.buyprice) / self.buyprice
                        # 计算收益金额
                        pnl_value = abs(order.executed.size) * (order.executed.price - self.buyprice)
                        # 减去手续费后的净收益 - 安全处理buycomm可能为None的情况
                        buy_commission = self.buycomm if self.buycomm is not None else 0.0
                        net_pnl = pnl_value - order.executed.comm - buy_commission
                        
                        # 安全计算净收益率，避免除零错误
                        total_buy_value = abs(self.buyprice * order.executed.size)
                        if total_buy_value > 0.0000001:
                            net_pnl_pct = net_pnl / total_buy_value
                        else:
                            net_pnl_pct = 0.0
                        
                        # 发送卖出通知
                        WechatNotifier.send_trade_notification(
                            trade_type='SELL',
                            stock_code=data_name,
                            price=order.executed.price,
                            quantity=order.executed.size,
                            value=order.executed.value,
                            commission=order.executed.comm,
                            buy_price=self.buyprice,
                            pnl=net_pnl,
                            cash=self.broker.getcash(),
                            total_value=self.broker.getvalue(),
                            order_status=self.get_order_status_text(order.status)
                        )
                    else:
                        self.log(f'警告: 无法计算卖出收益，买入价格无效 ({getattr(self, "buyprice", "未设置")})', level=logging.WARNING)
            
            if order.isbuy():
                # 设置买入价格（无论是否使用动态仓位都需要设置）
                self.buyprice = order.executed.price
                self.buycomm = order.executed.comm

                # 设置初始追踪止损
                if self.p.use_dynamic_position:
                    ## 单次交易计算止盈止损
                    self.trailing_stop_price = self.buyprice * (1 - self.p.stop_loss)
                
                # 记录交易 - 包含买入触发信号
                trade_record = {
                    'time': self.data.datetime.datetime(0),
                    'type': 'BUY',
                    'price': self.buyprice,
                    'size': order.executed.size,
                    'value': order.executed.value,
                    'commission': self.buycomm,
                    'return': 0.0,
                    'signal': self.buy_signal_reason if hasattr(self, 'buy_signal_reason') and self.buy_signal_reason else '未知信号'
                }
                self.trade_history.append(trade_record)
                
            else:  # 卖出
                # 计算收益 - 安全检查避免除零错误
                if hasattr(self, 'buyprice') and self.buyprice > 0.0000001:
                    # 计算收益率
                    pnl_pct = (order.executed.price - self.buyprice) / self.buyprice
                    # 计算收益金额
                    pnl_value = abs(order.executed.size) * (order.executed.price - self.buyprice)
                    # 减去手续费后的净收益 - 安全处理buycomm可能为None的情况
                    buy_commission = self.buycomm if self.buycomm is not None else 0.0
                    net_pnl = pnl_value - order.executed.comm - buy_commission
                    
                    # 安全计算净收益率，避免除零错误
                    total_buy_value = abs(self.buyprice * order.executed.size)
                    if total_buy_value > 0.0000001:
                        net_pnl_pct = net_pnl / total_buy_value
                    else:
                        net_pnl_pct = 0.0
                    
                    # 记录交易结果
                    self.log(f'交易结束. 毛收益: {pnl_value:.2f} ({pnl_pct:.2%}), '
                           f'净收益: {net_pnl:.2f} ({net_pnl_pct:.2%})', 
                           level=logging.INFO)
                    
                    # 更新交易统计
                    self.trade_count += 1
                    if pnl_pct > 0:
                        self.win_count += 1
                    else:
                        self.loss_count += 1
                    
                    # 新增：更新风险控制统计
                    is_loss = net_pnl < 0
                    self._update_stock_stats(data_name, is_loss)
                    
                    # 记录交易
                    trade_record = {
                        'time': self.data.datetime.datetime(0),
                        'type': 'SELL',
                        'price': order.executed.price,
                        'size': order.executed.size,
                        'value': order.executed.value,
                        'commission': order.executed.comm,
                        'return': pnl_pct * 100,
                        'pnl': net_pnl
                    }
                    self.trade_history.append(trade_record)
                else:
                    self.log(f'警告: 无法计算卖出收益，买入价格无效 ({getattr(self, "buyprice", "未设置")})', level=logging.WARNING)
                
                self.bar_executed = len(self)

        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log(f'订单被取消/保证金不足/拒绝, 原因: {order.status}', level=logging.WARNING)

        # 无待处理订单
        self.order = None

    def next(self):
        # 获取当前时间
        current_time = self.data.datetime.time(0)

        # 增加计数器
        self.bar_count += 1
        
        # 获取当前持仓
        self.pos = self.getposition(self.data)
        # ## 如果持仓不为空，且当前没有最新买入价，使用持仓均价作为买入价，并更新止盈止损，不能使用持仓均价更新，需要计算单次买入的止盈止损
        # if self.pos.size > 0 and self.buyprice == 0.0:
        #     self.buyprice = self.pos.price
        #     self.trailing_stop_price = self.buyprice * (1 - self.p.stop_loss)

        self.has_position = self.pos.size > 0
        # 每200个bar输出一次指标状态
        if self.bar_count % 200 == 0:
            self.log(f'\n=== 指标状态报告 ===', level=logging.INFO)
            self.log(f'股票代码: {format_stock_code_with_name(self.stock_code)}', level=logging.INFO)
            self.log(f'当前时间: {self.data.datetime.datetime(0)}', level=logging.INFO)
            self.log(f'当前价格: {self.dataclose[0]:.4f}', level=logging.INFO)
            self.log(f'当前成交量: {self.datavolume[0]}', level=logging.INFO)
            self.log(f'MACD: {self.macd.macd[0]:.4f}, Signal: {self.macd.signal[0]:.4f}, Hist: {self.macd.macd[0] - self.macd.signal[0]:.4f}', level=logging.INFO)
            self.log(f'ROC: {self.roc[0]:.4f}', level=logging.INFO)
            self.log(f'成交量均线: {self.volume_ma[0]:.4f}', level=logging.INFO)
            self.log(f'ATR: {self.atr[0]:.4f}', level=logging.INFO)
            self.log(f'均线系统 - SMA5: {self.sma5[0]:.4f}, SMA10: {self.sma10[0]:.4f}, SMA20: {self.sma20[0]:.4f}', level=logging.INFO)
            self.log(f'布林带 - 上轨: {self.bollinger.lines.top[0]:.4f}, 中轨: {self.bollinger.lines.mid[0]:.4f}, 下轨: {self.bollinger.lines.bot[0]:.4f}', level=logging.INFO)
            if self.has_position:
                self.log(f'当前持仓状态:', level=logging.INFO)
                self.log(f'买入价格: {self.buyprice:.4f}', level=logging.INFO)
                # 安全计算收益率，避免除零错误
                if self.buyprice > 0.0000001:
                    current_return_pct = (self.dataclose[0] - self.buyprice) / self.buyprice * 100
                    self.log(f'当前收益率: {current_return_pct:.2f}%', level=logging.INFO)
                else:
                    self.log(f'当前收益率: 无法计算（买入价格无效）', level=logging.INFO)
                if self.p.use_dynamic_position:
                    self.log(f'追踪止损价格: {self.trailing_stop_price:.4f}', level=logging.INFO)
            if self.is_live_debug:
                self.log(f'当前模式: 实盘调试模式 (固定仓位: {self.live_debug_size})', level=logging.INFO)
            self.log(f'可用资金: {self.broker.getcash():.2f}', level=logging.INFO)
            self.log(f'总资产: {self.broker.getvalue():.2f}', level=logging.INFO)
            
            # 新增：风险控制状态报告
            self.log(f'=== 风险控制状态 ===', level=logging.INFO)
            
            if self.stock_code in self.stock_consecutive_losses:
                consecutive_losses = self.stock_consecutive_losses[self.stock_code]
                self.log(f'当前股票连续亏损: {consecutive_losses}/{self.p.max_consecutive_losses_per_stock}', level=logging.INFO)
            
            # 显示永久停止的股票
            if self.permanently_stopped_stocks:
                self.log(f'永久停止交易的股票: {", ".join(self.permanently_stopped_stocks)}', level=logging.INFO)
            
            self.log(f'===================\n', level=logging.INFO)
        
        # 每个交易日收盘前输出统计信息并清仓，还需要检查是否有买入未成交的委托单，全部撤单
        if current_time >= self.p.clear_position_time:
            # 检查是否启用清仓时撤单功能
            if CONFIG['strategy'].get('cancel_on_clear_position', True):
                self.log('收盘前撤销所有待处理订单', level=logging.INFO)
                if hasattr(self.broker, 'cancel_pending_orders'):
                    cancel_results = self.broker.cancel_pending_orders()
                    if cancel_results:
                        self.log(f'撤销了{len(cancel_results)}个订单', level=logging.INFO)
                        for result in cancel_results:
                            if result['success']:
                                self.log(f"撤单成功: {result['stock_code']} {'买入' if result['order_type'] == 23 else '卖出'}", level=logging.INFO)
                            else:
                                self.log(f"撤单失败: {result['stock_code']} 错误码={result['cancel_result']}", level=logging.WARNING)
            
            self.log_trade_statistics()
            if self.has_position:
                self.log(f'收盘前清仓, 数量: {self.pos.size}', level=logging.INFO)
                self.order = self.sell(size=self.pos.size, price=self.dataclose[0])
                return

        # 检查是否在交易时间内
        if not (self.p.trade_time_start <= current_time <= self.p.trade_time_end):
            return
        
        # 检查并撤销超时订单
        if self.is_live and CONFIG['strategy'].get('enable_auto_cancel', True):
            timeout_seconds = CONFIG['strategy'].get('order_timeout_seconds', 60)
            if hasattr(self.broker, 'check_and_cancel_timeout_orders'):
                self.log(f"开始检查超时订单，超时阈值: {timeout_seconds}秒", level=logging.DEBUG)
                cancelled_orders = self.broker.check_and_cancel_timeout_orders(timeout_seconds)
                if cancelled_orders:
                    self.log(f"发现并处理了{len(cancelled_orders)}个超时订单", level=logging.INFO)
                    for cancelled in cancelled_orders:
                        self.log(f"撤销超时订单: {cancelled['stock_code']} {'买入' if cancelled['is_buy'] else '卖出'}, "
                               f"超时{cancelled['timeout_duration']:.1f}秒", level=logging.INFO)
                        
                        # 如果是卖出单被撤销，需要重新提交卖出订单
                        if not cancelled['is_buy'] and self.has_position:
                            self.log(f"卖出单被撤销，重新提交卖出订单", level=logging.INFO)
                            self.order = self.sell(size=self.pos.size, price=self.dataclose[0])
                            return
                else:
                    self.log(f"未发现超时订单", level=logging.DEBUG)
            else:
                self.log(f"broker不支持check_and_cancel_timeout_orders方法", level=logging.WARNING)
        

        # 检查止损条件
        if self.has_position:
            # 计算当前收益率 - 安全检查避免除零错误
            if self.buyprice > 0.0000001:  # 确保买入价格有效
                current_return = (self.dataclose[0] - self.buyprice) / self.buyprice
            else:
                self.log(f'警告: 买入价格无效 ({self.buyprice})，跳过收益率计算', level=logging.WARNING)
                return
            
            # 更新追踪止损，超过一档止盈目标更新动态止损目标
            if current_return >= self.p.batch_profit_target_1 and self.p.use_dynamic_position and self.trailing_stop_price > 0:
                if self.buyprice > 0:
                    self.trailing_stop_price = self.buyprice
                    self.log(f'超过止盈一档，更新追踪止损价格到成本价: {self.trailing_stop_price:.4f}', level=logging.DEBUG)
                else:
                    atr_value = self.atr[0]
                    potential_new_stop = self.dataclose[0] - 5.0 * atr_value
                    if potential_new_stop > self.trailing_stop_price:
                        self.trailing_stop_price = potential_new_stop
                        self.log(f'超过止盈一档，更新动态追踪止损价格: {self.trailing_stop_price:.4f}', level=logging.DEBUG)
            
            # 超过两档止盈目标更新动态止损目标，并卖出两成仓位
            if current_return >= self.p.batch_profit_target_2 and self.p.use_dynamic_position and self.trailing_stop_price > 0:
                if self.buyprice > 0:
                    self.trailing_stop_price = self.buyprice * (1 + self.p.batch_profit_target_1)
                    self.log(f'超过两档止盈目标，更新追踪止损价格到一档止盈价: {self.trailing_stop_price:.4f}', level=logging.DEBUG)
                else:
                    atr_value = self.atr[0]
                    potential_new_stop = self.dataclose[0] - 10.0 * atr_value
                    if potential_new_stop > self.trailing_stop_price:
                        self.trailing_stop_price = potential_new_stop
                        self.log(f'超过两档止盈目标，更新追踪止损价格: {self.trailing_stop_price:.4f}', level=logging.DEBUG)
                # 卖出一半仓位
                self.log(f'[SIGNAL] 超过两档止盈目标，卖出两成仓位', level=logging.INFO)
                size = int((self.pos.size * 0.2) / 10) * 10
                self.order = self.sell(size=size, price=self.dataclose[0])
            
            # 超过三档止盈目标更新动态止损目标，并卖出一半仓位
            if current_return >= self.p.batch_profit_target_3 and self.p.use_dynamic_position and self.trailing_stop_price > 0:
                if self.buyprice > 0:
                    self.trailing_stop_price = self.buyprice * (1 + self.p.batch_profit_target_2)
                    self.log(f'超过三档止盈目标，更新追踪止损价格到两档止盈价: {self.trailing_stop_price:.4f}', level=logging.DEBUG)
                else:
                    atr_value = self.atr[0]
                    potential_new_stop = self.dataclose[0] - 15.0 * atr_value
                    if potential_new_stop > self.trailing_stop_price:
                        self.trailing_stop_price = potential_new_stop
                        self.log(f'超过三档止盈目标，更新追踪止损价格: {self.trailing_stop_price:.4f}', level=logging.DEBUG)
                # 卖出一半仓位
                self.log(f'[SIGNAL] 超过三档止盈目标，卖出一半仓位', level=logging.INFO)
                size = int((self.pos.size * 0.25) / 10) * 10
                self.order = self.sell(size=size, price=self.dataclose[0])

            # 1. 检查止盈条件
            if current_return >= self.p.profit_target:
                self.log(f'[SIGNAL] 触发止盈信号: 收益率 {current_return*100:.2f}%, 数量: {self.pos.size}', level=logging.INFO)
                self.order = self.sell(size=self.pos.size, price=self.dataclose[0])
                return
                
            # 2. 检查固定止损条件
            if current_return <= -self.p.stop_loss:
                self.log(f'[SIGNAL] 触发固定止损信号: 收益率 {current_return*100:.2f}%, 数量: {self.pos.size}', level=logging.INFO)
                self.order = self.sell(size=self.pos.size, price=self.dataclose[0])
                return
                
            # 3. 检查追踪止损条件
            if self.p.use_dynamic_position and self.dataclose[0] < self.trailing_stop_price:
                self.log(f'[SIGNAL] 触发追踪止损信号: 价格 {self.dataclose[0]:.4f}, 止损价: {self.trailing_stop_price:.4f}, 数量: {self.pos.size}', 
                         level=logging.INFO)
                self.order = self.sell(size=self.pos.size, price=self.dataclose[0])
                return
            
            # 4. 检查分批止损条件，一档卖出一半仓位
            if current_return <= -self.p.batch_loss_target_1:
                self.log(f'[SIGNAL] 触发分批止损信号: 收益率 {current_return*100:.2f}%, 数量: {self.pos.size}', level=logging.INFO)
                size = int((self.pos.size / 2) / 10) * 10
                self.order = self.sell(size=size, price=self.dataclose[0])
                return

            # 4. 检查MACD死叉
            macd_death_cross = self.macd_long.macd[0] < self.macd_long.signal[0] and self.macd_long.macd[-1] >= self.macd_long.signal[-1]
            
            # 5. 检查ROC减弱
            # 原来的简单判断：roc_weakening = self.roc[0] < self.roc[-1] and self.roc[-1] < self.roc[-2]
            # 现在使用阈值比较：当前ROC值与前一个比至少下降roc_weakening_threshold比例
            roc_weakening = (self.roc[0] < self.roc[-1] * self.p.roc_weakening_threshold and 
                           self.roc[-1] < self.roc[-2] * self.p.roc_weakening_threshold)
            
            # 6. 检查成交量萎缩（使用配置的阈值）
            volume_shrinking = self.datavolume[0] < self.volume_ma[0] * self.p.volume_shrink_threshold
            
            # 7. 检查均线死叉
            ma_death_cross = self.sma5[0] < self.sma10[0] and self.sma5[-1] >= self.sma10[-1]
            
            # 8. 检查布林带突破
            price_above_upper = self.dataclose[0] > self.bollinger.lines.top[0]
            price_below_lower = self.dataclose[0] < self.bollinger.lines.bot[0]
            
            # 9. 检查价格跌破均线
            price_below_ma = self.dataclose[0] < self.sma5[0]
            
            # 10. 检查MACD柱状图转负
            macd_hist_negative = self.macd.macd[0] - self.macd.signal[0] < 0 and self.macd.macd[-1] - self.macd.signal[-1] > 0
            
            # 11. 检查ROC跌破零轴
            roc_below_zero = self.roc[0] < 0 and self.roc[-1] >= 0
            
            
            # 综合卖出信号
            sell_signal = (
                (macd_death_cross and current_return > self.p.min_profit_to_sell) or  # MACD死叉且有足够利润
                (roc_weakening and volume_shrinking and current_return > self.p.min_profit_to_sell) or  # ROC减弱且成交量萎缩
                (ma_death_cross and current_return > self.p.min_profit_to_sell) or  # 均线死叉且有足够利润
                (price_above_upper and current_return > self.p.profit_target * 0.5) or  # 突破上轨且盈利超过目标的一半
                (price_below_lower and current_return <= -self.p.batch_loss_target_1) or  # 突破下轨且亏损
                (price_below_ma and current_return > self.p.min_profit_to_sell) or  # 价格跌破均线且有足够利润
                (macd_hist_negative and current_return > self.p.min_profit_to_sell) or  # MACD柱状图转负且有足够利润
                (roc_below_zero and current_return > self.p.min_profit_to_sell)  # ROC跌破零轴且有足够利润
            )
            
            if sell_signal:
                reason = []
                if macd_death_cross and current_return > self.p.min_profit_to_sell:
                    reason.append("MACD死叉且有足够利润")
                if roc_weakening and volume_shrinking and current_return > self.p.min_profit_to_sell:
                    reason.append("ROC减弱且成交量萎缩且有足够利润")
                if ma_death_cross and current_return > self.p.min_profit_to_sell:
                    reason.append("均线死叉且有足够利润")
                if price_above_upper and current_return > self.p.profit_target * 0.5:
                    reason.append("突破上轨且盈利超过目标的一半")
                if price_below_lower and current_return < 0:
                    reason.append("突破下轨且亏损")
                if price_below_ma and current_return > self.p.min_profit_to_sell:
                    reason.append("价格跌破均线且有足够利润")
                if macd_hist_negative and current_return > self.p.min_profit_to_sell:
                    reason.append("MACD柱状图转负且有足够利润")
                if roc_below_zero and current_return > self.p.min_profit_to_sell:
                    reason.append("ROC跌破零轴且有足够利润")
                
                # 获取持仓
                size = self.pos.size
                
                # 估算卖出收益
                estimated_pnl = size * (self.dataclose[0] - self.buyprice)
                self.log(f'[SIGNAL] 触发卖出信号: {", ".join(reason)}, 收益率: {current_return*100:.2f}%, '
                       f'数量: {size}, 估计收益: {estimated_pnl:.2f}', level=logging.INFO)
                
                # 明确指定卖出价格
                self.order = self.sell(size=size, price=self.dataclose[0])
                return

        # 买入条件检查
        if not self.has_position:
            # 检查是否有待处理订单             0: '已创建',      # Created 1: '已提交',      # Submitted  2: '已接受',      # Accepted 3: '部分成交',    # Partial
            if self.order and self.order.status in [0, 1, 2, 3]: 
                self.log(f'有待处理订单{self.order.ref}, 订单状态: {self.order.status}, 取消买入订单', level=logging.INFO)
                return
            # 新增：检查单股风险控制
            if not self._check_stock_risk_control(self.stock_code):
                return
            
            # 1. MACD零轴上金叉
            macd_cross_up = self.macd.macd[0] > 0 and self.macd.macd[0] > self.macd.signal[0] and self.macd.macd[-1] <= self.macd.signal[-1]
            
            # 2. ROC连续增长
            roc_increasing = self.roc[0] > self.roc[-1] > self.roc[-2]
            
            # 3. 成交量突破
            volume_breakout = self.datavolume[0] > self.volume_ma[0] * 1.5
            
            # 综合买入信号
            if macd_cross_up and roc_increasing and volume_breakout:
                # 记录买入触发信号
                buy_reasons = []
                buy_reasons.append("MACD零轴上金叉")
                buy_reasons.append("ROC连续增长")
                buy_reasons.append("成交量突破")
                
                self.buy_signal_reason = ", ".join(buy_reasons)
                
                # 计算近3,5,10日成交量均值
                vol_3_avg = sum([self.datavolume[-i] for i in range(min(3, len(self)))]) / min(3, len(self))
                vol_5_avg = sum([self.datavolume[-i] for i in range(min(5, len(self)))]) / min(5, len(self))
                vol_10_avg = sum([self.datavolume[-i] for i in range(min(10, len(self)))]) / min(10, len(self))
                
                # 整合所有指标信息到一条日志记录中
                indicators_detail = (
                    f"买入信号触发详情 - "
                    f"价格: {self.dataclose[0]:.4f} | "
                    f"成交量: 当前={self.datavolume[0]:,.0f}, 均线={self.volume_ma[0]:,.0f}, 倍数={self.datavolume[0]/self.volume_ma[0]:.2f}, "
                    f"近3bar均量={vol_3_avg:,.0f}, 近5bar均量={vol_5_avg:,.0f}, 近10bar均量={vol_10_avg:,.0f} | "
                    f"MACD: 当前={self.macd.macd[0]:.6f}, 前值={self.macd.macd[-1]:.6f}, "
                    f"信号线当前={self.macd.signal[0]:.6f}, 信号线前值={self.macd.signal[-1]:.6f}, "
                    f"柱状图当前={self.macd.macd[0] - self.macd.signal[0]:.6f}, 柱状图前值={self.macd.macd[-1] - self.macd.signal[-1]:.6f} | "
                    f"ROC: 当前={self.roc[0]:.6f}, 前1值={self.roc[-1]:.6f}, 前2值={self.roc[-2]:.6f} | "
                    f"均线系统: SMA5={self.sma5[0]:.4f}, SMA10={self.sma10[0]:.4f}, SMA20={self.sma20[0]:.4f} | "
                    f"成交额均线: 当前={self.dataamount[0]:,.0f}, 近3bar={self.amount_ma_3[0]:,.0f}, 近5bar={self.amount_ma_5[0]:,.0f}, 近10bar={self.amount_ma_10[0]:,.0f} | "
                    f"条件验证: MACD零轴上金叉={macd_cross_up}, ROC连续增长={roc_increasing}, 成交量突破={volume_breakout}"
                )
                
                # 计算仓位
                if self.p.use_dynamic_position:
                    # 使用ATR估算每股风险
                    atr_val = self.atr[0]
                    risk_per_share = max(self.p.stop_loss * self.dataclose[0], atr_val * self.p.atr_stop_multiplier)
                    if risk_per_share < 1e-6:
                        risk_per_share = self.dataclose[0] * 0.02
                    
                    # 计算可买入数量
                    equity = self.broker.getvalue()
                    max_risk_amount = equity * self.p.risk_per_trade
                    size = int(max_risk_amount / risk_per_share / 10) * 10
                    size = max(size, 10)
                else:
                    # 固定仓位
                    position_value = self.broker.getvalue() * self.p.position_size
                    size = int(position_value / (self.dataclose[0] * 10)) * 10
                    size = max(size, 10)
                
                if size > 0:
                    # 实盘调试模式检查
                    if self.is_live_debug:
                        # 实盘调试模式：使用固定仓位大小
                        size = self.live_debug_size
                        self.log(f'实盘调试模式 - 使用固定仓位: {size}', level=logging.INFO)
                    
                    # 新增：检查并调整仓位限制
                    adjusted_size = self._check_and_adjust_position_limits(self.stock_code, size, self.dataclose[0])
                    if adjusted_size <= 10:
                        self.log(f'仓位限制检查后无法交易，取消买入订单', level=logging.WARNING)
                        return
                    
                    # 使用调整后的仓位大小
                    size = adjusted_size
                    
                    # 计算买入金额
                    buy_value = size * self.dataclose[0]
                    self.log(f'[SIGNAL] 触发买入信号: {self.buy_signal_reason}， [SIGNAL_DETAIL] {indicators_detail}', level=logging.INFO)
                    self.log(f'创建买入订单, 价格: {self.dataclose[0]:.4f}, 数量: {size}, 金额: {buy_value:.2f}', 
                           level=logging.INFO)
                    # 明确指定买入价格
                    self.order = self.buy(size=size, price=self.dataclose[0])

    def stop(self):
        """策略结束时调用的方法"""
        # 输出最终统计信息
        self.log_trade_statistics()
        
        # 新增：输出风险控制统计
        self.log("\n=== 风险控制统计 ===", level=logging.INFO)
        
        if self.stock_consecutive_losses:
            self.log("股票连续亏损统计:", level=logging.INFO)
            for stock_code, consecutive_losses in self.stock_consecutive_losses.items():
                self.log(f"  {stock_code}: 连续亏损 {consecutive_losses} 次", level=logging.INFO)
        
        if self.permanently_stopped_stocks:
            self.log("永久停止交易的股票:", level=logging.INFO)
            for stock_code in self.permanently_stopped_stocks:
                self.log(f"  {stock_code}: 永久停止", level=logging.INFO)
        
        # 记录交易详情到日志
        if hasattr(self, 'trade_history') and self.trade_history:
            self.log("\n=== 交易明细 ===", level=logging.INFO)
            for trade in self.trade_history:
                self.log(f"时间: {trade['time']}, 类型: {trade['type']}, "
                       f"价格: {trade['price']:.4f}, 数量: {trade['size']}, "
                       f"收益率: {trade['return']:.2f}%", level=logging.INFO)
        
        # 如果是实盘模式，发送策略结束通知
        if self.is_live:
            WechatNotifier.send_strategy_summary(
                stock_code=self.stock_code,
                start_cash=self.broker.startingcash,
                end_value=self.broker.getvalue(),
                trade_count=self.trade_count,
                win_count=self.win_count,
                loss_count=self.loss_count
            )
        
        self.log("=== 策略结束 ===", level=logging.INFO)


