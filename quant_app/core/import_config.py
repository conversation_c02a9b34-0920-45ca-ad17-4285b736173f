#!/usr/bin/env python3
"""
配置导入工具
将YAML配置导入到SQLite数据库中
"""

import os
import sys
import logging
import argparse
from datetime import datetime

# 添加项目根目录到Python路径
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(root_dir)

from quant_app.core.db_config_manager import db_config_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('import_config')

def import_config_from_yaml(yaml_path, clear_existing=False):
    """将YAML配置导入到SQLite数据库
    
    Args:
        yaml_path: YAML配置文件路径
        clear_existing: 是否在导入前清空现有配置
        
    Returns:
        bool: 是否成功导入
    """
    if not os.path.exists(yaml_path):
        logger.error(f"YAML文件不存在: {yaml_path}")
        return False
    
    # 如果指定了清空，则清空数据库中的配置
    if clear_existing:
        try:
            for section in db_config_manager.get_sections():
                db_config_manager.clear_section(section['section'])
            logger.info("已清空数据库中的配置")
        except Exception as e:
            logger.error(f"清空数据库失败: {str(e)}")
    
    # 导入YAML
    logger.info(f"导入YAML到数据库: {yaml_path}")
    if db_config_manager.import_from_yaml(yaml_path):
        logger.info("导入成功")
        return True
    else:
        logger.error("导入失败")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='将YAML配置导入到SQLite数据库')
    parser.add_argument('--yaml', type=str, default=os.path.join(root_dir, 'config.yaml'),
                        help='YAML配置文件路径 (默认: config.yaml)')
    parser.add_argument('--db', type=str, 
                        default=os.path.join(root_dir, 'quant_app', 'data', 'system.db'),
                        help='SQLite数据库路径 (默认: quant_app/data/system.db)')
    parser.add_argument('--export', action='store_true',
                        help='导出SQLite数据库配置到YAML文件')
    parser.add_argument('--clear', action='store_true',
                        help='导入前清空数据库中的配置')
    
    args = parser.parse_args()
    
    if args.export:
        # 导出到YAML
        yaml_path = args.yaml
        if os.path.exists(yaml_path):
            backup_path = f"{yaml_path}.{datetime.now().strftime('%Y%m%d%H%M%S')}.bak"
            logger.info(f"备份原YAML文件到: {backup_path}")
            os.rename(yaml_path, backup_path)
        
        logger.info(f"导出配置到YAML: {yaml_path}")
        if db_config_manager.export_to_yaml(yaml_path):
            logger.info("导出成功")
        else:
            logger.error("导出失败")
            return 1
    else:
        # 导入YAML到数据库
        yaml_path = args.yaml
        if not os.path.exists(yaml_path):
            logger.error(f"YAML文件不存在: {yaml_path}")
            return 1
        
        # 备份数据库
        db_path = args.db
        if os.path.exists(db_path):
            backup_path = f"{db_path}.{datetime.now().strftime('%Y%m%d%H%M%S')}.bak"
            logger.info(f"备份原数据库到: {backup_path}")
            import shutil
            shutil.copy2(db_path, backup_path)
        
        # 调用导入函数
        if import_config_from_yaml(yaml_path, clear_existing=args.clear):
            logger.info("导入成功")
        else:
            logger.error("导入失败")
            return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main()) 