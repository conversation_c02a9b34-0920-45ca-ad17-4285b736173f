"""
数据处理器 - 处理行情数据和交易数据
"""
import os
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

from quant_app.core.db_config_manager import db_config_manager
from quant_app.core.qmt_client import QMTClient

# 配置日志
logger = logging.getLogger(__name__)

class DataHandler:
    """数据处理器 - 统一的数据访问接口"""
    
    def __init__(self):
        """初始化数据处理器"""
        self.qmt_client = QMTClient()
        self.logger = logging.getLogger(__name__)
    
    def get_market_overview(self) -> Dict[str, Dict[str, Any]]:
        """获取市场概览数据
        
        Returns:
            Dict[str, Dict[str, Any]]: 市场指数数据，格式：
            {
                '上证指数': {'code': '000001.SH', 'price': 3000.0, 'change': 10.0, 'change_pct': 0.33},
                '深证成指': {'code': '399001.SZ', 'price': 12000.0, 'change': 20.0, 'change_pct': 0.17},
                ...
            }
        """
        try:
            # 定义要获取的指数
            indices = {
                '上证指数': '000001.SH',
                '深证成指': '399001.SZ',
                '创业板指': '399006.SZ',
                '上证50': '000016.SH',
                '沪深300': '000300.SH',
                '中证500': '000905.SH'
            }
            
            # 获取最新行情
            result = {}
            for name, code in indices.items():
                try:
                    # 获取K线数据
                    data = self.qmt_client.get_market_data_ex(
                        stock_list=[code],
                        period='1d',
                        count=2,
                        dividend_type='none'
                    )
                    
                    if data and code in data:
                        df = data[code]
                        if not df.empty:
                            latest = df.iloc[-1]
                            prev = df.iloc[-2]
                            
                            result[name] = {
                                'code': code,
                                'price': float(latest['close']),
                                'change': float(latest['close'] - prev['close']),
                                'change_pct': float((latest['close'] - prev['close']) / prev['close'] * 100)
                            }
                    else:
                        self.logger.warning(f"未获取到指数 {code} 的行情数据")
                        
                except Exception as e:
                    self.logger.warning(f"获取指数 {code} 行情失败: {str(e)}")
                    continue
            
            if not result:
                raise Exception("未获取到任何指数行情数据")
                
            return result
            
        except Exception as e:
            self.logger.error(f"获取市场概览失败: {str(e)}")
            # 返回模拟数据
            return {
                '上证指数': {'code': '000001.SH', 'price': 3000.0, 'change': 10.0, 'change_pct': 0.33},
                '深证成指': {'code': '399001.SZ', 'price': 12000.0, 'change': 20.0, 'change_pct': 0.17},
                '创业板指': {'code': '399006.SZ', 'price': 2500.0, 'change': 5.0, 'change_pct': 0.20}
            }
    
    def get_selected_stocks(self) -> List[str]:
        """获取选中的股票列表
        
        Returns:
            List[str]: 股票代码列表
        """
        stocks = db_config_manager.get_config('backtest', 'stock_codes', [])
        if isinstance(stocks, str):
            # 处理可能的字符串格式
            try:
                stocks = json.loads(stocks)
            except:
                stocks = stocks.split(',')
        
        return stocks if isinstance(stocks, list) else []
    
    def get_trading_status(self) -> Dict[str, Any]:
        """获取交易状态
        
        Returns:
            Dict[str, Any]: 交易状态信息
        """
        # 检查QMT连接状态
        qmt_connected = False
        try:
            # 尝试连接QMT
            qmt_connected = self.qmt_client.connect()
        except:
            qmt_connected = False
        
        # 获取交易模式
        is_live = db_config_manager.get_config('qmt', 'is_live', False)
        
        # 获取交易账户信息
        account_id = db_config_manager.get_config('qmt', 'account_id', '')
        
        # 检查交易进程
        active_trading_processes = []
        try:
            import psutil
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if 'run_backtest.py' in cmdline and '--live' in cmdline:
                        # 提取股票代码
                        stock_code = 'Unknown'
                        for i, arg in enumerate(proc.info['cmdline']):
                            if arg == '--stock-code' and i + 1 < len(proc.info['cmdline']):
                                stock_code = proc.info['cmdline'][i + 1]
                                break
                        
                        active_trading_processes.append({
                            'pid': proc.info['pid'],
                            'stock_code': stock_code,
                            'cmd': cmdline
                        })
                except:
                    continue
        except:
            pass
        
        # 交易配置
        trading_config = {
            'initial_cash': db_config_manager.get_config('backtest', 'initial_cash', 1000000),
            'max_position_ratio': db_config_manager.get_config('risk', 'max_position_ratio', 0.8),
            'stop_loss_ratio': db_config_manager.get_config('risk', 'stop_loss_ratio', 0.05),
            'take_profit_ratio': db_config_manager.get_config('risk', 'take_profit_ratio', 0.2)
        }
        
        return {
            'qmt_connected': qmt_connected,
            'is_live': is_live,
            'account_id': account_id,
            'active_processes': active_trading_processes,
            'trading_config': trading_config,
            'selected_stocks': self.get_selected_stocks()
        }
    
    def get_strategies(self) -> List[Dict[str, Any]]:
        """获取可用策略列表
        
        Returns:
            List[Dict[str, Any]]: 策略信息列表
        """
        try:
            # 获取策略配置
            strategy_config = db_config_manager.get_config('strategy_config', {})
            if not strategy_config:
                return []
            
            # 获取活动策略
            active_strategy = strategy_config.get('active_strategy')
            strategies = strategy_config.get('strategies', {})
            
            # 构建策略列表
            result = []
            for name, info in strategies.items():
                strategy = {
                    'name': name,
                    'file': info.get('module', '').replace('.py', ''),
                    'description': info.get('description', ''),
                    'class': info.get('class', ''),
                    'parameters': info.get('parameters', {})
                }
                result.append(strategy)
            
            return result
            
        except Exception as e:
            self.logger.error(f"获取策略列表失败: {str(e)}")
            return []
    
    def get_data_statistics(self) -> Dict[str, Any]:
        """获取数据统计信息
        
        Returns:
            Dict[str, Any]: 数据统计信息字典
        """
        result = {
            'stock_count': 0,
            'data_size': 0,
            'last_update': None,
            'data_types': {}
        }
        
        try:
            # 获取股票列表
            stocks = self.get_selected_stocks()
            result['stock_count'] = len(stocks)
            
            # 获取数据目录
            data_dir = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                'quant_app',
                'data'
            )
            
            # 计算数据大小
            if os.path.exists(data_dir):
                total_size = 0
                for root, dirs, files in os.walk(data_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        total_size += os.path.getsize(file_path)
                result['data_size'] = total_size
            
            # 获取最后更新时间
            if os.path.exists(data_dir):
                result['last_update'] = datetime.fromtimestamp(
                    os.path.getmtime(data_dir)
                ).strftime('%Y-%m-%d %H:%M:%S')
            
            # 统计数据类型
            if os.path.exists(data_dir):
                for root, dirs, files in os.walk(data_dir):
                    for file in files:
                        ext = os.path.splitext(file)[1]
                        if ext not in result['data_types']:
                            result['data_types'][ext] = 0
                        result['data_types'][ext] += 1
            
        except Exception as e:
            self.logger.error(f"获取数据统计信息失败: {str(e)}")
        
        return result


# 创建全局实例
data_handler = DataHandler() 