import logging
from typing import List, Dict, Any, Union
from datetime import datetime, timedelta
import xtquant.xtdata as xtdata
import xtquant.xttrader as xttrader
import pandas as pd

logger = logging.getLogger(__name__)

class QMTClient:
    def __init__(self):
        """初始化QMT客户端"""
        self.xtdata = xtdata
        self.xttrader = xttrader
        self.connected = False
        self.account_id = None
        
    def connect(self, account_id: str = None) -> bool:
        """连接QMT
        
        Args:
            account_id (str, optional): 账户ID. Defaults to None.
            
        Returns:
            bool: 是否连接成功
        """
        try:
            # 初始化数据接口
            self.xtdata.download_config()
            
            # 初始化交易接口
            if account_id:
                self.account_id = account_id
                self.xttrader.start()
                self.connected = True
                logger.info(f"QMT连接成功，账户ID: {account_id}")
            else:
                logger.warning("未提供账户ID，仅初始化数据接口")
                
            return True
            
        except Exception as e:
            logger.error(f"QMT连接失败: {str(e)}")
            return False
            
    def disconnect(self):
        """断开QMT连接"""
        try:
            if self.connected:
                self.xttrader.stop()
                self.connected = False
                logger.info("QMT已断开连接")
        except Exception as e:
            logger.error(f"断开QMT连接失败: {str(e)}")
            
    def download_history_data(self, stock_list: List[str], period: str = '1d', 
                            start_time: str = None, end_time: str = None,
                            dividend_type: str = 'front') -> Dict[str, pd.DataFrame]:
        """下载历史行情数据
        
        Args:
            stock_list (List[str]): 股票代码列表，例如：['000001.SZ', '600000.SH']
            period (str, optional): K线周期，支持：1m, 5m, 15m, 30m, 1h, 1d, 1w, 1M. Defaults to '1d'.
            start_time (str, optional): 开始时间，格式：YYYYMMDDHHMMSS. Defaults to None.
            end_time (str, optional): 结束时间，格式：YYYYMMDDHHMMSS. Defaults to None.
            dividend_type (str, optional): 除权类型，支持：front(前复权), back(后复权), none(不复权). Defaults to 'front'.
            
        Returns:
            Dict[str, pd.DataFrame]: 股票代码为key，DataFrame为value的字典
        """
        try:
            # 转换周期格式
            period_map = {
                '1m': '1min',
                '5m': '5min',
                '15m': '15min',
                '30m': '30min',
                '1h': '60min',
                '1d': 'day',
                '1w': 'week',
                '1M': 'month'
            }
            period = period_map.get(period, period)
            
            # 转换除权类型
            dividend_map = {
                'front': 1,  # 前复权
                'back': 2,   # 后复权
                'none': 3    # 不复权
            }
            dividend_type = dividend_map.get(dividend_type, 1)
            
            # 下载数据
            data = self.xtdata.download_history_data(
                stock_list,
                period=period,
                start_time=start_time,
                end_time=end_time,
                dividend_type=dividend_type,
                fill_data=True  # 自动填充缺失数据
            )
            
            if not data:
                logger.warning(f"未获取到历史数据: {stock_list}, period={period}")
                return {}
                
            return data
            
        except Exception as e:
            logger.error(f"下载历史数据失败: {str(e)}")
            return {}
            
    def get_market_data_ex(self, stock_list: List[str], period: str = '1d',
                          count: int = 1, dividend_type: str = 'front') -> Dict[str, pd.DataFrame]:
        """获取市场行情数据
        
        Args:
            stock_list (List[str]): 股票代码列表，例如：['000001.SZ', '600000.SH']
            period (str, optional): K线周期，支持：1m, 5m, 15m, 30m, 1h, 1d, 1w, 1M. Defaults to '1d'.
            count (int, optional): 获取数量. Defaults to 1.
            dividend_type (str, optional): 除权类型，支持：front(前复权), back(后复权), none(不复权). Defaults to 'front'.
            
        Returns:
            Dict[str, pd.DataFrame]: 股票代码为key，DataFrame为value的字典
        """
        try:
            # 转换周期格式
            period_map = {
                '1m': '1min',
                '5m': '5min',
                '15m': '15min',
                '30m': '30min',
                '1h': '60min',
                '1d': 'day',
                '1w': 'week',
                '1M': 'month'
            }
            period = period_map.get(period, period)
            
            # 转换除权类型
            dividend_map = {
                'front': 1,  # 前复权
                'back': 2,   # 后复权
                'none': 3    # 不复权
            }
            dividend_type = dividend_map.get(dividend_type, 1)
            
            # 获取数据
            data = self.xtdata.get_market_data_ex(
                stock_list,
                period=period,
                count=count,
                dividend_type=dividend_type,
                fill_data=True  # 自动填充缺失数据
            )
            
            if not data:
                logger.warning(f"未获取到市场数据: {stock_list}, period={period}, count={count}")
                return {}
                
            return data
            
        except Exception as e:
            logger.error(f"获取市场数据失败: {str(e)}")
            return {}
            
    def get_kline_data(self, code: str, period: str = '1d', count: int = 1, dividend_type: str = 'front') -> List[Dict[str, Any]]:
        """获取K线数据（兼容旧接口）
        
        Args:
            code (str): 股票代码，例如：'000001.SZ'
            period (str, optional): K线周期，支持：1m, 5m, 15m, 30m, 1h, 1d, 1w, 1M. Defaults to '1d'.
            count (int, optional): 获取数量. Defaults to 1.
            dividend_type (str, optional): 除权类型，支持：front(前复权), back(后复权), none(不复权). Defaults to 'front'.
            
        Returns:
            List[Dict[str, Any]]: K线数据列表
        """
        try:
            # 使用新接口获取数据
            data = self.get_market_data_ex([code], period, count, dividend_type)
            if not data or code not in data:
                return []
                
            # 转换数据格式
            df = data[code]
            klines = []
            for _, row in df.iterrows():
                kline = {
                    'time': row['time'],
                    'open': float(row['open']),
                    'high': float(row['high']),
                    'low': float(row['low']),
                    'close': float(row['close']),
                    'volume': float(row['volume']),
                    'amount': float(row['amount'])
                }
                klines.append(kline)
                
            return klines
            
        except Exception as e:
            logger.error(f"获取K线数据失败: {str(e)}")
            return []
            
    def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息
        
        Returns:
            Dict[str, Any]: 账户信息
        """
        try:
            if not self.connected:
                raise Exception("QMT未连接")
                
            account_info = self.xttrader.query_stock_asset(self.account_id)
            if not account_info:
                return {}
                
            return {
                'total_asset': account_info.get('total_asset', 0),
                'cash': account_info.get('cash', 0),
                'market_value': account_info.get('market_value', 0),
                'frozen_cash': account_info.get('frozen_cash', 0),
                'frozen_market_value': account_info.get('frozen_market_value', 0)
            }
            
        except Exception as e:
            logger.error(f"获取账户信息失败: {str(e)}")
            return {}
            
    def get_positions(self) -> List[Dict[str, Any]]:
        """获取持仓信息
        
        Returns:
            List[Dict[str, Any]]: 持仓列表
        """
        try:
            if not self.connected:
                raise Exception("QMT未连接")
                
            positions = self.xttrader.query_stock_positions(self.account_id)
            if not positions:
                return []
                
            position_list = []
            for pos in positions:
                position = {
                    'code': pos.get('stock_code'),
                    'name': pos.get('stock_name'),
                    'volume': pos.get('volume'),
                    'available': pos.get('available'),
                    'cost': pos.get('cost'),
                    'market_value': pos.get('market_value'),
                    'profit': pos.get('profit'),
                    'profit_ratio': pos.get('profit_ratio')
                }
                position_list.append(position)
                
            return position_list
            
        except Exception as e:
            logger.error(f"获取持仓信息失败: {str(e)}")
            return [] 