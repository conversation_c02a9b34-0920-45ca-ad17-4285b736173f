"""
执行引擎 - 封装回测和交易执行逻辑
"""
import os
import sys
import json
import subprocess
import threading
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
import logging

import pandas as pd

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from quant_app.core.db_config_manager import db_config_manager
from quant_app.core.task_manager import task_manager, TaskType


class ExecutionEngine:
    """执行引擎 - 统一的任务执行接口"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._callbacks = {}
    
    def run_backtest(self, strategy_name: str, params: Dict[str, Any],
                    callback: Callable = None) -> str:
        """运行回测任务"""
        try:
            # 合并配置参数
            backtest_params = {
                'initial_capital': db_config_manager.get_config('initial_capital', 1000000),
                'commission_rate': db_config_manager.get_config('commission_rate', 0.0003),
                'slippage': db_config_manager.get_config('slippage', 0.002),
                **params  # 用户参数覆盖默认参数
            }
            
            # 创建任务
            task_id = task_manager.create_task(
                task_type=TaskType.BACKTEST,
                strategy_name=strategy_name,
                params=backtest_params
            )
            
            # 保存回调
            if callback:
                self._callbacks[task_id] = callback
            
            # 启动任务
            task_manager.start_task(task_id)
            
            # 如果有回调，启动监控线程
            if callback:
                threading.Thread(
                    target=self._monitor_task,
                    args=(task_id,),
                    daemon=True
                ).start()
            
            return task_id
            
        except Exception as e:
            self.logger.error(f"运行回测失败: {str(e)}")
            raise
    
    def run_trading(self, strategy_name: str, params: Dict[str, Any],
                   callback: Callable = None) -> str:
        """运行交易任务"""
        try:
            # 合并配置参数
            trading_params = {
                'account_id': db_config_manager.get_config('account_id'),
                'max_position_ratio': db_config_manager.get_config('max_position_ratio', 0.8),
                'single_stock_limit': db_config_manager.get_config('single_stock_limit', 0.2),
                'stop_loss_ratio': db_config_manager.get_config('stop_loss_ratio', 0.05),
                **params
            }
            
            # 检查必要参数
            if not trading_params.get('account_id'):
                raise ValueError("未配置交易账户ID")
            
            # 创建任务
            task_id = task_manager.create_task(
                task_type=TaskType.TRADING,
                strategy_name=strategy_name,
                params=trading_params
            )
            
            # 保存回调
            if callback:
                self._callbacks[task_id] = callback
            
            # 启动任务
            task_manager.start_task(task_id)
            
            # 如果有回调，启动监控线程
            if callback:
                threading.Thread(
                    target=self._monitor_task,
                    args=(task_id,),
                    daemon=True
                ).start()
            
            return task_id
            
        except Exception as e:
            self.logger.error(f"运行交易失败: {str(e)}")
            raise
    
    def stop_task(self, task_id: str, force: bool = False) -> bool:
        """停止任务"""
        return task_manager.stop_task(task_id, force)
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        task_info = task_manager.get_task(task_id)
        if not task_info:
            return {}
        
        # 添加日志预览
        log_lines = task_manager.get_task_log(task_id, lines=50)
        task_info['log_preview'] = log_lines
        
        return task_info
    
    def get_task_result(self, task_id: str) -> Dict[str, Any]:
        """获取任务结果"""
        task_info = task_manager.get_task(task_id)
        if not task_info:
            return {}
        
        result = {
            'task_id': task_id,
            'status': task_info['status'],
            'strategy_name': task_info['strategy_name'],
            'params': json.loads(task_info['params']),
            'created_at': task_info['created_at'],
            'finished_at': task_info['finished_at']
        }
        
        # 如果任务完成，解析日志获取结果
        if task_info['status'] in ['completed', 'failed']:
            from quant_app.core.log_parser import log_parser
            
            if task_info['log_file'] and os.path.exists(task_info['log_file']):
                parsed_data = log_parser.parse_log_file(task_info['log_file'])
                result['performance'] = log_parser.get_performance_metrics(parsed_data)
                result['summary'] = parsed_data.get('summary', {})
                result['trades'] = len(parsed_data.get('trades', []))
                result['errors'] = len(parsed_data.get('errors', []))
        
        return result
    
    def _monitor_task(self, task_id: str):
        """监控任务并触发回调"""
        callback = self._callbacks.get(task_id)
        if not callback:
            return
        
        last_status = None
        
        while True:
            task_info = task_manager.get_task(task_id)
            if not task_info:
                break
            
            current_status = task_info['status']
            
            # 状态变化时触发回调
            if current_status != last_status:
                callback(task_id, current_status, task_info)
                last_status = current_status
            
            # 任务结束时退出监控
            if current_status in ['completed', 'failed', 'cancelled']:
                # 清理回调
                if task_id in self._callbacks:
                    del self._callbacks[task_id]
                break
            
            # 休眠一段时间
            threading.Event().wait(1)
    
    def batch_backtest(self, strategy_name: str, param_sets: List[Dict[str, Any]],
                      callback: Callable = None) -> List[str]:
        """批量回测"""
        task_ids = []
        
        for i, params in enumerate(param_sets):
            try:
                # 为每个参数集创建任务
                task_id = self.run_backtest(
                    strategy_name=strategy_name,
                    params=params,
                    callback=lambda tid, status, info: callback(
                        tid, status, info, i, len(param_sets)
                    ) if callback else None
                )
                task_ids.append(task_id)
                
            except Exception as e:
                self.logger.error(f"批量回测任务 {i} 失败: {str(e)}")
        
        return task_ids
    
    def get_optimization_results(self, task_ids: List[str]) -> pd.DataFrame:
        """获取优化结果"""
        results = []
        
        for task_id in task_ids:
            result = self.get_task_result(task_id)
            if result and result.get('status') == 'completed':
                row = {
                    'task_id': task_id,
                    **result.get('params', {}),
                    **result.get('performance', {})
                }
                results.append(row)
        
        if results:
            import pandas as pd
            df = pd.DataFrame(results)
            # 按收益率排序
            if '总收益率' in df.columns:
                df = df.sort_values('总收益率', ascending=False)
            return df
        
        return pd.DataFrame()
    
    def validate_strategy(self, strategy_file: str) -> Dict[str, Any]:
        """验证策略文件"""
        result = {
            'valid': False,
            'errors': [],
            'warnings': [],
            'info': {}
        }
        
        try:
            # 检查文件是否存在
            if not os.path.exists(strategy_file):
                result['errors'].append(f"策略文件不存在: {strategy_file}")
                return result
            
            # 尝试导入策略
            import importlib.util
            spec = importlib.util.spec_from_file_location("strategy", strategy_file)
            module = importlib.util.module_from_spec(spec)
            
            try:
                spec.loader.exec_module(module)
            except Exception as e:
                result['errors'].append(f"策略导入失败: {str(e)}")
                return result
            
            # 检查必要的类和方法
            if not hasattr(module, 'Strategy'):
                result['errors'].append("策略文件缺少 Strategy 类")
                return result
            
            strategy_class = getattr(module, 'Strategy')
            
            # 检查必要的方法
            required_methods = ['__init__', 'next']
            for method in required_methods:
                if not hasattr(strategy_class, method):
                    result['errors'].append(f"策略类缺少必要方法: {method}")
            
            # 如果没有错误，标记为有效
            if not result['errors']:
                result['valid'] = True
                
                # 获取策略信息
                if hasattr(strategy_class, '__doc__'):
                    result['info']['description'] = strategy_class.__doc__
                
                if hasattr(strategy_class, 'params'):
                    result['info']['params'] = strategy_class.params
            
            return result
            
        except Exception as e:
            result['errors'].append(f"验证过程出错: {str(e)}")
            return result
    
    def export_results(self, task_id: str, output_format: str = 'excel') -> str:
        """导出回测结果"""
        result = self.get_task_result(task_id)
        if not result:
            return ""
        
        # 生成报告文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        strategy_name = result.get('strategy_name', 'unknown').replace(' ', '_').lower()
        filename = f"backtest_{strategy_name}_{timestamp}"
        
        # 确保导出目录存在
        export_dir = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
            'quant_app',
            'data',
            'exports'
        )
        os.makedirs(export_dir, exist_ok=True)
        
        # 导出路径
        output_path = os.path.join(export_dir, filename)
        
        try:
            if output_format == 'excel':
                # 创建Excel文件
                import pandas as pd
                from openpyxl import Workbook
                from openpyxl.utils.dataframe import dataframe_to_rows
                
                wb = Workbook()
                
                # 总览工作表
                ws = wb.active
                ws.title = "总览"
                
                # 添加任务信息
                ws.append(["任务ID", result.get('task_id', '')])
                ws.append(["策略名称", result.get('strategy_name', '')])
                ws.append(["创建时间", result.get('created_at', '')])
                ws.append(["完成时间", result.get('finished_at', '')])
                ws.append([])
                
                # 添加参数信息
                ws.append(["参数配置"])
                for k, v in result.get('params', {}).items():
                    ws.append([k, v])
                ws.append([])
                
                # 添加性能指标
                ws.append(["性能指标"])
                for k, v in result.get('performance', {}).items():
                    ws.append([k, v])
                
                # 保存文件
                output_path += '.xlsx'
                wb.save(output_path)
                
                return output_path
                
            elif output_format == 'json':
                # 导出为JSON
                output_path += '.json'
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                return output_path
                
            else:
                self.logger.error(f"不支持的导出格式: {output_format}")
                return ""
                
        except Exception as e:
            self.logger.error(f"导出结果失败: {str(e)}")
            return ""


# 创建全局实例
execution_engine = ExecutionEngine() 