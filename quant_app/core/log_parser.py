"""
日志解析器 - 解析任务日志并提取关键数据
"""
import re
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date
from collections import defaultdict
import logging


class LogParser:
    """日志解析器 - 支持多种日志格式"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 定义日志模式
        self.patterns = {
            # 交易相关
            'order': re.compile(
                r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*'
                r'(买入|卖出|BUY|SELL).*'
                r'股票[:：]\s*(\w+).*'
                r'价格[:：]\s*([\d.]+).*'
                r'数量[:：]\s*(\d+)'
            ),
            'trade': re.compile(
                r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*'
                r'成交.*'
                r'股票[:：]\s*(\w+).*'
                r'成交价[:：]\s*([\d.]+).*'
                r'成交量[:：]\s*(\d+)'
            ),
            
            # 资产相关
            'portfolio': re.compile(
                r'(\d{4}-\d{2}-\d{2}).*'
                r'总资产[:：]\s*([\d.]+).*'
                r'可用资金[:：]\s*([\d.]+).*'
                r'持仓市值[:：]\s*([\d.]+)'
            ),
            'position': re.compile(
                r'持仓.*股票[:：]\s*(\w+).*'
                r'数量[:：]\s*(\d+).*'
                r'成本[:：]\s*([\d.]+).*'
                r'现价[:：]\s*([\d.]+)'
            ),
            
            # 策略信号
            'signal': re.compile(
                r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*'
                r'(买入信号|卖出信号|BUY_SIGNAL|SELL_SIGNAL).*'
                r'股票[:：]\s*(\w+)'
            ),
            
            # 性能指标
            'performance': re.compile(
                r'(总收益率|年化收益率|夏普比率|最大回撤|胜率)[:：]\s*([-\d.]+%?)'
            ),
            
            # 错误和警告
            'error': re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*ERROR.*'),
            'warning': re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*WARNING.*')
        }
    
    def parse_log_file(self, log_file_path: str) -> Dict[str, Any]:
        """解析日志文件"""
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            return self.parse_log_lines(lines)
            
        except Exception as e:
            self.logger.error(f"解析日志文件失败: {str(e)}")
            return {}
    
    def parse_log_lines(self, lines: List[str]) -> Dict[str, Any]:
        """解析日志行"""
        results = {
            'orders': [],
            'trades': [],
            'portfolio_history': [],
            'positions': [],
            'signals': [],
            'performance': {},
            'errors': [],
            'warnings': [],
            'summary': {}
        }
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 解析订单
            match = self.patterns['order'].search(line)
            if match:
                results['orders'].append({
                    'datetime': match.group(1),
                    'action': match.group(2),
                    'symbol': match.group(3),
                    'price': float(match.group(4)),
                    'quantity': int(match.group(5))
                })
                continue
            
            # 解析成交
            match = self.patterns['trade'].search(line)
            if match:
                results['trades'].append({
                    'datetime': match.group(1),
                    'symbol': match.group(2),
                    'price': float(match.group(3)),
                    'quantity': int(match.group(4))
                })
                continue
            
            # 解析资产组合
            match = self.patterns['portfolio'].search(line)
            if match:
                results['portfolio_history'].append({
                    'date': match.group(1),
                    'total_value': float(match.group(2)),
                    'cash': float(match.group(3)),
                    'positions_value': float(match.group(4))
                })
                continue
            
            # 解析持仓
            match = self.patterns['position'].search(line)
            if match:
                results['positions'].append({
                    'symbol': match.group(1),
                    'quantity': int(match.group(2)),
                    'cost': float(match.group(3)),
                    'current_price': float(match.group(4))
                })
                continue
            
            # 解析信号
            match = self.patterns['signal'].search(line)
            if match:
                results['signals'].append({
                    'datetime': match.group(1),
                    'signal_type': match.group(2),
                    'symbol': match.group(3)
                })
                continue
            
            # 解析性能指标
            match = self.patterns['performance'].search(line)
            if match:
                key = match.group(1)
                value = match.group(2).replace('%', '')
                results['performance'][key] = float(value)
                continue
            
            # 解析错误
            match = self.patterns['error'].search(line)
            if match:
                results['errors'].append({
                    'datetime': match.group(1),
                    'message': line
                })
                continue
            
            # 解析警告
            match = self.patterns['warning'].search(line)
            if match:
                results['warnings'].append({
                    'datetime': match.group(1),
                    'message': line
                })
        
        # 生成摘要
        results['summary'] = self._generate_summary(results)
        
        return results
    
    def _generate_summary(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成解析摘要"""
        summary = {
            'total_orders': len(parsed_data['orders']),
            'total_trades': len(parsed_data['trades']),
            'total_signals': len(parsed_data['signals']),
            'error_count': len(parsed_data['errors']),
            'warning_count': len(parsed_data['warnings'])
        }
        
        # 计算交易统计
        if parsed_data['trades']:
            trades_df = pd.DataFrame(parsed_data['trades'])
            summary['unique_symbols'] = trades_df['symbol'].nunique()
            summary['total_volume'] = trades_df['quantity'].sum()
            summary['total_value'] = (trades_df['price'] * trades_df['quantity']).sum()
        
        # 计算收益统计
        if parsed_data['portfolio_history']:
            portfolio_df = pd.DataFrame(parsed_data['portfolio_history'])
            portfolio_df['date'] = pd.to_datetime(portfolio_df['date'])
            portfolio_df = portfolio_df.sort_values('date')
            
            if len(portfolio_df) > 1:
                initial_value = portfolio_df.iloc[0]['total_value']
                final_value = portfolio_df.iloc[-1]['total_value']
                summary['total_return'] = (final_value - initial_value) / initial_value * 100
                summary['max_drawdown'] = self._calculate_max_drawdown(
                    portfolio_df['total_value'].values
                )
        
        return summary
    
    def _calculate_max_drawdown(self, values: np.ndarray) -> float:
        """计算最大回撤"""
        peak = values[0]
        max_dd = 0
        
        for value in values:
            if value > peak:
                peak = value
            else:
                dd = (peak - value) / peak * 100
                if dd > max_dd:
                    max_dd = dd
        
        return max_dd
    
    def get_portfolio_dataframe(self, parsed_data: Dict[str, Any]) -> pd.DataFrame:
        """获取资产组合数据框"""
        if not parsed_data.get('portfolio_history'):
            return pd.DataFrame()
        
        df = pd.DataFrame(parsed_data['portfolio_history'])
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date')
        
        # 计算收益率
        df['return'] = df['total_value'].pct_change() * 100
        df['cumulative_return'] = (df['total_value'] / df['total_value'].iloc[0] - 1) * 100
        
        return df
    
    def get_trades_dataframe(self, parsed_data: Dict[str, Any]) -> pd.DataFrame:
        """获取交易数据框"""
        if not parsed_data.get('trades'):
            return pd.DataFrame()
        
        df = pd.DataFrame(parsed_data['trades'])
        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.sort_values('datetime')
        
        # 计算交易价值
        df['value'] = df['price'] * df['quantity']
        
        return df
    
    def get_performance_metrics(self, parsed_data: Dict[str, Any]) -> Dict[str, float]:
        """获取性能指标"""
        metrics = parsed_data.get('performance', {}).copy()
        
        # 从资产组合历史计算额外指标
        portfolio_df = self.get_portfolio_dataframe(parsed_data)
        if not portfolio_df.empty:
            # 年化收益率
            if '年化收益率' not in metrics and len(portfolio_df) > 1:
                days = (portfolio_df['date'].iloc[-1] - portfolio_df['date'].iloc[0]).days
                if days > 0:
                    total_return = portfolio_df['cumulative_return'].iloc[-1] / 100
                    metrics['年化收益率'] = (np.power(1 + total_return, 365/days) - 1) * 100
            
            # 夏普比率
            if '夏普比率' not in metrics and len(portfolio_df) > 30:
                daily_returns = portfolio_df['return'].dropna()
                if len(daily_returns) > 0:
                    metrics['夏普比率'] = (
                        daily_returns.mean() / daily_returns.std() * np.sqrt(252)
                    )
            
            # 最大回撤
            if '最大回撤' not in metrics:
                metrics['最大回撤'] = self._calculate_max_drawdown(
                    portfolio_df['total_value'].values
                )
        
        return metrics
    
    def get_signal_statistics(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """获取信号统计"""
        if not parsed_data.get('signals'):
            return {}
        
        signals_df = pd.DataFrame(parsed_data['signals'])
        
        # 按信号类型统计
        signal_counts = signals_df['signal_type'].value_counts().to_dict()
        
        # 按股票统计
        symbol_counts = signals_df['symbol'].value_counts().to_dict()
        
        return {
            'by_type': signal_counts,
            'by_symbol': symbol_counts,
            'total': len(signals_df)
        }
    
    def analyze_trading_patterns(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析交易模式"""
        trades_df = self.get_trades_dataframe(parsed_data)
        if trades_df.empty:
            return {}
        
        # 按时间分析
        trades_df['hour'] = trades_df['datetime'].dt.hour
        trades_df['weekday'] = trades_df['datetime'].dt.dayofweek
        trades_df['month'] = trades_df['datetime'].dt.month
        
        patterns = {
            'by_hour': trades_df.groupby('hour').size().to_dict(),
            'by_weekday': trades_df.groupby('weekday').size().to_dict(),
            'by_month': trades_df.groupby('month').size().to_dict(),
            'by_symbol': trades_df.groupby('symbol').agg({
                'quantity': 'sum',
                'value': 'sum',
                'price': 'mean'
            }).to_dict('index')
        }
        
        return patterns
    
    def export_to_excel(self, parsed_data: Dict[str, Any], 
                       output_file: str) -> bool:
        """导出解析结果到Excel"""
        try:
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 导出交易记录
                if parsed_data['trades']:
                    trades_df = self.get_trades_dataframe(parsed_data)
                    trades_df.to_excel(writer, sheet_name='交易记录', index=False)
                
                # 导出资产组合历史
                if parsed_data['portfolio_history']:
                    portfolio_df = self.get_portfolio_dataframe(parsed_data)
                    portfolio_df.to_excel(writer, sheet_name='资产历史', index=False)
                
                # 导出性能指标
                metrics = self.get_performance_metrics(parsed_data)
                if metrics:
                    metrics_df = pd.DataFrame(
                        list(metrics.items()), 
                        columns=['指标', '值']
                    )
                    metrics_df.to_excel(writer, sheet_name='性能指标', index=False)
                
                # 导出摘要
                summary_df = pd.DataFrame(
                    list(parsed_data['summary'].items()),
                    columns=['项目', '值']
                )
                summary_df.to_excel(writer, sheet_name='摘要', index=False)
            
            return True
            
        except Exception as e:
            self.logger.error(f"导出Excel失败: {str(e)}")
            return False


# 全局日志解析器实例
log_parser = LogParser() 