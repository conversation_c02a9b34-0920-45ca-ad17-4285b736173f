"""
日志监控模块 - 用于实盘交易页面监控和查看日志文件
"""
import os
import re
import io
import zipfile
from datetime import datetime, timedelta
import streamlit as st
from typing import List, Dict, Tuple, Optional, Any, Union


class LogMonitor:
    """日志监控类，用于查看和分析日志文件"""
    
    def __init__(self, logs_dir: str = None):
        """初始化日志监控器
        
        Args:
            logs_dir: 日志目录，如果为None则使用默认的logs目录
        """
        if logs_dir is None:
            # 使用默认日志目录
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            logs_dir = os.path.join(root_dir, 'logs')
        
        self.logs_dir = logs_dir
        # 确保日志目录存在
        os.makedirs(logs_dir, exist_ok=True)
    
    def get_log_files(self) -> List[str]:
        """获取所有日志文件列表
        
        Returns:
            日志文件名列表
        """
        if not os.path.exists(self.logs_dir):
            return []
        
        log_files = [f for f in os.listdir(self.logs_dir) if f.endswith('.log')]
        # 按修改时间倒序排序
        log_files.sort(key=lambda x: os.path.getmtime(os.path.join(self.logs_dir, x)), reverse=True)
        return log_files
    
    def get_trading_logs(self) -> List[str]:
        """获取交易相关的日志文件列表
        
        Returns:
            交易相关的日志文件名列表
        """
        all_logs = self.get_log_files()
        # 筛选可能与交易相关的日志文件
        trading_logs = [
            log for log in all_logs if any(
                keyword in log for keyword in 
                ['trade', 'stock', 'backtest', 'strategy', 'broker', 'order', 'live']
            )
        ]
        return trading_logs
    
    def read_log_tail(self, log_file: str, lines: int = 100) -> List[str]:
        """读取日志文件的最后n行
        
        Args:
            log_file: 日志文件名
            lines: 要读取的行数
            
        Returns:
            日志行列表
        """
        file_path = os.path.join(self.logs_dir, log_file)
        if not os.path.exists(file_path):
            return []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
            
            # 返回最后n行
            return all_lines[-lines:] if len(all_lines) > lines else all_lines
        except Exception as e:
            print(f"读取日志文件出错: {str(e)}")
            return []
    
    def filter_logs_by_level(self, log_lines: List[str], levels: List[str]) -> List[str]:
        """按日志级别过滤日志行
        
        Args:
            log_lines: 日志行列表
            levels: 要保留的日志级别列表
            
        Returns:
            过滤后的日志行列表
        """
        if not levels:
            return log_lines
        
        # 构建匹配日志级别的正则表达式
        level_pattern = r'\b(' + '|'.join(levels) + r')\b'
        
        # 过滤匹配指定级别的日志行
        filtered_lines = []
        for line in log_lines:
            if re.search(level_pattern, line):
                filtered_lines.append(line)
        
        return filtered_lines
    
    def search_logs(self, log_lines: List[str], keyword: str) -> List[str]:
        """在日志行中搜索关键词
        
        Args:
            log_lines: 日志行列表
            keyword: 搜索关键词
            
        Returns:
            匹配关键词的日志行列表
        """
        if not keyword:
            return log_lines
        
        try:
            pattern = re.compile(keyword, re.IGNORECASE)
            return [line for line in log_lines if pattern.search(line)]
        except re.error:
            # 如果关键词不是有效的正则表达式，则进行普通文本搜索
            return [line for line in log_lines if keyword.lower() in line.lower()]
    
    def format_log_line(self, line: str) -> str:
        """格式化日志行，添加颜色和高亮
        
        Args:
            line: 日志行
            
        Returns:
            格式化后的日志行
        """
        # 如果使用Streamlit显示，可以直接返回原始行
        # 如果需要添加颜色，可以在这里处理
        return line
    
    def get_log_stats(self, log_file: str) -> Dict[str, Any]:
        """获取日志文件的统计信息
        
        Args:
            log_file: 日志文件名
            
        Returns:
            统计信息字典
        """
        file_path = os.path.join(self.logs_dir, log_file)
        if not os.path.exists(file_path):
            return {'error': 'File not found'}
        
        try:
            # 获取文件大小和修改时间
            file_size = os.path.getsize(file_path)
            modified_time = datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S')
            
            # 获取行数
            with open(file_path, 'r', encoding='utf-8') as f:
                line_count = sum(1 for _ in f)
            
            return {
                'size': file_size,
                'size_kb': file_size / 1024,
                'modified_time': modified_time,
                'line_count': line_count,
            }
        except Exception as e:
            return {'error': str(e)}
    
    def get_log_level_stats(self, log_lines: List[str]) -> Dict[str, int]:
        """获取日志级别的统计信息
        
        Args:
            log_lines: 日志行列表
            
        Returns:
            各日志级别的行数统计
        """
        levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        stats = {level: 0 for level in levels}
        
        for line in log_lines:
            for level in levels:
                if f' {level} ' in line:
                    stats[level] += 1
                    break
        
        return stats
    
    def get_recent_errors(self, hours: int = 24) -> List[Tuple[str, str]]:
        """获取最近n小时内的错误日志
        
        Args:
            hours: 要查看的小时数
            
        Returns:
            (文件名, 错误行)的元组列表
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        errors = []
        
        for log_file in self.get_log_files():
            file_path = os.path.join(self.logs_dir, log_file)
            file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
            
            # 只检查最近修改的文件
            if file_mtime >= cutoff_time:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            if ' ERROR ' in line or ' CRITICAL ' in line:
                                errors.append((log_file, line.strip()))
                except Exception:
                    continue
        
        return errors
    
    def clean_old_logs(self, days: int = 7) -> int:
        """清理指定天数之前的旧日志文件
        
        Args:
            days: 保留的天数
            
        Returns:
            清理的文件数量
        """
        cutoff_time = datetime.now() - timedelta(days=days)
        deleted_count = 0
        
        for log_file in self.get_log_files():
            file_path = os.path.join(self.logs_dir, log_file)
            file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
            
            if file_mtime < cutoff_time:
                try:
                    os.remove(file_path)
                    deleted_count += 1
                except Exception:
                    continue
        
        return deleted_count
    
    def export_logs(self, log_files: List[str]) -> Optional[bytes]:
        """导出日志文件为ZIP压缩包
        
        Args:
            log_files: 要导出的日志文件列表
            
        Returns:
            ZIP文件的二进制数据
        """
        if not log_files:
            return None
        
        try:
            # 创建内存中的ZIP文件
            zip_buffer = io.BytesIO()
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for log_file in log_files:
                    file_path = os.path.join(self.logs_dir, log_file)
                    if os.path.exists(file_path):
                        zipf.write(file_path, arcname=log_file)
            
            # 获取ZIP文件的二进制数据
            zip_buffer.seek(0)
            return zip_buffer.getvalue()
        except Exception as e:
            print(f"导出日志文件出错: {str(e)}")
            return None 