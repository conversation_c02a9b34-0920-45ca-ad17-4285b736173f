"""
基于SQLite的配置管理器
提供统一的配置管理，支持从YAML导入和同步到Web界面
"""

import os
import sqlite3
import yaml
import json
from typing import Any, Dict, List, Optional, Union
import logging

# 配置日志
logger = logging.getLogger(__name__)

class DBConfigManager:
    """基于SQLite的配置管理器"""
    
    def __init__(self, db_path: str = None):
        """初始化配置管理器
        
        Args:
            db_path: SQLite数据库路径，如果为None则使用默认路径
        """
        if db_path is None:
            # 默认数据库路径
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            db_path = os.path.join(root_dir, 'quant_app', 'data', 'system.db')
        
        self.db_path = db_path
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        self._ensure_db()
    
    def _get_connection(self):
        """获取数据库连接"""
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        return sqlite3.connect(self.db_path)
    
    def _ensure_db(self):
        """确保数据库和表结构存在"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 创建配置表（如果不存在）
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS config (
                section TEXT NOT NULL,
                key TEXT NOT NULL,
                value TEXT,
                value_type TEXT NOT NULL,
                description TEXT,
                PRIMARY KEY (section, key)
            )
            ''')
            
            # 创建配置分组表（如果不存在）
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS config_sections (
                section TEXT PRIMARY KEY,
                description TEXT,
                display_order INTEGER
            )
            ''')
            
            conn.commit()
            conn.close()
            logger.debug("数据库结构已初始化")
            
        except Exception as e:
            logger.error(f"初始化数据库失败: {str(e)}")
            raise
    
    def get_config(self, section: str, key: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            section: 配置分组
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT value, value_type FROM config WHERE section = ? AND key = ?",
                (section, key)
            )
            result = cursor.fetchone()
            conn.close()
            
            if result:
                value, value_type = result
                return self._convert_value(value, value_type)
            return default
            
        except Exception as e:
            logger.error(f"获取配置 {section}.{key} 失败: {str(e)}")
            return default
    
    def set_config(self, section: str, key: str, value: Any, 
                  description: str = None, value_type: str = None) -> bool:
        """设置配置值
        
        Args:
            section: 配置分组
            key: 配置键
            value: 配置值
            description: 配置描述
            value_type: 值类型，如果为None则自动推断
            
        Returns:
            是否成功
        """
        if value_type is None:
            value_type = self._get_value_type(value)
        
        # 将值转换为字符串
        str_value = self._value_to_str(value)
        
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 确保section存在
            self._ensure_section(section, cursor)
            
            # 插入或更新配置
            cursor.execute('''
            INSERT OR REPLACE INTO config (section, key, value, value_type, description)
            VALUES (?, ?, ?, ?, ?)
            ''', (section, key, str_value, value_type, description))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"设置配置 {section}.{key} 失败: {str(e)}")
            return False
    
    def _ensure_section(self, section: str, cursor = None, description: str = None, 
                       display_order: int = None):
        """确保配置分组存在
        
        Args:
            section: 配置分组
            cursor: 数据库游标，如果为None则创建新连接
            description: 分组描述
            display_order: 显示顺序
        """
        close_conn = False
        if cursor is None:
            conn = self._get_connection()
            cursor = conn.cursor()
            close_conn = True
        
        cursor.execute("SELECT 1 FROM config_sections WHERE section = ?", (section,))
        if not cursor.fetchone():
            cursor.execute('''
            INSERT INTO config_sections (section, description, display_order)
            VALUES (?, ?, ?)
            ''', (section, description, display_order))
        elif description is not None or display_order is not None:
            update_sql = "UPDATE config_sections SET "
            params = []
            
            if description is not None:
                update_sql += "description = ?"
                params.append(description)
            
            if display_order is not None:
                if description is not None:
                    update_sql += ", "
                update_sql += "display_order = ?"
                params.append(display_order)
            
            update_sql += " WHERE section = ?"
            params.append(section)
            
            cursor.execute(update_sql, params)
        
        if close_conn:
            conn.commit()
            conn.close()
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """获取指定分组的所有配置
        
        Args:
            section: 配置分组
            
        Returns:
            配置字典
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT key, value, value_type FROM config WHERE section = ?",
                (section,)
            )
            results = cursor.fetchall()
            conn.close()
            
            config_dict = {}
            for key, value, value_type in results:
                config_dict[key] = self._convert_value(value, value_type)
            
            return config_dict
            
        except Exception as e:
            logger.error(f"获取配置分组 {section} 失败: {str(e)}")
            return {}
    
    def get_all_config(self) -> Dict[str, Dict[str, Any]]:
        """获取所有配置
        
        Returns:
            所有配置，格式为 {section: {key: value, ...}, ...}
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT section, key, value, value_type FROM config"
            )
            results = cursor.fetchall()
            conn.close()
            
            config_dict = {}
            for section, key, value, value_type in results:
                if section not in config_dict:
                    config_dict[section] = {}
                config_dict[section][key] = self._convert_value(value, value_type)
            
            return config_dict
            
        except Exception as e:
            logger.error(f"获取所有配置失败: {str(e)}")
            return {}
    
    def get_sections(self) -> List[Dict[str, Any]]:
        """获取所有配置分组
        
        Returns:
            配置分组列表，每个元素为 {section, description, display_order}
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT section, description, display_order FROM config_sections ORDER BY display_order"
            )
            results = cursor.fetchall()
            conn.close()
            
            sections = []
            for section, description, display_order in results:
                sections.append({
                    'section': section,
                    'description': description,
                    'display_order': display_order
                })
            
            return sections
            
        except Exception as e:
            logger.error(f"获取配置分组失败: {str(e)}")
            return []
    
    def delete_config(self, section: str, key: str) -> bool:
        """删除配置
        
        Args:
            section: 配置分组
            key: 配置键
            
        Returns:
            是否成功
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            cursor.execute(
                "DELETE FROM config WHERE section = ? AND key = ?",
                (section, key)
            )
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"删除配置 {section}.{key} 失败: {str(e)}")
            return False
    
    def clear_section(self, section: str) -> bool:
        """清空配置分组
        
        Args:
            section: 配置分组
            
        Returns:
            是否成功
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            cursor.execute(
                "DELETE FROM config WHERE section = ?",
                (section,)
            )
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"清空配置分组 {section} 失败: {str(e)}")
            return False
    
    def import_from_yaml(self, yaml_path: str) -> bool:
        """从YAML文件导入配置
        
        Args:
            yaml_path: YAML文件路径
            
        Returns:
            是否成功
        """
        try:
            # 读取YAML文件
            with open(yaml_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            if not isinstance(config_data, dict):
                logger.error(f"YAML文件格式错误: {yaml_path}")
                return False
            
            # 开始导入
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 设置配置分组和显示顺序
            for i, (section, section_data) in enumerate(config_data.items()):
                self._ensure_section(section, cursor, 
                                    description=f"{section} 配置", 
                                    display_order=i+1)
                
                # 导入配置项
                self._import_section(section, section_data, cursor)
            
            conn.commit()
            conn.close()
            
            logger.info(f"成功从YAML导入配置: {yaml_path}")
            return True
            
        except Exception as e:
            logger.error(f"从YAML导入配置失败: {str(e)}")
            return False
    
    def _import_section(self, section: str, data: Dict, cursor, parent_key: str = ""):
        """递归导入配置分组
        
        Args:
            section: 配置分组
            data: 配置数据
            cursor: 数据库游标
            parent_key: 父级键，用于处理嵌套配置
        """
        for key, value in data.items():
            full_key = f"{parent_key}.{key}" if parent_key else key
            
            if isinstance(value, dict):
                # 递归处理嵌套配置
                self._import_section(section, value, cursor, full_key)
            else:
                # 处理值
                value_type = self._get_value_type(value)
                str_value = self._value_to_str(value)
                
                # 插入或更新配置
                cursor.execute('''
                INSERT OR REPLACE INTO config (section, key, value, value_type, description)
                VALUES (?, ?, ?, ?, ?)
                ''', (section, full_key, str_value, value_type, None))
    
    def export_to_yaml(self, yaml_path: str) -> bool:
        """导出配置到YAML文件
        
        Args:
            yaml_path: YAML文件路径
            
        Returns:
            是否成功
        """
        try:
            # 获取所有配置
            config_data = self.get_all_config()
            
            # 将扁平化的键值对转换为嵌套结构
            nested_config = {}
            for section, section_data in config_data.items():
                nested_config[section] = {}
                for key, value in section_data.items():
                    parts = key.split('.')
                    current = nested_config[section]
                    for i, part in enumerate(parts):
                        if i == len(parts) - 1:
                            current[part] = value
                        else:
                            if part not in current:
                                current[part] = {}
                            current = current[part]
            
            # 确保导出目录存在
            os.makedirs(os.path.dirname(yaml_path), exist_ok=True)
            
            # 导出到YAML文件
            with open(yaml_path, 'w', encoding='utf-8') as f:
                yaml.dump(nested_config, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"成功导出配置到YAML: {yaml_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出配置到YAML失败: {str(e)}")
            return False
    
    @staticmethod
    def _get_value_type(value: Any) -> str:
        """获取值的类型名称
        
        Args:
            value: 要检查类型的值
            
        Returns:
            类型名称
        """
        if value is None:
            return "null"
        elif isinstance(value, bool):
            return "bool"
        elif isinstance(value, int):
            return "int"
        elif isinstance(value, float):
            return "float"
        elif isinstance(value, str):
            return "str"
        elif isinstance(value, list):
            return "list"
        elif isinstance(value, dict):
            return "dict"
        else:
            return "str"  # 默认为字符串
    
    @staticmethod
    def _value_to_str(value: Any) -> str:
        """将值转换为字符串
        
        Args:
            value: 要转换的值
            
        Returns:
            字符串表示
        """
        if value is None:
            return "null"
        elif isinstance(value, (list, dict)):
            return json.dumps(value, ensure_ascii=False)
        else:
            return str(value)
    
    @staticmethod
    def _convert_value(value_str: str, value_type: str) -> Any:
        """将字符串值转换为指定类型
        
        Args:
            value_str: 字符串值
            value_type: 类型名称
            
        Returns:
            转换后的值
        """
        if value_str == "null" and value_type == "null":
            return None
        elif value_type == "bool":
            return value_str.lower() == "true"
        elif value_type == "int":
            return int(value_str)
        elif value_type == "float":
            return float(value_str)
        elif value_type == "str":
            return value_str
        elif value_type in ("list", "dict"):
            return json.loads(value_str)
        else:
            return value_str

# 创建全局实例
db_config_manager = DBConfigManager() 