"""
配置管理器 - 使用SQLite数据库管理系统配置
"""
import sqlite3
import json
import os
import yaml
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
import threading
from contextlib import contextmanager

# 导入数据库配置管理器
from quant_app.core.db_config_manager import db_config_manager


class ConfigManager:
    """配置管理器 - 包装db_config_manager，保持向后兼容性"""
    
    def __init__(self, db_path: str = "quant_app/data/system.db"):
        self.db_path = db_path
        # 不再需要初始化数据库，现在使用db_config_manager
        self._init_default_configs()
    
    def _init_default_configs(self):
        """初始化默认配置"""
        # 检查数据库是否为空，如果为空则从YAML导入
        sections = db_config_manager.get_sections()
        if not sections:
            # 尝试从YAML导入
            yaml_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config.yaml')
            if os.path.exists(yaml_path):
                try:
                    print(f"从YAML导入配置: {yaml_path}")
                    db_config_manager.import_from_yaml(yaml_path)
                except Exception as e:
                    print(f"从YAML导入配置失败: {str(e)}")
            else:
                print(f"YAML配置文件不存在: {yaml_path}")
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        # 拆分section和key
        parts = key.split('.')
        if len(parts) > 1:
            section = parts[0]
            real_key = '.'.join(parts[1:])
        else:
            # 兼容旧的扁平键
            section = 'general'
            real_key = key
        
        return db_config_manager.get_config(section, real_key, default)
    
    def set_config(self, key: str, value: Any, description: str = None, 
                   category: str = 'general', changed_by: str = 'user') -> bool:
        """设置配置值"""
        # 拆分section和key
        parts = key.split('.')
        if len(parts) > 1:
            section = parts[0]
            real_key = '.'.join(parts[1:])
        else:
            # 使用category作为section
            section = category
            real_key = key
        
        return db_config_manager.set_config(section, real_key, value, description)
    
    def get_configs_by_category(self, category: str) -> Dict[str, Any]:
        """按类别获取所有配置"""
        return db_config_manager.get_section(category)
    
    def get_all_configs(self) -> List[Dict[str, Any]]:
        """获取所有配置信息"""
        # 获取所有配置
        all_configs = db_config_manager.get_all_config()
        
        # 转换成旧格式
        configs = []
        for section, section_data in all_configs.items():
            for key, value in section_data.items():
                configs.append({
                    'key': f"{section}.{key}",
                    'value': str(value),
                    'description': None,  # DBConfigManager中的description在get_all_config()中不可用
                    'category': section,
                    'data_type': db_config_manager._get_value_type(value),
                    'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
        
        return configs
    
    def get_config_history(self, key: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取配置修改历史"""
        # DBConfigManager不支持历史记录，返回空列表
        return []
    
    def delete_config(self, key: str) -> bool:
        """删除配置"""
        # 拆分section和key
        parts = key.split('.')
        if len(parts) > 1:
            section = parts[0]
            real_key = '.'.join(parts[1:])
        else:
            # 默认section
            section = 'general'
            real_key = key
        
        return db_config_manager.delete_config(section, real_key)
    
    def export_configs(self) -> Dict[str, Any]:
        """导出所有配置"""
        # 获取所有配置
        all_configs = db_config_manager.get_all_config()
        
        # 转换成旧格式
        configs = {}
        for section, section_data in all_configs.items():
            for key, value in section_data.items():
                full_key = f"{section}.{key}"
                configs[full_key] = {
                    'value': value,
                    'description': None,
                    'category': section
                }
        
        return configs
    
    def import_configs(self, configs: Dict[str, Any], overwrite: bool = False):
        """导入配置"""
        for key, config in configs.items():
            # 拆分section和key
            parts = key.split('.')
            if len(parts) > 1:
                section = parts[0]
                real_key = '.'.join(parts[1:])
            else:
                # 使用category作为section
                section = config.get('category', 'general')
                real_key = key
            
            if overwrite or db_config_manager.get_config(section, real_key) is None:
                db_config_manager.set_config(
                    section=section,
                    key=real_key,
                    value=config.get('value'),
                    description=config.get('description')
                )
    
    def import_from_yaml(self, yaml_path: str) -> bool:
        """从YAML导入配置"""
        return db_config_manager.import_from_yaml(yaml_path)
    
    def export_to_yaml(self, yaml_path: str) -> bool:
        """导出配置到YAML"""
        return db_config_manager.export_to_yaml(yaml_path)


# 全局配置管理器实例
config_manager = ConfigManager()


# 便捷函数
def get_config(key: str, default: Any = None) -> Any:
    """获取配置值"""
    return config_manager.get_config(key, default)


def set_config(key: str, value: Any, **kwargs) -> bool:
    """设置配置值"""
    return config_manager.set_config(key, value, **kwargs) 