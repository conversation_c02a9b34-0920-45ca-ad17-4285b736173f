# 量化交易系统 Web界面使用指南

## 系统概述

本系统是基于Streamlit构建的量化交易可视化管理平台，提供了友好的Web界面来管理策略回测、实盘交易、任务监控和数据分析等功能。

## 功能特点

- 📊 **实时监控**: 系统总览页面实时显示账户状态、市场概况和任务统计
- 🎯 **策略回测**: 可视化配置和运行策略回测，支持参数优化
- 📋 **任务管理**: 统一管理所有回测和交易任务，支持并发控制
- 📈 **深度分析**: 详细的交易分析报告，包括收益曲线、风险指标等
- ⚙️ **配置管理**: 基于SQLite的配置管理，支持导入导出和历史追踪

## 快速开始

### 1. 系统要求

- Python 3.8 或更高版本
- Windows/Linux/MacOS 操作系统
- 至少 4GB 内存
- 稳定的网络连接（用于安装依赖）

### 2. 安装步骤

#### Windows用户

1. 双击运行 `启动Web界面.bat`
2. 系统会自动检查并安装依赖
3. 浏览器会自动打开界面

#### 其他系统用户

```bash
# 安装依赖
pip install -r quant_app/requirements.txt

# 启动系统
python run_web_app.py
```

### 3. 首次使用

1. **系统设置**: 首次使用请先进入"系统设置"页面，配置交易接口和基本参数
2. **数据准备**: 确保历史数据文件（.pkl, .sqlite）在正确位置
3. **策略选择**: 在"策略回测"页面选择要测试的策略

## 页面功能说明

### 📊 总览 (Dashboard)

显示系统的整体状态：
- 账户资产概览
- 市场指数行情
- 任务执行统计
- 系统资源使用情况

### 🎯 策略回测

策略回测的完整流程：
1. 选择策略文件
2. 设置回测时间范围
3. 配置资金和风控参数
4. 调整策略特定参数
5. 点击"开始回测"运行

### 📋 任务管理

任务的生命周期管理：
- 查看所有任务状态
- 实时监控运行日志
- 停止或取消任务
- 批量操作和导出

### 📈 分析报告

详细的结果分析：
- 性能指标总览
- 收益曲线图表
- 交易明细记录
- 风险分析指标
- 交易模式统计

### ⚙️ 系统设置

配置管理中心：
- **交易接口**: QMT API连接配置
- **风险管理**: 仓位控制和止损设置
- **回测设置**: 初始资金和交易成本
- **系统参数**: 日志级别和性能设置
- **配置管理**: 导入导出配置文件

## 高级功能

### 策略参数优化

1. 在策略回测页面展开"参数优化设置"
2. 设置参数范围（如KDJ周期：9-21）
3. 选择优化目标（总收益率、夏普比率等）
4. 设置并行任务数
5. 点击"开始优化"批量测试

### 自定义策略

1. 将策略文件放入 `quant_app/strategies/` 目录
2. 确保策略文件包含必要的类和方法
3. 重启系统后即可在界面中选择

### 日志分析

系统会自动解析任务日志，提取：
- 交易记录
- 资产变化
- 策略信号
- 错误警告

## 注意事项

1. **数据安全**: 所有配置存储在本地SQLite数据库中
2. **并发限制**: 默认最多同时运行5个任务，可在设置中调整
3. **内存使用**: 大量历史数据可能占用较多内存
4. **浏览器兼容**: 推荐使用Chrome、Firefox等现代浏览器

## 故障排除

### 常见问题

**Q: 无法启动Web界面**
- 检查Python版本是否满足要求
- 确认端口8501未被占用
- 查看控制台错误信息

**Q: 策略文件无法识别**
- 确认策略文件在正确目录
- 检查策略文件语法是否正确
- 查看任务日志中的错误信息

**Q: 数据加载失败**
- 检查数据文件是否存在
- 确认文件格式正确（.pkl, .sqlite）
- 查看数据处理器日志

### 技术支持

如遇到问题，请：
1. 查看任务日志文件 (`quant_app/data/logs/`)
2. 检查系统数据库 (`quant_app/data/system.db`)
3. 保存错误截图和日志信息

## 更新日志

### v2.0.0 (2024-01)
- 全新的Streamlit Web界面
- 基于SQLite的配置管理
- 优雅的进程管理系统
- 增强的日志分析功能
- 支持任务并发控制

## 开发计划

- [ ] 实时行情推送
- [ ] 策略性能对比
- [ ] 自动报告生成
- [ ] 移动端适配
- [ ] 多账户管理

---

*本系统基于QMT量化交易框架开发* 