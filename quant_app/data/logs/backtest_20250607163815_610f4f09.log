usage: run_backtest.py [-h] [--stock-code STOCK_CODE]
                       [--initial-cash INITIAL_CASH] [--live]
                       [--start-time START_TIME] [--end-time END_TIME]
                       [--img-dir IMG_DIR] [--log-dir LOG_DIR]
                       [--backtest-date BACKTEST_DATE]
                       [--config-path CONFIG_PATH] [--strategy STRATEGY]
                       [--list-strategies]
run_backtest.py: error: unrecognized arguments: --log-file quant_app/data/logs\backtest_20250607163815_610f4f09.log --initial-capital 1000000 --commission-rate 2.9999999999999997e-06 --slippage 2e-05 --start-date 2024-06-07 --end-date 2025-06-06 --rsi-period 14 --rsi-oversold 30 --rsi-overbought 70
