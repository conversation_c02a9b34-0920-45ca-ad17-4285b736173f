"""
任务管理页面 - 管理和监控所有任务
"""
import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from quant_app.core.task_manager import task_manager, TaskType, TaskStatus
from quant_app.core.execution_engine import execution_engine
from quant_app.core.log_parser import log_parser

# 页面配置
st.set_page_config(page_title="任务管理 - 量化交易系统", page_icon="📋", layout="wide")

# 标题
st.title("📋 任务管理")
st.markdown("统一管理所有回测和交易任务")
st.markdown("---")

# 侧边栏 - 过滤选项
st.sidebar.header("过滤选项")

# 任务类型过滤
task_type_filter = st.sidebar.multiselect(
    "任务类型",
    options=[t.value for t in TaskType],
    default=[t.value for t in TaskType],
    format_func=lambda x: {
        'backtest': '回测',
        'trading': '交易',
        'data_update': '数据更新',
        'analysis': '分析'
    }.get(x, x)
)

# 任务状态过滤
status_filter = st.sidebar.multiselect(
    "任务状态",
    options=[s.value for s in TaskStatus],
    default=[s.value for s in TaskStatus],
    format_func=lambda x: {
        'pending': '等待中',
        'running': '运行中',
        'completed': '已完成',
        'failed': '失败',
        'cancelled': '已取消'
    }.get(x, x)
)

# 时间范围过滤
date_range = st.sidebar.date_input(
    "时间范围",
    value=(datetime.now().date(), datetime.now().date()),
    max_value=datetime.now().date()
)

# 自动刷新
auto_refresh = st.sidebar.checkbox("自动刷新", value=False)
refresh_interval = st.sidebar.slider("刷新间隔(秒)", 5, 60, 10) if auto_refresh else None

if auto_refresh:
    import time
    time.sleep(refresh_interval)
    st.rerun()

# 主页面
# 1. 任务概览
st.header("📊 任务概览")

# 统计数据
try:
    stats = task_manager.get_task_statistics()

    # 任务状态分布
    st.subheader("任务状态分布")
    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        status_stats = stats.get('by_status', {})
        status_data = []
        
        for status, count in status_stats.items():
            status_name = {
                'pending': '等待中',
                'running': '运行中',
                'completed': '已完成',
                'failed': '失败',
                'cancelled': '已取消'
            }.get(status, status)
            
            status_data.append({
                '状态': status_name,
                '数量': count
            })
        
        if status_data:
            status_df = pd.DataFrame(status_data)
            fig = px.bar(
                status_df, 
                x='状态', 
                y='数量',
                color='状态',
                color_discrete_map={
                    '等待中': '#ffc107',
                    '运行中': '#007bff',
                    '已完成': '#28a745',
                    '失败': '#dc3545',
                    '已取消': '#6c757d'
                }
            )
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("暂无任务状态数据")

    with col2:
        st.metric(
            "今日任务数", 
            stats.get('today', {}).get('total', 0)
        )
        st.metric(
            "运行中任务", 
            stats.get('by_status', {}).get('running', 0)
        )

    with col3:
        st.metric(
            "完成率", 
            f"{stats.get('today', {}).get('completed', 0) / max(stats.get('today', {}).get('total', 1), 1) * 100:.0f}%"
        )
        st.metric(
            "失败率", 
            f"{stats.get('today', {}).get('failed', 0) / max(stats.get('today', {}).get('total', 1), 1) * 100:.0f}%",
            delta_color="inverse"
        )
except Exception as e:
    st.error(f"获取任务统计信息失败: {str(e)}")
    st.info("系统运行正常，但任务统计数据暂时无法显示。这可能是因为数据库结构变更或者数据库文件不存在。")

# 2. 任务列表
st.header("📋 任务列表")

# 获取任务列表
all_tasks = []
for task_type in task_type_filter:
    for status in status_filter:
        tasks = task_manager.list_tasks(
            task_type=TaskType(task_type) if task_type else None,
            status=TaskStatus(status) if status else None,
            limit=1000
        )
        all_tasks.extend(tasks)

# 去重 - 使用task_id作为唯一标识
unique_tasks = {task['task_id']: task for task in all_tasks if 'task_id' in task}.values()
tasks_list = list(unique_tasks)

if tasks_list:
    # 转换为DataFrame
    df = pd.DataFrame(tasks_list)
    
    # 数据处理
    df['created_at'] = pd.to_datetime(df['created_at'])
    
    # 时间过滤
    if len(date_range) == 2:
        start_date = pd.Timestamp(date_range[0])
        end_date = pd.Timestamp(date_range[1]) + pd.Timedelta(days=1)
        df = df[(df['created_at'] >= start_date) & (df['created_at'] < end_date)]
    
    # 排序
    df = df.sort_values('created_at', ascending=False)
    
    # 选择显示的列 - 根据实际返回的列名调整
    display_columns = ['task_id', 'task_type', 'title', 'status', 
                     'created_at', 'started_at', 'completed_at', 'error']
    
    # 确保所有列都存在
    for col in display_columns:
        if col not in df.columns:
            df[col] = None
    
    # 创建显示DataFrame
    display_df = df[display_columns].copy()
    
    # 重命名列
    display_df.columns = ['任务ID', '类型', '任务名称', '状态', 
                         '创建时间', '开始时间', '完成时间', '错误信息']
    
    # 格式化
    type_map = {
        'backtest': '回测',
        'trading': '交易',
        'data_update': '数据更新',
        'analysis': '分析'
    }
    display_df['类型'] = display_df['类型'].map(type_map)
    
    status_map = {
        'pending': '🟡 等待中',
        'running': '🔵 运行中',
        'completed': '🟢 已完成',
        'failed': '🔴 失败',
        'cancelled': '⚫ 已取消'
    }
    display_df['状态'] = display_df['状态'].map(status_map)
    
    # 时间格式化
    for col in ['创建时间', '开始时间', '完成时间']:
        display_df[col] = pd.to_datetime(display_df[col]).dt.strftime('%Y-%m-%d %H:%M:%S')
        display_df[col] = display_df[col].fillna('-')
    
    # 错误信息处理
    display_df['错误信息'] = display_df['错误信息'].fillna('-')
    
    # 显示表格
    st.dataframe(
        display_df,
        use_container_width=True,
        hide_index=True
    )
    
    # 批量操作
    st.subheader("批量操作")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🛑 停止任务", type="secondary"):
            st.warning("请在任务详情中停止特定任务")
    
    with col2:
        if st.button("🗑️ 删除任务", type="secondary"):
            st.warning("删除功能暂未实现")
    
    with col3:
        if st.button("📊 分析任务", type="primary"):
            st.warning("请在任务详情中分析特定任务")
    
    with col4:
        if st.button("📥 导出任务数据"):
            # 导出功能
            export_df = df[['task_id', 'task_type', 'strategy_name', 
                           'status', 'created_at', 'finished_at']]
            csv = export_df.to_csv(index=False)
            st.download_button(
                label="下载CSV",
                data=csv,
                file_name=f"tasks_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
else:
    st.info("没有找到符合条件的任务")

# 3. 任务详情
st.header("🔍 任务详情")

# 任务ID输入
task_id_input = st.text_input("输入任务ID查看详情", placeholder="例如: backtest_20240101120000_abcd1234")

if task_id_input:
    task_info = task_manager.get_task(task_id_input)
    
    if task_info:
        # 基本信息
        col1, col2 = st.columns([1, 2])
        
        with col1:
            st.subheader("基本信息")
            st.write(f"**任务ID**: {task_info['task_id']}")
            st.write(f"**类型**: {type_map.get(task_info['task_type'], task_info['task_type'])}")
            st.write(f"**策略**: {task_info['strategy_name'] or '-'}")
            st.write(f"**状态**: {status_map.get(task_info['status'], task_info['status'])}")
            st.write(f"**创建者**: {task_info['created_by']}")
            
            # 操作按钮
            if task_info['status'] == 'running':
                if st.button("🛑 停止任务", key=f"stop_{task_id_input}"):
                    if task_manager.stop_task(task_id_input):
                        st.success("任务已停止")
                        st.rerun()
            
            if task_info['status'] in ['completed', 'failed']:
                if st.button("📊 查看分析", key=f"analyze_{task_id_input}"):
                    st.session_state['analyze_task_id'] = task_id_input
                    st.switch_page("pages/4_📈_分析报告.py")
        
        with col2:
            # 参数信息
            st.subheader("任务参数")
            params = json.loads(task_info['params'])
            if params:
                params_df = pd.DataFrame(list(params.items()), columns=['参数', '值'])
                st.dataframe(params_df, hide_index=True, use_container_width=True)
            else:
                st.write("无参数")
            
            # 时间信息
            st.subheader("时间信息")
            time_info = {
                '创建时间': task_info['created_at'],
                '开始时间': task_info['started_at'] or '-',
                '完成时间': task_info['finished_at'] or '-'
            }
            
            # 计算执行时长
            if task_info['started_at'] and task_info['finished_at']:
                start = pd.to_datetime(task_info['started_at'])
                end = pd.to_datetime(task_info['finished_at'])
                duration = end - start
                time_info['执行时长'] = str(duration)
            
            time_df = pd.DataFrame(list(time_info.items()), columns=['时间', '值'])
            st.dataframe(time_df, hide_index=True, use_container_width=True)
        
        # 日志查看
        st.subheader("任务日志")
        
        log_lines = st.slider("显示日志行数", 10, 1000, 100, step=10)
        
        if st.button("🔄 刷新日志", key=f"refresh_log_{task_id_input}"):
            pass  # 触发重新加载
        
        task_logs = task_manager.get_task_log(task_id_input, lines=log_lines)
        
        if task_logs:
            # 日志高亮
            log_text = '\n'.join(task_logs)
            st.code(log_text, language='log')
            
            # 下载日志
            st.download_button(
                label="📥 下载完整日志",
                data='\n'.join(task_manager.get_task_log(task_id_input, lines=-1)),
                file_name=f"{task_id_input}_log.txt",
                mime="text/plain"
            )
        else:
            st.info("暂无日志信息")
        
        # 错误信息
        if task_info['error_message']:
            st.error(f"错误信息: {task_info['error_message']}")
    else:
        st.warning(f"未找到任务: {task_id_input}")

# 4. 系统维护
st.header("🛠️ 系统维护")

col1, col2, col3 = st.columns(3)

with col1:
    st.subheader("清理旧任务")
    days_to_keep = st.number_input("保留天数", min_value=1, max_value=365, value=30)
    if st.button("清理旧任务", type="secondary"):
        cleaned = task_manager.clean_old_tasks(days=days_to_keep)
        st.success(f"已清理 {cleaned} 个旧任务")
        st.rerun()

with col2:
    st.subheader("系统信息")
    st.write(f"数据库路径: {task_manager.db_path}")
    st.write(f"最大并发数: {task_manager.max_workers}")
    
    # 获取数据库大小
    if os.path.exists(task_manager.db_path):
        db_size = os.path.getsize(task_manager.db_path) / 1024 / 1024
        st.write(f"数据库大小: {db_size:.2f} MB")

with col3:
    st.subheader("快速操作")
    if st.button("🛑 停止所有任务", type="secondary"):
        running = task_manager.get_running_tasks()
        stopped = 0
        for task in running:
            if task_manager.stop_task(task['task_id']):
                stopped += 1
        st.success(f"已停止 {stopped} 个任务")
        st.rerun() 