"""
参数优化页面 - 优化策略参数并分析结果
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import sys
import os
import json
import time
import subprocess
import itertools

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

# 导入项目模块
from quant_app.core.db_config_manager import db_config_manager
from quant_app.core.task_manager import task_manager, TaskType, TaskStatus
from quant_app.core.data_handler import data_handler

# 页面配置
st.set_page_config(page_title="参数优化 - 量化交易系统", page_icon="⚙️", layout="wide")

# 标题
st.title("⚙️ 参数优化")
st.markdown("优化策略参数，提高回测表现")
st.markdown("---")

# 获取可用策略
try:
    strategies = data_handler.get_strategies()
    strategy_options = {strategy['name']: strategy for strategy in strategies}
except Exception as e:
    st.error(f"获取策略列表失败: {str(e)}")
    strategy_options = {}

# 侧边栏 - 优化设置
st.sidebar.header("优化设置")

# 选择策略
selected_strategy_name = st.sidebar.selectbox(
    "选择策略", 
    options=list(strategy_options.keys()),
    help="选择要优化的策略"
)

if not selected_strategy_name:
    st.warning("请选择一个策略进行优化")
    st.stop()

selected_strategy = strategy_options.get(selected_strategy_name, {})

# 选择股票
try:
    all_stocks = data_handler.get_selected_stocks()
except Exception as e:
    st.sidebar.error(f"获取股票列表失败: {str(e)}")
    all_stocks = []

if not all_stocks:
    st.warning("没有找到可用的股票，请先在实盘交易页面添加股票")
    st.stop()

selected_stocks = st.sidebar.multiselect(
    "选择用于优化的股票",
    options=all_stocks,
    default=[all_stocks[0]] if all_stocks else None,
    help="选择1-3只股票进行参数优化（股票越多，优化时间越长）"
)

if not selected_stocks:
    st.warning("请至少选择一只股票进行优化")
    st.stop()

# 时间范围设置
st.sidebar.subheader("回测时间范围")

# 从配置中获取默认时间范围
default_start = db_config_manager.get_config('backtest', 'start_time', '20240101093000')
default_end = db_config_manager.get_config('backtest', 'end_time', '20240331150000')

# 格式化为日期
try:
    default_start_date = datetime.strptime(default_start[:8], '%Y%m%d').date()
    default_end_date = datetime.strptime(default_end[:8], '%Y%m%d').date()
except:
    default_start_date = datetime.now().date() - timedelta(days=90)
    default_end_date = datetime.now().date()

start_date = st.sidebar.date_input(
    "开始日期",
    value=default_start_date,
    min_value=datetime(2020, 1, 1).date(),
    max_value=datetime.now().date()
)

end_date = st.sidebar.date_input(
    "结束日期",
    value=default_end_date,
    min_value=start_date,
    max_value=datetime.now().date()
)

# 参数设置
st.sidebar.subheader("参数范围设置")

# 获取策略默认参数
default_params = {}
if selected_strategy and 'parameters' in selected_strategy:
    default_params = selected_strategy['parameters']

# 优化参数设置
st.sidebar.info("请为每个参数设置优化范围")

param_ranges = {}
max_combinations = 1

# 根据策略类型获取可优化参数
if 'kdj' in selected_strategy_name.lower():
    # KDJ策略参数
    kdj_period_min = st.sidebar.number_input("KDJ周期最小值", min_value=5, max_value=21, value=9)
    kdj_period_max = st.sidebar.number_input("KDJ周期最大值", min_value=kdj_period_min, max_value=21, value=14)
    kdj_period_step = st.sidebar.number_input("KDJ周期步长", min_value=1, max_value=5, value=1)
    kdj_periods = list(range(kdj_period_min, kdj_period_max + 1, kdj_period_step))
    param_ranges['kdj_period'] = kdj_periods
    
    j_oversold_min = st.sidebar.number_input("J超卖阈值最小值", min_value=0, max_value=30, value=10)
    j_oversold_max = st.sidebar.number_input("J超卖阈值最大值", min_value=j_oversold_min, max_value=30, value=20)
    j_oversold_step = st.sidebar.number_input("J超卖阈值步长", min_value=1, max_value=5, value=5)
    j_oversolds = list(range(j_oversold_min, j_oversold_max + 1, j_oversold_step))
    param_ranges['j_oversold'] = j_oversolds
    
    j_overbought_min = st.sidebar.number_input("J超买阈值最小值", min_value=70, max_value=100, value=80)
    j_overbought_max = st.sidebar.number_input("J超买阈值最大值", min_value=j_overbought_min, max_value=100, value=90)
    j_overbought_step = st.sidebar.number_input("J超买阈值步长", min_value=1, max_value=5, value=5)
    j_overboughts = list(range(j_overbought_min, j_overbought_max + 1, j_overbought_step))
    param_ranges['j_overbought'] = j_overboughts
    
    volume_ma_min = st.sidebar.number_input("成交量MA周期最小值", min_value=5, max_value=30, value=10)
    volume_ma_max = st.sidebar.number_input("成交量MA周期最大值", min_value=volume_ma_min, max_value=30, value=20)
    volume_ma_step = st.sidebar.number_input("成交量MA周期步长", min_value=1, max_value=5, value=5)
    volume_mas = list(range(volume_ma_min, volume_ma_max + 1, volume_ma_step))
    param_ranges['volume_ma_period'] = volume_mas
    
elif 'triple' in selected_strategy_name.lower() or 'resonance' in selected_strategy_name.lower():
    # 三重共振策略参数
    rsi_period_min = st.sidebar.number_input("RSI周期最小值", min_value=2, max_value=14, value=3)
    rsi_period_max = st.sidebar.number_input("RSI周期最大值", min_value=rsi_period_min, max_value=14, value=5)
    rsi_period_step = st.sidebar.number_input("RSI周期步长", min_value=1, max_value=2, value=1)
    rsi_periods = list(range(rsi_period_min, rsi_period_max + 1, rsi_period_step))
    param_ranges['rsi_period'] = rsi_periods
    
    rsi_upper_min = st.sidebar.number_input("RSI上限最小值", min_value=50, max_value=90, value=70)
    rsi_upper_max = st.sidebar.number_input("RSI上限最大值", min_value=rsi_upper_min, max_value=90, value=80)
    rsi_upper_step = st.sidebar.number_input("RSI上限步长", min_value=1, max_value=10, value=5)
    rsi_uppers = list(range(rsi_upper_min, rsi_upper_max + 1, rsi_upper_step))
    param_ranges['rsi_upper'] = rsi_uppers
    
    rsi_lower_min = st.sidebar.number_input("RSI下限最小值", min_value=10, max_value=50, value=20)
    rsi_lower_max = st.sidebar.number_input("RSI下限最大值", min_value=rsi_lower_min, max_value=50, value=30)
    rsi_lower_step = st.sidebar.number_input("RSI下限步长", min_value=1, max_value=10, value=5)
    rsi_lowers = list(range(rsi_lower_min, rsi_lower_max + 1, rsi_lower_step))
    param_ranges['rsi_lower'] = rsi_lowers
    
    macd_fast_min = st.sidebar.number_input("MACD快线最小值", min_value=5, max_value=20, value=12)
    macd_fast_max = st.sidebar.number_input("MACD快线最大值", min_value=macd_fast_min, max_value=20, value=15)
    macd_fast_step = st.sidebar.number_input("MACD快线步长", min_value=1, max_value=3, value=1)
    macd_fasts = list(range(macd_fast_min, macd_fast_max + 1, macd_fast_step))
    param_ranges['macd_fast'] = macd_fasts

# 通用参数
st.sidebar.subheader("止盈止损设置")

profit_min = st.sidebar.number_input("止盈比例最小值", min_value=0.01, max_value=0.10, value=0.02, format="%.2f")
profit_max = st.sidebar.number_input("止盈比例最大值", min_value=profit_min, max_value=0.20, value=0.05, format="%.2f")
profit_step = st.sidebar.number_input("止盈比例步长", min_value=0.01, max_value=0.05, value=0.01, format="%.2f")
profits = [round(p, 2) for p in np.arange(profit_min, profit_max + 0.001, profit_step)]
param_ranges['profit_target'] = profits

loss_min = st.sidebar.number_input("止损比例最小值", min_value=0.01, max_value=0.05, value=0.01, format="%.2f")
loss_max = st.sidebar.number_input("止损比例最大值", min_value=loss_min, max_value=0.10, value=0.03, format="%.2f")
loss_step = st.sidebar.number_input("止损比例步长", min_value=0.01, max_value=0.02, value=0.01, format="%.2f")
losses = [round(l, 2) for l in np.arange(loss_min, loss_max + 0.001, loss_step)]
param_ranges['stop_loss'] = losses

# 计算参数组合总数
for param_values in param_ranges.values():
    max_combinations *= len(param_values)

st.sidebar.info(f"总参数组合数: {max_combinations}")

# 限制最大组合数
if max_combinations > 500:
    st.sidebar.warning("参数组合过多，建议减少参数范围或增加步长")

# 初始资金设置
initial_cash = st.sidebar.number_input(
    "初始资金",
    min_value=10000,
    max_value=1000000,
    value=int(db_config_manager.get_config('backtest', 'initial_cash', 100000)),
    step=10000
)

# 创建标签页
tab1, tab2 = st.tabs(["参数优化", "历史结果"])

with tab1:
    st.header("参数优化配置")
    
    # 显示当前设置
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("策略", selected_strategy_name)
    with col2:
        st.metric("股票数量", len(selected_stocks))
    with col3:
        st.metric("参数组合数", max_combinations)
    
    # 显示时间范围
    st.subheader("时间范围")
    st.info(f"回测区间: {start_date} 至 {end_date}")
    
    # 显示参数范围
    st.subheader("参数范围")
    param_df = pd.DataFrame({
        "参数名": list(param_ranges.keys()),
        "最小值": [min(values) for values in param_ranges.values()],
        "最大值": [max(values) for values in param_ranges.values()],
        "步长": [values[1] - values[0] if len(values) > 1 else 0 for values in param_ranges.values()],
        "可能值": [str(values) for values in param_ranges.values()]
    })
    st.dataframe(param_df, use_container_width=True)
    
    # 优化按钮
    start_button = st.button("开始优化", type="primary", use_container_width=True)

with tab2:
    st.header("历史优化结果")
    
    # 查询历史优化结果
    optimization_results = task_manager.get_optimization_results(limit=100)
    
    if not optimization_results:
        st.info("暂无历史优化结果")
    else:
        # 创建结果选择器
        result_options = []
        for result in optimization_results:
            strategy_name = result.get('strategy_name', '未知策略')
            stocks = result.get('stocks', [])
            if isinstance(stocks, str):
                try:
                    stocks = json.loads(stocks)
                except:
                    stocks = []
                    
            stock_str = ", ".join(stocks[:3])
            if len(stocks) > 3:
                stock_str += f" 等{len(stocks)}只股票"
                
            created_at = result.get('created_at', '')
            if created_at:
                try:
                    created_at = datetime.fromisoformat(created_at).strftime('%Y-%m-%d %H:%M')
                except:
                    pass
                    
            result_options.append(f"{strategy_name} | {stock_str} | {created_at}")
            
        if result_options:
            selected_result_idx = st.selectbox("选择历史优化结果", 
                                               range(len(result_options)), 
                                               format_func=lambda i: result_options[i])
            
            if selected_result_idx is not None:
                selected_result = optimization_results[selected_result_idx]
                
                # 显示结果详情
                st.subheader("优化结果详情")
                
                # 获取策略参数
                params = selected_result.get('params', {})
                if isinstance(params, str):
                    try:
                        params = json.loads(params)
                    except:
                        params = {}
                
                # 显示参数和性能指标
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write("**最佳参数:**")
                    for param, value in params.items():
                        st.write(f"- {param}: {value}")
                
                with col2:
                    st.write("**性能指标:**")
                    st.write(f"- 夏普比率: {selected_result.get('sharpe_ratio', 0):.4f}")
                    st.write(f"- 最大回撤: {selected_result.get('max_drawdown', 0):.2f}%")
                    st.write(f"- 年化收益率: {selected_result.get('annual_return', 0):.2f}%")
                    st.write(f"- 胜率: {selected_result.get('win_rate', 0):.2f}%")
                
                # 应用参数按钮
                if st.button("应用此参数", key="apply_params"):
                    try:
                        # 获取策略信息
                        strategy_name = selected_result.get('strategy_name')
                        
                        # 保存为新策略
                        if strategy_name:
                            new_strategy_name = f"{strategy_name}_optimized_{datetime.now().strftime('%m%d%H%M')}"
                            
                            # 获取原策略信息
                            strategies = db_config_manager.get_config('strategy_config', 'strategies', {})
                            
                            if strategy_name in strategies:
                                # 复制策略并添加优化参数
                                new_strategy = strategies[strategy_name].copy()
                                new_strategy['parameters'] = params
                                new_strategy['description'] = f"{new_strategy.get('description', strategy_name)} (优化)"
                                
                                # 保存新策略
                                strategies[new_strategy_name] = new_strategy
                                db_config_manager.set_config('strategy_config', 'strategies', strategies)
                                
                                # 设置为活动策略
                                db_config_manager.set_config('strategy_config', 'active_strategy', new_strategy_name)
                                
                                st.success(f"已创建优化策略: {new_strategy_name} 并设为当前活动策略")
                            else:
                                st.error(f"找不到原策略: {strategy_name}")
                        else:
                            st.error("优化结果中没有策略名称信息")
                    except Exception as e:
                        st.error(f"应用参数失败: {str(e)}")

# 优化功能
if 'tab1' in locals() and start_button:
    if max_combinations > 1000:
        if not st.warning("参数组合过多可能导致优化时间过长，确定要继续吗?", icon="⚠️"):
            st.stop()
    
    st.subheader("优化进度")
    progress_bar = st.progress(0, "准备中...")
    status_text = st.empty()
    
    # 存储优化结果
    results = []
    
    # 创建参数组合
    param_names = list(param_ranges.keys())
    param_values = list(param_ranges.values())
    param_combinations = list(itertools.product(*param_values))
    
    # 创建任务
    task_id = task_manager.create_task(
        TaskType.OPTIMIZATION,
        strategy_name=selected_strategy_name,
        params={
            'stocks': selected_stocks,
            'param_ranges': param_ranges,
            'start_date': start_date.strftime('%Y%m%d'),
            'end_date': end_date.strftime('%Y%m%d'),
            'initial_cash': initial_cash,
            'combinations_count': max_combinations
        }
    )
    
    # 更新任务状态
    task_manager.update_task_status(task_id, TaskStatus.RUNNING)
    
    # 显示进度
    for i, params in enumerate(param_combinations):
        try:
            # 更新进度
            progress = (i + 1) / len(param_combinations)
            progress_bar.progress(progress, f"优化中... {i+1}/{len(param_combinations)}")
            
            # 创建参数字典
            param_dict = dict(zip(param_names, params))
            param_str = ", ".join([f"{k}={v}" for k, v in param_dict.items()])
            status_text.info(f"正在测试参数组合: {param_str}")
            
            # 对每只股票运行回测
            stock_results = []
            for stock in selected_stocks:
                # 构建回测命令
                cmd = [
                    sys.executable,
                    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), 'run_backtest.py'),
                    "--stock-code", stock,
                    "--initial-cash", str(initial_cash),
                    "--start-time", f"{start_date.strftime('%Y%m%d')}093000",
                    "--end-time", f"{end_date.strftime('%Y%m%d')}150000",
                    "--strategy", selected_strategy['module'].replace('.py', ''),
                    "--strat", ",".join([f"{k}={v}" for k, v in param_dict.items()])
                ]
                
                # 运行回测进程
                try:
                    process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.STDOUT,
                        text=True
                    )
                    
                    # 等待进程完成
                    stdout, _ = process.communicate()
                    
                    # 解析回测结果
                    sharpe_ratio = 0.0
                    max_drawdown = 0.0
                    annual_return = 0.0
                    win_rate = 0.0
                    
                    for line in stdout.split('\n'):
                        if '夏普比率:' in line:
                            try:
                                sharpe_ratio = float(line.split(':')[1].strip())
                            except:
                                pass
                        elif '最大回撤:' in line:
                            try:
                                max_drawdown = float(line.split(':')[1].strip().replace('%', ''))
                            except:
                                pass
                        elif '年化收益率:' in line:
                            try:
                                annual_return = float(line.split(':')[1].strip().replace('%', ''))
                            except:
                                pass
                        elif '胜率:' in line:
                            try:
                                win_rate_str = line.split(':')[1].strip().replace('%', '')
                                win_rate = float(win_rate_str) / 100 if '%' in line else float(win_rate_str)
                            except:
                                pass
                    
                    # 保存单个股票结果
                    stock_results.append({
                        'stock_code': stock,
                        'sharpe_ratio': sharpe_ratio,
                        'max_drawdown': max_drawdown,
                        'annual_return': annual_return,
                        'win_rate': win_rate
                    })
                    
                except Exception as e:
                    st.error(f"运行回测出错: {str(e)}")
                    stock_results.append({
                        'stock_code': stock,
                        'sharpe_ratio': 0.0,
                        'max_drawdown': 0.0,
                        'annual_return': 0.0,
                        'win_rate': 0.0,
                        'error': str(e)
                    })
            
            # 计算平均指标
            avg_sharpe = sum(r['sharpe_ratio'] for r in stock_results) / len(stock_results)
            avg_drawdown = sum(r['max_drawdown'] for r in stock_results) / len(stock_results)
            avg_return = sum(r['annual_return'] for r in stock_results) / len(stock_results)
            avg_win_rate = sum(r['win_rate'] for r in stock_results) / len(stock_results)
            
            # 保存该参数组合的结果
            result = {
                'params': param_dict,
                'avg_sharpe_ratio': avg_sharpe,
                'avg_max_drawdown': avg_drawdown,
                'avg_annual_return': avg_return,
                'avg_win_rate': avg_win_rate,
                'stock_results': stock_results
            }
            
            results.append(result)
            
        except Exception as e:
            st.error(f"优化过程出错: {str(e)}")
            task_manager.update_task_status(task_id, TaskStatus.FAILED, error=str(e))
            break
    
    # 找出最佳参数
    if results:
        # 根据夏普比率排序
        results.sort(key=lambda x: x['avg_sharpe_ratio'], reverse=True)
        
        best_result = results[0]
        best_params = best_result['params']
        
        # 显示最佳结果
        st.subheader("优化结果")
        
        # 显示最佳参数
        st.write("**最佳参数组合:**")
        for param, value in best_params.items():
            st.write(f"- {param}: {value}")
        
        # 显示性能指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("平均夏普比率", f"{best_result['avg_sharpe_ratio']:.4f}")
        
        with col2:
            st.metric("平均最大回撤", f"{best_result['avg_max_drawdown']:.2f}%")
        
        with col3:
            st.metric("平均年化收益率", f"{best_result['avg_annual_return']:.2f}%")
        
        with col4:
            st.metric("平均胜率", f"{best_result['avg_win_rate']:.2f}%")
        
        # 显示每只股票的结果
        st.subheader("各股票表现")
        
        stock_df = pd.DataFrame([
            {
                '股票代码': r['stock_code'],
                '夏普比率': r['sharpe_ratio'],
                '最大回撤': r['max_drawdown'],
                '年化收益率': r['annual_return'],
                '胜率': r['win_rate']
            }
            for r in best_result['stock_results']
        ])
        
        st.dataframe(stock_df, use_container_width=True)
        
        # 创建所有结果的数据框
        all_results_df = pd.DataFrame([
            {
                **{param: r['params'][param] for param in param_names},
                '夏普比率': r['avg_sharpe_ratio'],
                '最大回撤': r['avg_max_drawdown'],
                '年化收益率': r['avg_annual_return'],
                '胜率': r['avg_win_rate']
            }
            for r in results
        ])
        
        # 显示所有结果
        st.subheader("所有参数组合结果")
        st.dataframe(all_results_df.sort_values('夏普比率', ascending=False), use_container_width=True)
        
        # 保存结果到数据库
        try:
            task_manager.save_optimization_result(
                task_id=task_id,
                strategy_name=selected_strategy_name,
                params=best_params,
                stocks=selected_stocks,
                start_date=start_date.strftime('%Y%m%d'),
                end_date=end_date.strftime('%Y%m%d'),
                metrics={
                    'sharpe_ratio': best_result['avg_sharpe_ratio'],
                    'max_drawdown': best_result['avg_max_drawdown'],
                    'annual_return': best_result['avg_annual_return'],
                    'win_rate': best_result['avg_win_rate']
                }
            )
            
            # 更新任务状态
            task_manager.update_task_status(
                task_id,
                TaskStatus.COMPLETED,
                result={
                    'best_params': best_params,
                    'metrics': {
                        'sharpe_ratio': best_result['avg_sharpe_ratio'],
                        'max_drawdown': best_result['avg_max_drawdown'],
                        'annual_return': best_result['avg_annual_return'],
                        'win_rate': best_result['avg_win_rate']
                    }
                }
            )
            
            st.success("优化完成，结果已保存")
            
        except Exception as e:
            st.error(f"保存优化结果失败: {str(e)}")
            task_manager.update_task_status(task_id, TaskStatus.FAILED, error=str(e))
    else:
        st.error("优化未产生有效结果")
        task_manager.update_task_status(task_id, TaskStatus.FAILED, error="优化未产生有效结果") 