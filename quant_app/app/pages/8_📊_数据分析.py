"""
数据分析页面 - 市场数据分析和选股功能
"""
import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import sys
import os
import numpy as np

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from quant_app.core.db_config_manager import db_config_manager
from quant_app.core.data_handler import data_handler

# 页面配置
st.set_page_config(page_title="数据分析 - 量化交易系统", page_icon="📊", layout="wide")

# 标题
st.title("📊 数据分析")
st.markdown("市场数据分析、选股和数据管理")
st.markdown("---")

# 创建标签页
tab1, tab2, tab3, tab4 = st.tabs([
    "📈 市场数据", "🎯 选股分析", "📥 数据导入", "📤 数据导出"
])

# Tab 1: 市场数据分析
with tab1:
    st.header("市场数据分析")
    
    # 股票选择
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # 获取已保存的股票列表
        try:
            saved_stocks = data_handler.get_selected_stocks()
        except Exception as e:
            st.warning(f"获取股票列表失败: {str(e)}")
            saved_stocks = []
        
        if saved_stocks:
            selected_stock = st.selectbox(
                "选择股票",
                options=saved_stocks,
                help="选择要分析的股票"
            )
        else:
            st.warning("未找到股票数据，请先在实盘交易页面添加股票")
            selected_stock = None
    
    with col2:
        # 时间范围选择
        days_back = st.selectbox(
            "数据范围",
            options=[30, 60, 90, 180, 365],
            index=2,
            format_func=lambda x: f"最近{x}天"
        )
    
    if selected_stock:
        # 模拟数据展示（实际应该从数据源获取）
        st.subheader(f"{selected_stock} 技术分析")
        
        # 生成模拟数据
        dates = pd.date_range(end=datetime.now(), periods=days_back, freq='D')
        np.random.seed(42)
        base_price = 100
        prices = []
        current_price = base_price
        
        for i in range(days_back):
            change = np.random.normal(0, 0.02)
            current_price *= (1 + change)
            prices.append(current_price)
        
        # 创建数据框
        df = pd.DataFrame({
            'date': dates,
            'close': prices,
            'volume': np.random.randint(1000000, 10000000, days_back)
        })
        
        # 计算技术指标
        df['ma5'] = df['close'].rolling(5).mean()
        df['ma20'] = df['close'].rolling(20).mean()
        df['rsi'] = 50 + np.random.normal(0, 15, days_back)  # 模拟RSI
        
        # 价格图表
        fig_price = go.Figure()
        
        fig_price.add_trace(go.Scatter(
            x=df['date'], 
            y=df['close'],
            mode='lines',
            name='收盘价',
            line=dict(color='blue', width=2)
        ))
        
        fig_price.add_trace(go.Scatter(
            x=df['date'], 
            y=df['ma5'],
            mode='lines',
            name='MA5',
            line=dict(color='orange', width=1)
        ))
        
        fig_price.add_trace(go.Scatter(
            x=df['date'], 
            y=df['ma20'],
            mode='lines',
            name='MA20',
            line=dict(color='red', width=1)
        ))
        
        fig_price.update_layout(
            title=f"{selected_stock} 价格走势",
            xaxis_title="日期",
            yaxis_title="价格",
            height=400
        )
        
        st.plotly_chart(fig_price, use_container_width=True)
        
        # 成交量图表
        fig_volume = go.Figure()
        fig_volume.add_trace(go.Bar(
            x=df['date'], 
            y=df['volume'],
            name='成交量',
            marker_color='lightblue'
        ))
        
        fig_volume.update_layout(
            title=f"{selected_stock} 成交量",
            xaxis_title="日期",
            yaxis_title="成交量",
            height=300
        )
        
        st.plotly_chart(fig_volume, use_container_width=True)
        
        # 技术指标表格
        st.subheader("最新技术指标")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("当前价格", f"{df['close'].iloc[-1]:.2f}")
        
        with col2:
            st.metric("MA5", f"{df['ma5'].iloc[-1]:.2f}")
        
        with col3:
            st.metric("MA20", f"{df['ma20'].iloc[-1]:.2f}")
        
        with col4:
            st.metric("RSI", f"{df['rsi'].iloc[-1]:.1f}")

# Tab 2: 选股分析
with tab2:
    st.header("选股分析")
    
    st.subheader("选股条件设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**技术指标条件**")
        
        rsi_min = st.slider("RSI最小值", 0, 100, 30)
        rsi_max = st.slider("RSI最大值", 0, 100, 70)
        
        ma_condition = st.selectbox(
            "均线条件",
            ["价格在MA5上方", "价格在MA20上方", "MA5在MA20上方", "无要求"]
        )
        
        volume_condition = st.selectbox(
            "成交量条件",
            ["放量突破", "缩量整理", "无要求"]
        )
    
    with col2:
        st.write("**基本面条件**")
        
        market_cap_min = st.number_input("最小市值(亿)", min_value=0, value=50)
        market_cap_max = st.number_input("最大市值(亿)", min_value=0, value=1000)
        
        pe_min = st.number_input("最小PE", min_value=0.0, value=0.0)
        pe_max = st.number_input("最大PE", min_value=0.0, value=50.0)
        
        industry = st.multiselect(
            "行业筛选",
            ["科技", "医药", "消费", "金融", "制造", "能源"],
            default=[]
        )
    
    if st.button("开始选股", type="primary"):
        with st.spinner("正在筛选股票..."):
            # 模拟选股结果
            selected_stocks_result = [
                {"代码": "600000.SH", "名称": "浦发银行", "价格": 8.45, "RSI": 45.2, "市值": 2500, "PE": 5.2},
                {"代码": "000001.SZ", "名称": "平安银行", "价格": 12.34, "RSI": 52.1, "市值": 2800, "PE": 6.1},
                {"代码": "600036.SH", "名称": "招商银行", "价格": 35.67, "RSI": 48.9, "市值": 8900, "PE": 8.5},
            ]
            
            result_df = pd.DataFrame(selected_stocks_result)
            
            st.success(f"筛选完成，找到 {len(result_df)} 只符合条件的股票")
            st.dataframe(result_df, use_container_width=True)
            
            # 添加到股票池按钮
            if st.button("添加到股票池"):
                st.success("已添加到股票池")

# Tab 3: 数据导入
with tab3:
    st.header("数据导入")
    
    st.subheader("股票列表导入")
    
    # 文件上传
    uploaded_file = st.file_uploader(
        "上传股票列表文件",
        type=['csv', 'xlsx', 'txt'],
        help="支持CSV、Excel和文本文件格式"
    )
    
    if uploaded_file is not None:
        try:
            if uploaded_file.name.endswith('.csv'):
                df = pd.read_csv(uploaded_file)
            elif uploaded_file.name.endswith('.xlsx'):
                df = pd.read_excel(uploaded_file)
            else:
                # 文本文件，每行一个股票代码
                content = uploaded_file.read().decode('utf-8')
                stocks = [line.strip() for line in content.split('\n') if line.strip()]
                df = pd.DataFrame({'股票代码': stocks})
            
            st.success(f"成功读取 {len(df)} 条记录")
            st.dataframe(df.head(10), use_container_width=True)
            
            if st.button("导入到系统"):
                st.success("数据导入成功")
                
        except Exception as e:
            st.error(f"文件读取失败: {str(e)}")
    
    st.subheader("手动添加股票")
    
    col1, col2 = st.columns([3, 1])
    
    with col1:
        new_stock_codes = st.text_area(
            "股票代码",
            placeholder="每行输入一个股票代码，例如：\n600000.SH\n000001.SZ",
            height=100
        )
    
    with col2:
        if st.button("添加股票", type="primary"):
            if new_stock_codes:
                codes = [code.strip() for code in new_stock_codes.split('\n') if code.strip()]
                st.success(f"已添加 {len(codes)} 只股票")

# Tab 4: 数据导出
with tab4:
    st.header("数据导出")
    
    st.subheader("导出选项")
    
    col1, col2 = st.columns(2)
    
    with col1:
        export_type = st.selectbox(
            "导出类型",
            ["股票列表", "交易记录", "回测结果", "策略参数"]
        )
        
        export_format = st.selectbox(
            "导出格式",
            ["Excel (.xlsx)", "CSV (.csv)", "JSON (.json)"]
        )
    
    with col2:
        date_range = st.date_input(
            "数据时间范围",
            value=[datetime.now() - timedelta(days=30), datetime.now()],
            help="选择要导出的数据时间范围"
        )
    
    if st.button("生成导出文件", type="primary"):
        with st.spinner("正在生成文件..."):
            # 模拟文件生成
            import time
            time.sleep(2)
            
            filename = f"{export_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            st.success(f"文件生成成功: {filename}")
            
            # 这里应该提供实际的下载链接
            st.download_button(
                label="下载文件",
                data=b"mock data",  # 实际应该是生成的文件内容
                file_name=filename,
                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )

# 侧边栏 - 快速操作
st.sidebar.header("快速操作")

if st.sidebar.button("刷新数据"):
    st.rerun()

if st.sidebar.button("清理缓存"):
    st.cache_data.clear()
    st.sidebar.success("缓存已清理")

# 数据统计
st.sidebar.header("数据统计")
try:
    saved_stocks = data_handler.get_selected_stocks()
    st.sidebar.metric("股票池数量", len(saved_stocks))
except:
    st.sidebar.metric("股票池数量", 0)

st.sidebar.metric("数据更新时间", datetime.now().strftime('%H:%M:%S'))
