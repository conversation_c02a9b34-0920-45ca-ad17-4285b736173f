"""
批量回测页面 - 批量运行回测任务
"""
import streamlit as st
import pandas as pd
import sys
import os
import subprocess
import threading
import time
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from quant_app.core.db_config_manager import db_config_manager
from quant_app.core.data_handler import data_handler
from quant_app.core.task_manager import task_manager, TaskType, TaskStatus

# 页面配置
st.set_page_config(page_title="批量回测 - 量化交易系统", page_icon="📊", layout="wide")

# 标题
st.title("📊 批量回测")
st.markdown("批量运行多日期、多股票的回测任务")
st.markdown("---")

# 初始化session state
if 'batch_tasks' not in st.session_state:
    st.session_state.batch_tasks = []

# 创建标签页
tab1, tab2, tab3 = st.tabs([
    "🚀 启动批量回测", "📋 任务监控", "📈 结果分析"
])

# Tab 1: 启动批量回测
with tab1:
    st.header("批量回测配置")
    
    # 基本配置
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("时间范围")
        
        # 回测模式选择
        backtest_mode = st.radio(
            "回测模式",
            ["指定日期范围", "最近N天", "指定日期列表"],
            help="选择回测的时间范围模式"
        )
        
        if backtest_mode == "指定日期范围":
            start_date = st.date_input(
                "开始日期",
                value=datetime.now() - timedelta(days=30),
                max_value=datetime.now()
            )
            
            end_date = st.date_input(
                "结束日期",
                value=datetime.now() - timedelta(days=1),
                min_value=start_date,
                max_value=datetime.now()
            )
            
            date_list = pd.date_range(start=start_date, end=end_date, freq='D').strftime('%Y%m%d').tolist()
            
        elif backtest_mode == "最近N天":
            days_back = st.number_input(
                "回测天数",
                min_value=1,
                max_value=365,
                value=30,
                help="从今天往前推算的天数"
            )
            
            end_date = datetime.now() - timedelta(days=1)
            start_date = end_date - timedelta(days=days_back-1)
            date_list = pd.date_range(start=start_date, end=end_date, freq='D').strftime('%Y%m%d').tolist()
            
        else:  # 指定日期列表
            date_input = st.text_area(
                "日期列表",
                placeholder="每行输入一个日期，格式：20240101\n20240102\n20240103",
                help="每行输入一个日期，格式为YYYYMMDD"
            )
            
            if date_input:
                date_list = [line.strip() for line in date_input.split('\n') if line.strip()]
            else:
                date_list = []
        
        st.info(f"将回测 {len(date_list)} 个交易日")
    
    with col2:
        st.subheader("股票选择")
        
        # 获取股票列表
        try:
            saved_stocks = data_handler.get_selected_stocks()
        except Exception as e:
            st.warning(f"获取股票列表失败: {str(e)}")
            saved_stocks = []
        
        if saved_stocks:
            stock_selection_mode = st.radio(
                "股票选择模式",
                ["使用已保存股票", "手动选择股票", "自动选股"],
                help="选择要回测的股票"
            )
            
            if stock_selection_mode == "使用已保存股票":
                selected_stocks = saved_stocks
                st.success(f"将使用 {len(selected_stocks)} 只已保存的股票")
                
            elif stock_selection_mode == "手动选择股票":
                selected_stocks = st.multiselect(
                    "选择股票",
                    options=saved_stocks,
                    default=saved_stocks[:5] if len(saved_stocks) > 5 else saved_stocks,
                    help="选择要回测的股票"
                )
                
            else:  # 自动选股
                st.info("将在每个回测日期自动执行选股")
                selected_stocks = []
        else:
            st.warning("未找到已保存的股票，请先在实盘交易页面添加股票")
            selected_stocks = []
    
    # 高级配置
    st.subheader("高级配置")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        initial_cash = st.number_input(
            "初始资金",
            min_value=10000,
            max_value=10000000,
            value=1000000,
            step=10000
        )
        
        max_parallel = st.number_input(
            "最大并行任务数",
            min_value=1,
            max_value=10,
            value=3,
            help="同时运行的回测任务数量"
        )
    
    with col2:
        commission_rate = st.number_input(
            "手续费率 (%)",
            min_value=0.0,
            max_value=1.0,
            value=0.03,
            step=0.01,
            format="%.3f"
        ) / 100
        
        enable_plot = st.checkbox(
            "生成图表",
            value=False,
            help="是否为每个回测生成图表（会增加运行时间）"
        )
    
    with col3:
        slippage = st.number_input(
            "滑点 (%)",
            min_value=0.0,
            max_value=1.0,
            value=0.2,
            step=0.1,
            format="%.1f"
        ) / 100
        
        save_results = st.checkbox(
            "保存详细结果",
            value=True,
            help="是否保存详细的回测结果"
        )
    
    # 启动批量回测
    st.subheader("启动回测")
    
    if st.button("🚀 开始批量回测", type="primary", use_container_width=True):
        if not date_list:
            st.error("请选择回测日期")
        elif stock_selection_mode != "自动选股" and not selected_stocks:
            st.error("请选择回测股票")
        else:
            # 创建批量回测任务
            with st.spinner("正在创建批量回测任务..."):
                try:
                    # 准备批量回测参数
                    batch_params = {
                        'date_list': date_list,
                        'stock_list': selected_stocks if stock_selection_mode != "自动选股" else [],
                        'auto_select': stock_selection_mode == "自动选股",
                        'initial_cash': initial_cash,
                        'commission_rate': commission_rate,
                        'slippage': slippage,
                        'max_parallel': max_parallel,
                        'enable_plot': enable_plot,
                        'save_results': save_results
                    }
                    
                    # 创建任务
                    task_id = task_manager.create_task(
                        TaskType.BATCH_BACKTEST,
                        strategy_name="batch_backtest",
                        params=batch_params
                    )
                    
                    # 启动批量回测进程
                    def run_batch_backtest():
                        try:
                            # 构建命令
                            cmd = [
                                "python", "run_batch_backtest.py",
                                "--start-date", date_list[0],
                                "--end-date", date_list[-1],
                                "--max-processes", str(max_parallel)
                            ]
                            
                            # 运行批量回测
                            process = subprocess.Popen(
                                cmd,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.STDOUT,
                                text=True,
                                bufsize=1
                            )
                            
                            # 更新任务状态
                            task_manager.update_task_status(
                                task_id,
                                TaskStatus.RUNNING,
                                pid=process.pid
                            )
                            
                            # 等待完成
                            process.wait()
                            
                            if process.returncode == 0:
                                task_manager.update_task_status(task_id, TaskStatus.COMPLETED)
                            else:
                                task_manager.update_task_status(
                                    task_id, 
                                    TaskStatus.FAILED,
                                    error="批量回测进程异常退出"
                                )
                                
                        except Exception as e:
                            task_manager.update_task_status(
                                task_id,
                                TaskStatus.FAILED,
                                error=str(e)
                            )
                    
                    # 在后台线程中运行
                    thread = threading.Thread(target=run_batch_backtest, daemon=True)
                    thread.start()
                    
                    # 添加到session state
                    st.session_state.batch_tasks.append(task_id)
                    
                    st.success(f"批量回测任务已启动，任务ID: {task_id}")
                    st.info(f"将回测 {len(date_list)} 个日期，预计需要 {len(date_list) * 2} 分钟")
                    
                except Exception as e:
                    st.error(f"启动批量回测失败: {str(e)}")

# Tab 2: 任务监控
with tab2:
    st.header("批量回测任务监控")
    
    # 刷新按钮
    if st.button("🔄 刷新状态"):
        st.rerun()
    
    # 获取所有批量回测任务
    try:
        all_tasks = task_manager.get_tasks_by_type(TaskType.BATCH_BACKTEST)
        
        if not all_tasks:
            st.info("暂无批量回测任务")
        else:
            # 显示任务列表
            task_data = []
            for task in all_tasks:
                task_data.append({
                    "任务ID": task['task_id'][:8],
                    "状态": task['status'],
                    "创建时间": task['created_at'],
                    "开始时间": task.get('started_at', '-'),
                    "完成时间": task.get('finished_at', '-'),
                    "进度": f"{task.get('progress', 0):.1f}%"
                })
            
            df = pd.DataFrame(task_data)
            st.dataframe(df, use_container_width=True)
            
            # 任务详情
            st.subheader("任务详情")
            
            if task_data:
                selected_task_id = st.selectbox(
                    "选择任务查看详情",
                    options=[task['task_id'] for task in all_tasks],
                    format_func=lambda x: f"{x[:8]} - {next(t['status'] for t in all_tasks if t['task_id'] == x)}"
                )
                
                if selected_task_id:
                    task_info = next(t for t in all_tasks if t['task_id'] == selected_task_id)
                    
                    col1, col2 = st.columns([2, 1])
                    
                    with col1:
                        st.write(f"**任务ID**: {task_info['task_id']}")
                        st.write(f"**状态**: {task_info['status']}")
                        st.write(f"**创建时间**: {task_info['created_at']}")
                        
                        if task_info.get('params'):
                            params = task_info['params']
                            st.write(f"**回测日期数**: {len(params.get('date_list', []))}")
                            st.write(f"**股票数**: {len(params.get('stock_list', []))}")
                            st.write(f"**初始资金**: {params.get('initial_cash', 0):,}")
                    
                    with col2:
                        if task_info['status'] == 'running':
                            if st.button("停止任务", key=f"stop_{selected_task_id}"):
                                if task_manager.stop_task(selected_task_id):
                                    st.success("任务已停止")
                                    st.rerun()
                        
                        if st.button("查看日志", key=f"log_{selected_task_id}"):
                            # 显示任务日志
                            log_lines = task_manager.get_task_log(selected_task_id, lines=100)
                            if log_lines:
                                st.text_area(
                                    "任务日志",
                                    value='\n'.join(log_lines),
                                    height=300
                                )
                            else:
                                st.info("暂无日志内容")
    
    except Exception as e:
        st.error(f"获取任务列表失败: {str(e)}")

# Tab 3: 结果分析
with tab3:
    st.header("批量回测结果分析")
    
    # 结果文件选择
    st.subheader("选择结果文件")
    
    # 查找results目录
    results_dir = "results"
    if os.path.exists(results_dir):
        result_folders = [f for f in os.listdir(results_dir) 
                         if os.path.isdir(os.path.join(results_dir, f)) and f.startswith('backtest_')]
        
        if result_folders:
            result_folders.sort(reverse=True)  # 最新的在前面
            
            selected_folder = st.selectbox(
                "选择回测结果",
                options=result_folders,
                format_func=lambda x: f"{x} ({datetime.strptime(x.split('_')[1], '%Y%m%d_%H%M%S').strftime('%Y-%m-%d %H:%M:%S')})"
            )
            
            if selected_folder:
                folder_path = os.path.join(results_dir, selected_folder)
                
                # 分析结果文件
                st.subheader("结果概览")
                
                # 查找日志文件
                log_files = []
                for root, dirs, files in os.walk(folder_path):
                    for file in files:
                        if file.endswith('.log'):
                            log_files.append(os.path.join(root, file))
                
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("结果文件夹", selected_folder)
                
                with col2:
                    st.metric("日志文件数", len(log_files))
                
                with col3:
                    folder_size = sum(os.path.getsize(os.path.join(root, file))
                                    for root, dirs, files in os.walk(folder_path)
                                    for file in files)
                    st.metric("文件夹大小", f"{folder_size / 1024 / 1024:.1f} MB")
                
                # 显示部分日志文件
                if log_files:
                    st.subheader("日志文件列表")
                    
                    log_data = []
                    for log_file in log_files[:20]:  # 只显示前20个
                        file_name = os.path.basename(log_file)
                        file_size = os.path.getsize(log_file)
                        mod_time = datetime.fromtimestamp(os.path.getmtime(log_file))
                        
                        log_data.append({
                            "文件名": file_name,
                            "大小(KB)": f"{file_size / 1024:.1f}",
                            "修改时间": mod_time.strftime('%Y-%m-%d %H:%M:%S')
                        })
                    
                    df = pd.DataFrame(log_data)
                    st.dataframe(df, use_container_width=True)
                    
                    if len(log_files) > 20:
                        st.info(f"还有 {len(log_files) - 20} 个文件未显示")
                
                # 分析按钮
                if st.button("📊 分析回测结果", type="primary"):
                    with st.spinner("正在分析回测结果..."):
                        try:
                            # 这里可以调用log_analyzer.py来分析结果
                            st.success("结果分析功能开发中...")
                            st.info("请使用命令行工具 python log_analyzer.py 进行详细分析")
                            
                        except Exception as e:
                            st.error(f"分析失败: {str(e)}")
        else:
            st.info("未找到批量回测结果文件夹")
    else:
        st.info("results目录不存在，请先运行批量回测")

# 侧边栏 - 快速操作
st.sidebar.header("快速操作")

if st.sidebar.button("清理旧任务"):
    try:
        cleaned = task_manager.clean_old_tasks(days=7)
        st.sidebar.success(f"已清理 {cleaned} 个旧任务")
    except Exception as e:
        st.sidebar.error(f"清理失败: {str(e)}")

if st.sidebar.button("停止所有任务"):
    try:
        running_tasks = [t for t in task_manager.get_tasks_by_type(TaskType.BATCH_BACKTEST) 
                        if t['status'] == 'running']
        
        stopped_count = 0
        for task in running_tasks:
            if task_manager.stop_task(task['task_id']):
                stopped_count += 1
        
        st.sidebar.success(f"已停止 {stopped_count} 个任务")
    except Exception as e:
        st.sidebar.error(f"停止任务失败: {str(e)}")

# 状态信息
st.sidebar.header("状态信息")

try:
    all_tasks = task_manager.get_tasks_by_type(TaskType.BATCH_BACKTEST)
    running_tasks = [t for t in all_tasks if t['status'] == 'running']
    completed_tasks = [t for t in all_tasks if t['status'] == 'completed']
    
    st.sidebar.metric("总任务数", len(all_tasks))
    st.sidebar.metric("运行中", len(running_tasks))
    st.sidebar.metric("已完成", len(completed_tasks))
    
except Exception as e:
    st.sidebar.error(f"获取状态失败: {str(e)}")
