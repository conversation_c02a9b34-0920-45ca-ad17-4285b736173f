"""
策略回测页面 - 运行和管理策略回测
"""
import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from datetime import datetime, timedelta
import sys
import os
import json
import numpy as np

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from quant_app.core.db_config_manager import db_config_manager
from quant_app.core.data_handler import data_handler
from quant_app.core.execution_engine import execution_engine
from quant_app.core.task_manager import TaskStatus

# 页面配置
st.set_page_config(page_title="策略回测 - 量化交易系统", page_icon="🎯", layout="wide")

# 标题
st.title("🎯 策略回测")
st.markdown("测试和优化您的交易策略")
st.markdown("---")

# 初始化session state
if 'backtest_results' not in st.session_state:
    st.session_state.backtest_results = {}
if 'running_tasks' not in st.session_state:
    st.session_state.running_tasks = []

# 侧边栏 - 回测参数设置
st.sidebar.header("回测参数设置")

# 1. 策略选择
available_strategies = data_handler.get_strategies()

if not available_strategies:
    st.error("未找到可用的策略文件")
    st.stop()

strategy_names = [s['name'] for s in available_strategies]
selected_strategy_name = st.sidebar.selectbox(
    "选择策略",
    strategy_names,
    help="选择要回测的策略"
)

# 获取选中策略的信息
selected_strategy = next(s for s in available_strategies if s['name'] == selected_strategy_name)

# 显示策略描述
if selected_strategy.get('description'):
    st.sidebar.info(f"策略说明: {selected_strategy['description']}")

# 2. 股票选择
st.sidebar.subheader("股票选择")

# 获取已保存的股票列表
try:
    saved_stocks = data_handler.get_selected_stocks()
except Exception as e:
    st.warning(f"获取已保存股票列表失败: {str(e)}")
    saved_stocks = []

if not saved_stocks:
    st.warning("未找到可用的股票，请先在系统设置页面添加股票")
    st.stop()

# 选择股票
selected_stocks = st.sidebar.multiselect(
    "选择要回测的股票",
    options=saved_stocks,
    default=[saved_stocks[0]] if saved_stocks else None,
    help="选择要回测的股票（可多选）"
)

if not selected_stocks:
    st.warning("请至少选择一只股票进行回测")
    st.stop()

# 3. 时间范围
st.sidebar.subheader("回测时间范围")

col1, col2 = st.sidebar.columns(2)
with col1:
    start_date = st.date_input(
        "开始日期",
        value=datetime.now() - timedelta(days=365),
        max_value=datetime.now() - timedelta(days=1)
    )

with col2:
    end_date = st.date_input(
        "结束日期",
        value=datetime.now() - timedelta(days=1),
        min_value=start_date,
        max_value=datetime.now()
    )

# 4. 资金和风控参数
st.sidebar.subheader("资金管理")

initial_capital = st.sidebar.number_input(
    "初始资金 (元)",
    min_value=10000,
    max_value=100000000,
    value=int(db_config_manager.get_config('initial_capital', 1000000) or 1000000),
    step=10000,
    help="回测使用的初始资金"
)

commission_rate = st.sidebar.number_input(
    "手续费率 (%)",
    min_value=0.0,
    max_value=1.0,
    value=float(db_config_manager.get_config('commission_rate', 0.03) or 0.03),
    step=0.01,
    format="%.3f",
    help="交易手续费率，例如0.03表示万分之三"
) / 100

slippage = st.sidebar.number_input(
    "滑点 (%)",
    min_value=0.0,
    max_value=1.0,
    value=float(db_config_manager.get_config('slippage', 0.2) or 0.2),
    step=0.1,
    format="%.1f",
    help="预期滑点，例如0.2表示0.2%"
) / 100

# 5. 策略特定参数
st.sidebar.subheader("策略参数")

# 这里可以根据不同策略动态生成参数输入
# 暂时使用固定参数
strategy_params = {}

if 'kdj' in selected_strategy_name.lower():
    kdj_period = st.sidebar.slider("KDJ周期", 5, 30, 9)
    volume_ma_period = st.sidebar.slider("成交量均线周期", 5, 30, 20)
    strategy_params = {
        'kdj_period': kdj_period,
        'volume_ma_period': volume_ma_period
    }
elif 'rsi' in selected_strategy_name.lower():
    rsi_period = st.sidebar.slider("RSI周期", 5, 30, 14)
    rsi_oversold = st.sidebar.slider("RSI超卖阈值", 10, 40, 30)
    rsi_overbought = st.sidebar.slider("RSI超买阈值", 60, 90, 70)
    strategy_params = {
        'rsi_period': rsi_period,
        'rsi_oversold': rsi_oversold,
        'rsi_overbought': rsi_overbought
    }

# 6. 执行按钮
if st.sidebar.button("🚀 开始回测", type="primary", use_container_width=True):
    # 准备回测参数
    backtest_params = {
        'stock_code': selected_stocks[0],  # 暂时只支持单只股票回测
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'initial_capital': initial_capital,
        'commission_rate': commission_rate,
        'slippage': slippage,
        **strategy_params
    }
    
    # 创建任务状态占位符
    with st.spinner(f"正在启动回测任务..."):
        try:
            # 运行回测
            task_id = execution_engine.run_backtest(
                strategy_name=selected_strategy['file'],
                params=backtest_params
            )
            
            st.session_state.running_tasks.append(task_id)
            st.success(f"回测任务已启动，任务ID: {task_id}")
            
        except Exception as e:
            st.error(f"启动回测失败: {str(e)}")

# 主页面内容
# 1. 运行中的任务
if st.session_state.running_tasks:
    st.header("⏳ 运行中的任务")
    
    running_tasks_info = []
    for task_id in st.session_state.running_tasks[:]:
        task_status = execution_engine.get_task_status(task_id)
        if task_status:
            if task_status['status'] in ['completed', 'failed', 'cancelled']:
                # 任务已结束，从运行列表中移除
                st.session_state.running_tasks.remove(task_id)
                # 如果成功完成，添加到结果中
                if task_status['status'] == 'completed':
                    result = execution_engine.get_task_result(task_id)
                    st.session_state.backtest_results[task_id] = result
            else:
                running_tasks_info.append(task_status)
    
    if running_tasks_info:
        for task in running_tasks_info:
            with st.expander(f"任务 {task['task_id']} - {task['strategy_name']}", expanded=True):
                col1, col2, col3 = st.columns([2, 1, 1])
                
                with col1:
                    st.write(f"**状态**: {task['status']}")
                    st.write(f"**创建时间**: {task['created_at']}")
                
                with col2:
                    if st.button(f"停止", key=f"stop_{task['task_id']}"):
                        if execution_engine.stop_task(task['task_id']):
                            st.success("任务已停止")
                            st.session_state.running_tasks.remove(task['task_id'])
                            st.rerun()
                
                with col3:
                    if st.button(f"查看日志", key=f"log_{task['task_id']}"):
                        st.session_state[f"show_log_{task['task_id']}"] = True
                
                # 显示日志
                if st.session_state.get(f"show_log_{task['task_id']}", False):
                    st.text_area(
                        "任务日志",
                        value='\n'.join(task.get('log_preview', [])),
                        height=200,
                        key=f"log_area_{task['task_id']}"
                    )

# 2. 回测结果
if st.session_state.backtest_results:
    st.header("📊 回测结果")
    
    # 创建选项卡
    tabs = st.tabs([f"任务 {task_id[:8]}" for task_id in st.session_state.backtest_results.keys()])
    
    for i, (task_id, result) in enumerate(st.session_state.backtest_results.items()):
        with tabs[i]:
            # 基本信息
            col1, col2 = st.columns([3, 1])
            
            with col1:
                st.subheader(f"策略: {result.get('strategy_name', 'Unknown')}")
                
                # 参数显示
                params = result.get('params', {})
                param_str = ", ".join([f"{k}={v}" for k, v in params.items() if k not in ['start_date', 'end_date']])
                st.caption(f"参数: {param_str}")
            
            with col2:
                if st.button("导出报告", key=f"export_{task_id}"):
                    output_file = execution_engine.export_results(task_id)
                    if output_file:
                        st.success(f"报告已导出: {output_file}")
            
            # 性能指标
            st.subheader("性能指标")
            
            performance = result.get('performance', {})
            if performance:
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    total_return = performance.get('总收益率', 0)
                    st.metric(
                        "总收益率",
                        f"{total_return:.2f}%",
                        delta_color="normal" if total_return > 0 else "inverse"
                    )
                
                with col2:
                    annual_return = performance.get('年化收益率', 0)
                    st.metric(
                        "年化收益率",
                        f"{annual_return:.2f}%",
                        delta_color="normal" if annual_return > 0 else "inverse"
                    )
                
                with col3:
                    sharpe_ratio = performance.get('夏普比率', 0)
                    st.metric(
                        "夏普比率",
                        f"{sharpe_ratio:.2f}",
                        delta_color="normal" if sharpe_ratio > 0 else "inverse"
                    )
                
                with col4:
                    max_drawdown = performance.get('最大回撤', 0)
                    st.metric(
                        "最大回撤",
                        f"{max_drawdown:.2f}%",
                        delta_color="inverse" if max_drawdown > 20 else "normal"
                    )
            
            # 交易统计
            st.subheader("交易统计")
            
            summary = result.get('summary', {})
            if summary:
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("总交易次数", summary.get('total_trades', 0))
                
                with col2:
                    st.metric("胜率", f"{summary.get('win_rate', 0):.1f}%")
                
                with col3:
                    st.metric("盈亏比", f"{summary.get('profit_factor', 0):.2f}")
                
                with col4:
                    st.metric("平均持仓天数", f"{summary.get('avg_holding_days', 0):.1f}")
            
            # 收益曲线（这里需要从日志解析获取）
            # 暂时显示占位符
            st.subheader("收益曲线")
            st.info("收益曲线图表功能开发中...")

# 3. 策略优化
st.header("🔧 策略优化")

with st.expander("参数优化设置", expanded=False):
    st.write("批量测试不同参数组合，找出最优参数")
    
    # 参数网格设置
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("参数范围设置")
        
        # 参数范围设置 - 动态根据策略类型设置
        param_ranges = {}
        
        if 'kdj' in selected_strategy_name.lower():
            kdj_period_min = st.number_input("KDJ周期最小值", min_value=5, max_value=21, value=9)
            kdj_period_max = st.number_input("KDJ周期最大值", min_value=kdj_period_min, max_value=21, value=14)
            kdj_period_step = st.number_input("KDJ周期步长", min_value=1, max_value=5, value=1)
            kdj_periods = list(range(kdj_period_min, kdj_period_max + 1, kdj_period_step))
            param_ranges['kdj_period'] = kdj_periods
            
            j_oversold_min = st.number_input("J超卖阈值最小值", min_value=0, max_value=30, value=10)
            j_oversold_max = st.number_input("J超卖阈值最大值", min_value=j_oversold_min, max_value=30, value=20)
            j_oversold_step = st.number_input("J超卖阈值步长", min_value=1, max_value=5, value=5)
            j_oversolds = list(range(j_oversold_min, j_oversold_max + 1, j_oversold_step))
            param_ranges['j_oversold'] = j_oversolds
            
            j_overbought_min = st.number_input("J超买阈值最小值", min_value=70, max_value=100, value=80)
            j_overbought_max = st.number_input("J超买阈值最大值", min_value=j_overbought_min, max_value=100, value=90)
            j_overbought_step = st.number_input("J超买阈值步长", min_value=1, max_value=5, value=5)
            j_overboughts = list(range(j_overbought_min, j_overbought_max + 1, j_overbought_step))
            param_ranges['j_overbought'] = j_overboughts
            
            volume_ma_min = st.number_input("成交量MA周期最小值", min_value=5, max_value=30, value=10)
            volume_ma_max = st.number_input("成交量MA周期最大值", min_value=volume_ma_min, max_value=30, value=20)
            volume_ma_step = st.number_input("成交量MA周期步长", min_value=1, max_value=5, value=5)
            volume_mas = list(range(volume_ma_min, volume_ma_max + 1, volume_ma_step))
            param_ranges['volume_ma_period'] = volume_mas
            
        elif 'rsi' in selected_strategy_name.lower() or 'triple' in selected_strategy_name.lower() or 'resonance' in selected_strategy_name.lower():
            # 三重共振策略参数
            rsi_period_min = st.number_input("RSI周期最小值", min_value=2, max_value=14, value=3)
            rsi_period_max = st.number_input("RSI周期最大值", min_value=rsi_period_min, max_value=14, value=5)
            rsi_period_step = st.number_input("RSI周期步长", min_value=1, max_value=2, value=1)
            rsi_periods = list(range(rsi_period_min, rsi_period_max + 1, rsi_period_step))
            param_ranges['rsi_period'] = rsi_periods
            
            rsi_upper_min = st.number_input("RSI上限最小值", min_value=50, max_value=90, value=70)
            rsi_upper_max = st.number_input("RSI上限最大值", min_value=rsi_upper_min, max_value=90, value=80)
            rsi_upper_step = st.number_input("RSI上限步长", min_value=1, max_value=10, value=5)
            rsi_uppers = list(range(rsi_upper_min, rsi_upper_max + 1, rsi_upper_step))
            param_ranges['rsi_upper'] = rsi_uppers
            
            rsi_lower_min = st.number_input("RSI下限最小值", min_value=10, max_value=50, value=20)
            rsi_lower_max = st.number_input("RSI下限最大值", min_value=rsi_lower_min, max_value=50, value=30)
            rsi_lower_step = st.number_input("RSI下限步长", min_value=1, max_value=10, value=5)
            rsi_lowers = list(range(rsi_lower_min, rsi_lower_max + 1, rsi_lower_step))
            param_ranges['rsi_lower'] = rsi_lowers
        
        # 通用参数 - 止盈止损
        profit_min = st.number_input("止盈比例最小值", min_value=0.01, max_value=0.10, value=0.02, format="%.2f")
        profit_max = st.number_input("止盈比例最大值", min_value=profit_min, max_value=0.20, value=0.05, format="%.2f")
        profit_step = st.number_input("止盈比例步长", min_value=0.01, max_value=0.05, value=0.01, format="%.2f")
        profits = [round(p, 2) for p in np.arange(profit_min, profit_max + 0.001, profit_step)]
        param_ranges['profit_target'] = profits

        loss_min = st.number_input("止损比例最小值", min_value=0.01, max_value=0.05, value=0.01, format="%.2f")
        loss_max = st.number_input("止损比例最大值", min_value=loss_min, max_value=0.10, value=0.03, format="%.2f")
        loss_step = st.number_input("止损比例步长", min_value=0.01, max_value=0.02, value=0.01, format="%.2f")
        losses = [round(l, 2) for l in np.arange(loss_min, loss_max + 0.001, loss_step)]
        param_ranges['stop_loss'] = losses
        
        # 计算参数组合总数
        max_combinations = 1
        for param_values in param_ranges.values():
            max_combinations *= len(param_values)
        
        st.info(f"总参数组合数: {max_combinations}")
    
    with col2:
        st.subheader("优化设置")
        
        # 选择股票
        try:
            all_stocks = data_handler.get_selected_stocks()
        except Exception as e:
            st.error(f"获取股票列表失败: {str(e)}")
            all_stocks = []
        
        if not all_stocks:
            st.warning("没有找到可用的股票，请先在实盘交易页面添加股票")
        else:
            selected_stocks = st.multiselect(
                "选择用于优化的股票",
                options=all_stocks,
                default=[all_stocks[0]] if all_stocks else None,
                help="选择1-3只股票进行参数优化（股票越多，优化时间越长）"
            )
        
        optimization_metric = st.selectbox(
            "优化目标",
            ["夏普比率", "年化收益率", "最大回撤", "胜率"],
            help="选择要优化的指标"
        )
        
        metric_map = {
            "夏普比率": "sharpe_ratio", 
            "年化收益率": "annual_return", 
            "最大回撤": "max_drawdown", 
            "胜率": "win_rate"
        }
        
        max_parallel = st.number_input(
            "最大并行任务数",
            min_value=1,
            max_value=10,
            value=min(4, len(selected_stocks) if 'selected_stocks' in locals() else 1),
            help="同时运行的回测任务数量"
        )
        
        # 初始资金
        opt_initial_capital = st.number_input(
            "优化初始资金 (元)",
            min_value=10000,
            max_value=1000000,
            value=100000,
            step=10000,
            help="优化时使用的初始资金"
        )
    
    # 优化按钮
    if st.button("开始优化", type="primary"):
        if 'selected_stocks' not in locals() or not selected_stocks:
            st.error("请至少选择一只股票进行优化")
        elif max_combinations > 500:
            st.warning("参数组合过多，可能需要较长时间。建议减少参数范围或增加步长。")
            if st.button("确认继续", key="confirm_optimize"):
                st.info("参数优化任务已提交，请查看任务管理页面获取进度")
                
                # 创建任务
                from quant_app.core.task_manager import TaskType, TaskStatus, task_manager
                import json
                import itertools
                
                task_id = task_manager.create_task(
                    TaskType.OPTIMIZATION,
                    strategy_name=selected_strategy_name,
                    params={
                        'stocks': selected_stocks,
                        'param_ranges': param_ranges,
                        'start_date': start_date.strftime('%Y%m%d'),
                        'end_date': end_date.strftime('%Y%m%d'),
                        'initial_cash': opt_initial_capital,
                        'combinations_count': max_combinations,
                        'optimization_metric': metric_map[optimization_metric],
                        'max_parallel': max_parallel
                    }
                )
                
                # 更新任务状态
                task_manager.update_task_status(task_id, TaskStatus.RUNNING)
                st.session_state.running_tasks.append(task_id)
                st.success(f"参数优化任务已启动，任务ID: {task_id}")
        else:
            st.info("参数优化任务已提交，请查看任务管理页面获取进度")
            
            # 创建任务
            from quant_app.core.task_manager import TaskType, TaskStatus, task_manager
            import json
            import itertools
            
            task_id = task_manager.create_task(
                TaskType.OPTIMIZATION,
                strategy_name=selected_strategy_name,
                params={
                    'stocks': selected_stocks,
                    'param_ranges': param_ranges,
                    'start_date': start_date.strftime('%Y%m%d'),
                    'end_date': end_date.strftime('%Y%m%d'),
                    'initial_cash': opt_initial_capital,
                    'combinations_count': max_combinations,
                    'optimization_metric': metric_map[optimization_metric],
                    'max_parallel': max_parallel
                }
            )
            
            # 更新任务状态
            task_manager.update_task_status(task_id, TaskStatus.RUNNING)
            st.session_state.running_tasks.append(task_id)
            st.success(f"参数优化任务已启动，任务ID: {task_id}")

# 历史优化结果
with st.expander("历史优化结果", expanded=False):
    # 获取历史优化结果
    from quant_app.core.task_manager import task_manager
    optimization_results = task_manager.get_optimization_results(limit=20)
    
    if not optimization_results:
        st.info("暂无历史优化结果")
    else:
        # 转换为DataFrame
        results_df = pd.DataFrame([
            {
                'ID': r['id'],
                '策略': r['strategy_name'],
                '股票': ', '.join(r['stocks']),
                '日期范围': f"{r['start_date']} - {r['end_date']}",
                '夏普比率': r['sharpe_ratio'],
                '年化收益率': r['annual_return'],
                '最大回撤': r['max_drawdown'],
                '胜率': r['win_rate'],
                '创建时间': r['created_at']
            }
            for r in optimization_results
        ])
        
        # 显示结果表格
        st.dataframe(results_df, use_container_width=True)
        
        # 选择查看详情
        selected_result_id = st.selectbox(
            "选择查看详情",
            options=[r['id'] for r in optimization_results],
            format_func=lambda x: f"ID {x}: {next((r['strategy_name'] for r in optimization_results if r['id'] == x), '')}"
        )
        
        if selected_result_id:
            # 获取选中的结果
            selected_result = next((r for r in optimization_results if r['id'] == selected_result_id), None)
            
            if selected_result:
                # 显示基本信息
                col1, col2 = st.columns(2)
                with col1:
                    st.write(f"**策略**: {selected_result['strategy_name']}")
                    st.write(f"**股票**: {', '.join(selected_result['stocks'])}")
                    st.write(f"**日期范围**: {selected_result['start_date']} - {selected_result['end_date']}")
                
                with col2:
                    st.write(f"**任务ID**: {selected_result['task_id']}")
                    st.write(f"**创建时间**: {selected_result['created_at']}")
                
                # 显示指标
                st.subheader("性能指标")
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("夏普比率", f"{selected_result['sharpe_ratio']:.4f}")
                
                with col2:
                    st.metric("年化收益率", f"{selected_result['annual_return']:.2f}%")
                
                with col3:
                    st.metric("最大回撤", f"{selected_result['max_drawdown']:.2f}%")
                
                with col4:
                    st.metric("胜率", f"{selected_result['win_rate']:.2%}")
                
                # 显示参数
                st.subheader("最佳参数")
                st.json(selected_result['params'])
                
                # 应用按钮
                if st.button("应用该参数", type="primary"):
                    # 获取当前策略配置
                    strategy_config = db_config_manager.get_config('strategy_config', {})
                    
                    # 确保策略配置存在
                    if not strategy_config:
                        strategy_config = {
                            'active_strategy': selected_result['strategy_name'],
                            'strategies': {}
                        }
                    
                    # 创建自定义策略版本
                    custom_strategy_name = f"{selected_result['strategy_name']}_result_{selected_result_id}"
                    
                    # 检查是否已存在，如果是则更新
                    if 'strategies' not in strategy_config:
                        strategy_config['strategies'] = {}
                    
                    # 确保能获取原策略信息
                    try:
                        strategies = data_handler.get_strategies()
                        original_strategy = next((s for s in strategies if s['name'] == selected_result['strategy_name']), {})
                        
                        strategy_config['strategies'][custom_strategy_name] = {
                            'class': original_strategy.get('class', ''),
                            'description': f"优化结果 #{selected_result_id} (夏普比率: {selected_result['sharpe_ratio']:.2f})",
                            'module': original_strategy.get('module', ''),
                            'parameters': selected_result['params']
                        }
                        
                        # 保存到配置
                        db_config_manager.set_config('strategy_config', strategy_config)
                        
                        st.success(f"已创建优化策略: {custom_strategy_name}")
                    except Exception as e:
                        st.error(f"应用参数失败: {str(e)}")

# 4. 历史回测记录
st.header("📜 历史回测记录")

# 这里可以从任务管理器获取历史回测任务
# 暂时显示提示信息
st.info('请在"任务管理"页面查看所有历史回测记录')

# 刷新按钮
if st.button("🔄 刷新页面"):
    st.rerun() 