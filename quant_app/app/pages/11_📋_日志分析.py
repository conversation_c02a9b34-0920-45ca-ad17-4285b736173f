"""
日志分析页面 - 深度分析交易日志和回测结果
"""
import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
import sys
import os
import glob
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

try:
    from log_analyzer import TradeLogAnalyzer
except ImportError:
    st.error("无法导入日志分析器，请检查log_analyzer.py文件")
    TradeLogAnalyzer = None

# 页面配置
st.set_page_config(page_title="日志分析 - 量化交易系统", page_icon="📋", layout="wide")

# 标题
st.title("📋 日志分析")
st.markdown("深度分析交易日志，提取交易记录和性能指标")
st.markdown("---")

# 创建标签页
tab1, tab2, tab3, tab4 = st.tabs([
    "📁 日志文件", "📊 交易分析", "📈 性能指标", "📋 详细报告"
])

# Tab 1: 日志文件管理
with tab1:
    st.header("日志文件管理")
    
    # 日志目录选择
    col1, col2 = st.columns([2, 1])
    
    with col1:
        log_directory = st.text_input(
            "日志目录",
            value="logs",
            help="输入日志文件所在目录"
        )
    
    with col2:
        if st.button("🔍 扫描日志文件"):
            st.rerun()
    
    # 扫描日志文件
    if os.path.exists(log_directory):
        # 查找所有日志文件
        log_patterns = [
            os.path.join(log_directory, "*.log"),
            os.path.join(log_directory, "**", "*.log")
        ]
        
        all_log_files = []
        for pattern in log_patterns:
            all_log_files.extend(glob.glob(pattern, recursive=True))
        
        if all_log_files:
            st.success(f"找到 {len(all_log_files)} 个日志文件")
            
            # 按类型分类日志文件
            strategy_logs = []
            backtest_logs = []
            broker_logs = []
            other_logs = []
            
            for log_file in all_log_files:
                filename = os.path.basename(log_file)
                if 'strategy' in filename:
                    strategy_logs.append(log_file)
                elif 'backtest' in filename:
                    backtest_logs.append(log_file)
                elif 'broker' in filename or 'qmt' in filename:
                    broker_logs.append(log_file)
                else:
                    other_logs.append(log_file)
            
            # 显示分类统计
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("策略日志", len(strategy_logs))
            
            with col2:
                st.metric("回测日志", len(backtest_logs))
            
            with col3:
                st.metric("经纪商日志", len(broker_logs))
            
            with col4:
                st.metric("其他日志", len(other_logs))
            
            # 日志文件列表
            st.subheader("日志文件列表")
            
            # 文件类型过滤
            file_type_filter = st.selectbox(
                "文件类型过滤",
                ["全部", "策略日志", "回测日志", "经纪商日志", "其他日志"]
            )
            
            if file_type_filter == "策略日志":
                filtered_files = strategy_logs
            elif file_type_filter == "回测日志":
                filtered_files = backtest_logs
            elif file_type_filter == "经纪商日志":
                filtered_files = broker_logs
            elif file_type_filter == "其他日志":
                filtered_files = other_logs
            else:
                filtered_files = all_log_files
            
            # 显示文件信息
            if filtered_files:
                file_data = []
                for log_file in filtered_files[:50]:  # 限制显示数量
                    try:
                        file_stat = os.stat(log_file)
                        file_data.append({
                            "文件名": os.path.basename(log_file),
                            "路径": log_file,
                            "大小(KB)": f"{file_stat.st_size / 1024:.1f}",
                            "修改时间": datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                            "类型": "策略" if 'strategy' in log_file else 
                                   "回测" if 'backtest' in log_file else 
                                   "经纪商" if ('broker' in log_file or 'qmt' in log_file) else "其他"
                        })
                    except Exception as e:
                        st.warning(f"读取文件信息失败: {log_file} - {str(e)}")
                
                df = pd.DataFrame(file_data)
                st.dataframe(df, use_container_width=True)
                
                if len(filtered_files) > 50:
                    st.info(f"还有 {len(filtered_files) - 50} 个文件未显示")
            else:
                st.info("该类型下没有日志文件")
        else:
            st.warning("未找到日志文件")
    else:
        st.error(f"日志目录不存在: {log_directory}")

# Tab 2: 交易分析
with tab2:
    st.header("交易记录分析")
    
    if TradeLogAnalyzer is None:
        st.error("日志分析器不可用")
    else:
        # 分析配置
        col1, col2 = st.columns([2, 1])
        
        with col1:
            analysis_mode = st.radio(
                "分析模式",
                ["分析指定文件", "分析整个目录", "分析最新文件"],
                help="选择要分析的日志范围"
            )
        
        with col2:
            if st.button("🚀 开始分析", type="primary"):
                with st.spinner("正在分析日志文件..."):
                    try:
                        analyzer = TradeLogAnalyzer()
                        
                        if analysis_mode == "分析指定文件":
                            # 让用户选择文件
                            if os.path.exists("logs"):
                                log_files = glob.glob("logs/*.log")
                                if log_files:
                                    selected_file = st.selectbox("选择日志文件", log_files)
                                    if selected_file:
                                        trades = analyzer.parse_log_file(selected_file)
                                        st.session_state.analyzed_trades = trades
                                        st.success(f"分析完成，找到 {len(trades)} 条交易记录")
                                else:
                                    st.error("未找到日志文件")
                            else:
                                st.error("logs目录不存在")
                        
                        elif analysis_mode == "分析整个目录":
                            # 分析整个logs目录
                            if os.path.exists("logs"):
                                all_trades = []
                                log_files = glob.glob("logs/**/*.log", recursive=True)
                                
                                progress_bar = st.progress(0)
                                for i, log_file in enumerate(log_files):
                                    trades = analyzer.parse_log_file(log_file)
                                    all_trades.extend(trades)
                                    progress_bar.progress((i + 1) / len(log_files))
                                
                                st.session_state.analyzed_trades = all_trades
                                st.success(f"分析完成，总共找到 {len(all_trades)} 条交易记录")
                            else:
                                st.error("logs目录不存在")
                        
                        else:  # 分析最新文件
                            if os.path.exists("logs"):
                                log_files = glob.glob("logs/*.log")
                                if log_files:
                                    # 找到最新的文件
                                    latest_file = max(log_files, key=os.path.getmtime)
                                    trades = analyzer.parse_log_file(latest_file)
                                    st.session_state.analyzed_trades = trades
                                    st.success(f"分析最新文件 {os.path.basename(latest_file)}，找到 {len(trades)} 条交易记录")
                                else:
                                    st.error("未找到日志文件")
                            else:
                                st.error("logs目录不存在")
                    
                    except Exception as e:
                        st.error(f"分析失败: {str(e)}")
        
        # 显示分析结果
        if 'analyzed_trades' in st.session_state and st.session_state.analyzed_trades:
            trades = st.session_state.analyzed_trades
            
            st.subheader("交易记录概览")
            
            # 基本统计
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("总交易数", len(trades))
            
            with col2:
                executed_trades = [t for t in trades if t.get('execution_status') == '已执行']
                st.metric("已执行交易", len(executed_trades))
            
            with col3:
                profitable_trades = [t for t in executed_trades if t.get('profit_rate', 0) > 0]
                win_rate = len(profitable_trades) / len(executed_trades) * 100 if executed_trades else 0
                st.metric("胜率", f"{win_rate:.1f}%")
            
            with col4:
                total_profit = sum(t.get('profit_rate', 0) for t in executed_trades)
                avg_profit = total_profit / len(executed_trades) if executed_trades else 0
                st.metric("平均收益率", f"{avg_profit:.2f}%")
            
            # 交易记录表格
            st.subheader("交易记录详情")
            
            # 转换为DataFrame
            trade_data = []
            for trade in trades:
                trade_data.append({
                    "时间": trade.get('timestamp', '').strftime('%Y-%m-%d %H:%M:%S') if isinstance(trade.get('timestamp'), datetime) else str(trade.get('timestamp', '')),
                    "股票": trade.get('stock_code', ''),
                    "买入信号": trade.get('buy_signal', ''),
                    "买入价": f"{trade.get('buy_price', 0):.2f}",
                    "卖出信号": trade.get('sell_signal', ''),
                    "卖出价": f"{trade.get('sell_price', 0):.2f}",
                    "收益率": f"{trade.get('profit_rate', 0):.2f}%",
                    "状态": trade.get('execution_status', ''),
                    "类型": trade.get('signal_type', '')
                })
            
            df = pd.DataFrame(trade_data)
            
            # 过滤选项
            col1, col2 = st.columns(2)
            
            with col1:
                status_filter = st.selectbox(
                    "状态过滤",
                    ["全部", "已执行", "未执行"]
                )
            
            with col2:
                type_filter = st.selectbox(
                    "类型过滤",
                    ["全部", "完整交易", "仅信号", "交易记录"]
                )
            
            # 应用过滤
            filtered_df = df.copy()
            
            if status_filter != "全部":
                filtered_df = filtered_df[filtered_df["状态"] == status_filter]
            
            if type_filter != "全部":
                filtered_df = filtered_df[filtered_df["类型"] == type_filter]
            
            st.dataframe(filtered_df, use_container_width=True)
            
            # 导出功能
            if st.button("📥 导出交易记录"):
                csv = filtered_df.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    label="下载CSV文件",
                    data=csv,
                    file_name=f"trade_records_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )

# Tab 3: 性能指标
with tab3:
    st.header("性能指标分析")
    
    if 'analyzed_trades' in st.session_state and st.session_state.analyzed_trades:
        trades = st.session_state.analyzed_trades
        executed_trades = [t for t in trades if t.get('execution_status') == '已执行']
        
        if executed_trades:
            # 收益率分布
            st.subheader("收益率分布")
            
            profit_rates = [t.get('profit_rate', 0) for t in executed_trades]
            
            fig_hist = px.histogram(
                x=profit_rates,
                nbins=20,
                title="收益率分布直方图",
                labels={'x': '收益率 (%)', 'y': '交易次数'}
            )
            st.plotly_chart(fig_hist, use_container_width=True)
            
            # 累计收益曲线
            st.subheader("累计收益曲线")
            
            # 按时间排序
            sorted_trades = sorted(executed_trades, key=lambda x: x.get('timestamp', datetime.min))
            
            cumulative_returns = []
            cumulative_return = 0
            dates = []
            
            for trade in sorted_trades:
                cumulative_return += trade.get('profit_rate', 0)
                cumulative_returns.append(cumulative_return)
                dates.append(trade.get('timestamp', datetime.now()))
            
            fig_cumulative = go.Figure()
            fig_cumulative.add_trace(go.Scatter(
                x=dates,
                y=cumulative_returns,
                mode='lines',
                name='累计收益率',
                line=dict(color='blue', width=2)
            ))
            
            fig_cumulative.update_layout(
                title="累计收益率曲线",
                xaxis_title="时间",
                yaxis_title="累计收益率 (%)",
                height=400
            )
            
            st.plotly_chart(fig_cumulative, use_container_width=True)
            
            # 详细统计指标
            st.subheader("详细统计指标")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**基本指标**")
                
                total_trades = len(executed_trades)
                profitable_trades = len([t for t in executed_trades if t.get('profit_rate', 0) > 0])
                losing_trades = len([t for t in executed_trades if t.get('profit_rate', 0) < 0])
                
                st.metric("总交易数", total_trades)
                st.metric("盈利交易数", profitable_trades)
                st.metric("亏损交易数", losing_trades)
                st.metric("胜率", f"{profitable_trades/total_trades*100:.1f}%" if total_trades > 0 else "0%")
            
            with col2:
                st.write("**收益指标**")
                
                total_return = sum(profit_rates)
                avg_return = total_return / len(profit_rates) if profit_rates else 0
                max_return = max(profit_rates) if profit_rates else 0
                min_return = min(profit_rates) if profit_rates else 0
                
                st.metric("总收益率", f"{total_return:.2f}%")
                st.metric("平均收益率", f"{avg_return:.2f}%")
                st.metric("最大单次收益", f"{max_return:.2f}%")
                st.metric("最大单次亏损", f"{min_return:.2f}%")
            
            # 风险指标
            st.subheader("风险指标")
            
            if len(profit_rates) > 1:
                import numpy as np
                
                col1, col2 = st.columns(2)
                
                with col1:
                    volatility = np.std(profit_rates)
                    sharpe_ratio = avg_return / volatility if volatility > 0 else 0
                    
                    st.metric("收益率标准差", f"{volatility:.2f}%")
                    st.metric("夏普比率", f"{sharpe_ratio:.2f}")
                
                with col2:
                    # 最大回撤
                    peak = 0
                    max_drawdown = 0
                    
                    for cum_return in cumulative_returns:
                        if cum_return > peak:
                            peak = cum_return
                        drawdown = peak - cum_return
                        if drawdown > max_drawdown:
                            max_drawdown = drawdown
                    
                    st.metric("最大回撤", f"{max_drawdown:.2f}%")
                    
                    # 盈亏比
                    avg_profit = np.mean([r for r in profit_rates if r > 0]) if any(r > 0 for r in profit_rates) else 0
                    avg_loss = abs(np.mean([r for r in profit_rates if r < 0])) if any(r < 0 for r in profit_rates) else 0
                    profit_loss_ratio = avg_profit / avg_loss if avg_loss > 0 else float('inf')
                    
                    st.metric("盈亏比", f"{profit_loss_ratio:.2f}" if profit_loss_ratio != float('inf') else "∞")
        else:
            st.info("没有已执行的交易记录")
    else:
        st.info("请先在交易分析页面进行日志分析")

# Tab 4: 详细报告
with tab4:
    st.header("详细分析报告")
    
    if 'analyzed_trades' in st.session_state and st.session_state.analyzed_trades:
        trades = st.session_state.analyzed_trades
        
        # 生成报告
        if st.button("📋 生成详细报告", type="primary"):
            with st.spinner("正在生成报告..."):
                try:
                    # 这里可以调用更详细的分析功能
                    st.success("报告生成功能开发中...")
                    
                    # 显示一些基本的报告内容
                    st.subheader("交易分析报告")
                    
                    executed_trades = [t for t in trades if t.get('execution_status') == '已执行']
                    
                    report_content = f"""
                    ## 交易分析报告
                    
                    **生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                    
                    ### 基本统计
                    - 总交易记录数: {len(trades)}
                    - 已执行交易数: {len(executed_trades)}
                    - 未执行信号数: {len(trades) - len(executed_trades)}
                    
                    ### 执行情况分析
                    """
                    
                    if executed_trades:
                        profitable_trades = [t for t in executed_trades if t.get('profit_rate', 0) > 0]
                        total_return = sum(t.get('profit_rate', 0) for t in executed_trades)
                        
                        report_content += f"""
                        - 胜率: {len(profitable_trades)/len(executed_trades)*100:.1f}%
                        - 总收益率: {total_return:.2f}%
                        - 平均收益率: {total_return/len(executed_trades):.2f}%
                        """
                    
                    st.markdown(report_content)
                    
                    # 提供下载
                    st.download_button(
                        label="📥 下载报告",
                        data=report_content,
                        file_name=f"trade_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                        mime="text/markdown"
                    )
                    
                except Exception as e:
                    st.error(f"生成报告失败: {str(e)}")
    else:
        st.info("请先进行日志分析")

# 侧边栏 - 快速操作
st.sidebar.header("快速操作")

if st.sidebar.button("🔄 刷新页面"):
    st.rerun()

if st.sidebar.button("🗑️ 清除分析结果"):
    if 'analyzed_trades' in st.session_state:
        del st.session_state.analyzed_trades
    st.sidebar.success("分析结果已清除")

# 状态信息
st.sidebar.header("状态信息")

if 'analyzed_trades' in st.session_state:
    trades = st.session_state.analyzed_trades
    executed_trades = [t for t in trades if t.get('execution_status') == '已执行']
    
    st.sidebar.metric("已分析交易", len(trades))
    st.sidebar.metric("已执行交易", len(executed_trades))
    
    if executed_trades:
        profitable_trades = [t for t in executed_trades if t.get('profit_rate', 0) > 0]
        win_rate = len(profitable_trades) / len(executed_trades) * 100
        st.sidebar.metric("当前胜率", f"{win_rate:.1f}%")
else:
    st.sidebar.info("暂无分析结果")

# 检查日志目录状态
if os.path.exists("logs"):
    log_files = glob.glob("logs/**/*.log", recursive=True)
    st.sidebar.metric("日志文件数", len(log_files))
else:
    st.sidebar.warning("logs目录不存在")
