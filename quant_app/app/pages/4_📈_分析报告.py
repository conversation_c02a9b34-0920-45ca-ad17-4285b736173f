"""
分析报告页面 - 深入分析交易和回测结果
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from quant_app.core.task_manager import task_manager
from quant_app.core.log_parser import log_parser
from quant_app.core.execution_engine import execution_engine

# 页面配置
st.set_page_config(page_title="分析报告 - 量化交易系统", page_icon="📈", layout="wide")

# 标题
st.title("📈 分析报告")
st.markdown("深入分析交易和回测结果")
st.markdown("---")

# 任务选择
st.header("选择任务")

# 获取已完成的任务
from quant_app.core.task_manager import TaskStatus
completed_tasks = task_manager.list_tasks(status=TaskStatus.COMPLETED, limit=100)

if not completed_tasks:
    st.warning("暂无已完成的任务可供分析")
    st.stop()

# 任务选择
task_options = {
    f"{task['task_id']} - {task['strategy_name']} ({task['created_at']})": task['task_id']
    for task in completed_tasks
}

# 检查是否有预选任务
preselected_task_id = st.session_state.get('analyze_task_id')
if preselected_task_id and preselected_task_id in [t['task_id'] for t in completed_tasks]:
    default_index = list(task_options.values()).index(preselected_task_id)
else:
    default_index = 0

selected_task_key = st.selectbox(
    "选择要分析的任务",
    options=list(task_options.keys()),
    index=default_index
)

selected_task_id = task_options[selected_task_key]

# 获取任务信息
task_info = task_manager.get_task(selected_task_id)

if not task_info or not task_info.get('log_file'):
    st.error("无法获取任务日志文件")
    st.stop()

# 解析日志数据
with st.spinner("正在解析任务日志..."):
    parsed_data = log_parser.parse_log_file(task_info['log_file'])

if not parsed_data:
    st.error("日志解析失败")
    st.stop()

# 任务基本信息
col1, col2, col3 = st.columns([2, 1, 1])

with col1:
    st.info(f"""
    **任务ID**: {task_info['task_id']}  
    **策略**: {task_info['strategy_name']}  
    **创建时间**: {task_info['created_at']}
    """)

with col2:
    if st.button("📥 导出Excel报告"):
        output_file = execution_engine.export_results(selected_task_id)
        if output_file:
            st.success(f"报告已导出: {output_file}")

with col3:
    if st.button("🔄 刷新数据"):
        st.rerun()

# 创建标签页
tab1, tab2, tab3, tab4, tab5 = st.tabs(["📊 性能总览", "📈 收益分析", "💹 交易详情", "📉 风险分析", "🔍 交易模式"])

# Tab 1: 性能总览
with tab1:
    st.header("性能指标")
    
    # 获取性能指标
    metrics = log_parser.get_performance_metrics(parsed_data)
    
    if metrics:
        # 显示主要指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            total_return = metrics.get('总收益率', 0)
            st.metric(
                "总收益率",
                f"{total_return:.2f}%",
                delta_color="normal" if total_return > 0 else "inverse"
            )
        
        with col2:
            annual_return = metrics.get('年化收益率', 0)
            st.metric(
                "年化收益率",
                f"{annual_return:.2f}%",
                delta_color="normal" if annual_return > 0 else "inverse"
            )
        
        with col3:
            sharpe_ratio = metrics.get('夏普比率', 0)
            st.metric(
                "夏普比率",
                f"{sharpe_ratio:.2f}",
                delta_color="normal" if sharpe_ratio > 0.5 else "off"
            )
        
        with col4:
            max_drawdown = metrics.get('最大回撤', 0)
            st.metric(
                "最大回撤",
                f"{max_drawdown:.2f}%",
                delta_color="inverse" if max_drawdown > 20 else "normal"
            )
    
    # 交易统计
    st.header("交易统计")
    
    summary = parsed_data.get('summary', {})
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("总交易次数", summary.get('total_trades', 0))
        st.metric("总订单数", summary.get('total_orders', 0))
    
    with col2:
        st.metric("交易股票数", summary.get('unique_symbols', 0))
        st.metric("总成交量", f"{summary.get('total_volume', 0):,}")
    
    with col3:
        st.metric("总成交额", f"¥{summary.get('total_value', 0):,.2f}")
        st.metric("信号总数", summary.get('total_signals', 0))
    
    with col4:
        st.metric("错误数", summary.get('error_count', 0),
                  delta_color="inverse" if summary.get('error_count', 0) > 0 else "off")
        st.metric("警告数", summary.get('warning_count', 0))

# Tab 2: 收益分析
with tab2:
    st.header("收益曲线")
    
    # 获取资产组合数据
    portfolio_df = log_parser.get_portfolio_dataframe(parsed_data)
    
    if not portfolio_df.empty:
        # 收益曲线图
        fig = go.Figure()
        
        # 总资产曲线
        fig.add_trace(go.Scatter(
            x=portfolio_df['date'],
            y=portfolio_df['total_value'],
            mode='lines',
            name='总资产',
            line=dict(color='blue', width=2)
        ))
        
        # 现金曲线
        fig.add_trace(go.Scatter(
            x=portfolio_df['date'],
            y=portfolio_df['cash'],
            mode='lines',
            name='现金',
            line=dict(color='green', width=1)
        ))
        
        # 持仓市值曲线
        fig.add_trace(go.Scatter(
            x=portfolio_df['date'],
            y=portfolio_df['positions_value'],
            mode='lines',
            name='持仓市值',
            line=dict(color='orange', width=1)
        ))
        
        fig.update_layout(
            title='资产变化曲线',
            xaxis_title='日期',
            yaxis_title='金额 (元)',
            hovermode='x unified',
            height=500
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # 累计收益率曲线
        fig2 = go.Figure()
        
        fig2.add_trace(go.Scatter(
            x=portfolio_df['date'],
            y=portfolio_df['cumulative_return'],
            mode='lines',
            name='累计收益率',
            line=dict(color='purple', width=2),
            fill='tozeroy',
            fillcolor='rgba(128, 0, 128, 0.1)'
        ))
        
        # 添加零线
        fig2.add_hline(y=0, line_dash="dash", line_color="gray")
        
        fig2.update_layout(
            title='累计收益率曲线',
            xaxis_title='日期',
            yaxis_title='收益率 (%)',
            hovermode='x unified',
            height=400
        )
        
        st.plotly_chart(fig2, use_container_width=True)
        
        # 日收益分布
        st.header("收益分布")
        
        daily_returns = portfolio_df['return'].dropna()
        
        if len(daily_returns) > 0:
            col1, col2 = st.columns(2)
            
            with col1:
                # 直方图
                fig3 = px.histogram(
                    daily_returns,
                    nbins=30,
                    title='日收益率分布',
                    labels={'value': '日收益率 (%)', 'count': '频次'}
                )
                fig3.add_vline(x=0, line_dash="dash", line_color="red")
                fig3.add_vline(x=daily_returns.mean(), line_dash="dash", 
                              line_color="green", annotation_text="均值")
                st.plotly_chart(fig3, use_container_width=True)
            
            with col2:
                # 箱线图
                fig4 = go.Figure()
                fig4.add_trace(go.Box(
                    y=daily_returns,
                    name='日收益率',
                    boxpoints='outliers'
                ))
                fig4.update_layout(
                    title='日收益率箱线图',
                    yaxis_title='收益率 (%)'
                )
                st.plotly_chart(fig4, use_container_width=True)
    else:
        st.warning("无资产组合历史数据")

# Tab 3: 交易详情
with tab3:
    st.header("交易记录")
    
    # 获取交易数据
    trades_df = log_parser.get_trades_dataframe(parsed_data)
    
    if not trades_df.empty:
        # 交易统计
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("总交易笔数", len(trades_df))
        
        with col2:
            st.metric("平均交易金额", f"¥{trades_df['value'].mean():,.2f}")
        
        with col3:
            st.metric("单笔最大金额", f"¥{trades_df['value'].max():,.2f}")
        
        # 交易记录表格
        st.subheader("交易明细")
        
        # 格式化显示
        display_trades = trades_df.copy()
        display_trades['datetime'] = display_trades['datetime'].dt.strftime('%Y-%m-%d %H:%M:%S')
        display_trades['price'] = display_trades['price'].round(2)
        display_trades['value'] = display_trades['value'].round(2)
        
        st.dataframe(
            display_trades[['datetime', 'symbol', 'price', 'quantity', 'value']],
            use_container_width=True,
            hide_index=True
        )
        
        # 按股票统计
        st.subheader("按股票统计")
        
        symbol_stats = trades_df.groupby('symbol').agg({
            'quantity': ['count', 'sum'],
            'value': 'sum',
            'price': 'mean'
        }).round(2)
        
        symbol_stats.columns = ['交易次数', '总数量', '总金额', '均价']
        symbol_stats = symbol_stats.reset_index()
        
        st.dataframe(symbol_stats, use_container_width=True, hide_index=True)
        
        # 交易时间分布
        st.subheader("交易时间分布")
        
        trades_df['hour'] = trades_df['datetime'].dt.hour
        hourly_trades = trades_df.groupby('hour').size().reset_index(name='count')
        
        fig = px.bar(
            hourly_trades,
            x='hour',
            y='count',
            title='交易时间分布',
            labels={'hour': '小时', 'count': '交易次数'}
        )
        st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("无交易记录")

# Tab 4: 风险分析
with tab4:
    st.header("风险指标")
    
    portfolio_df = log_parser.get_portfolio_dataframe(parsed_data)
    
    if not portfolio_df.empty and len(portfolio_df) > 1:
        # 计算回撤
        portfolio_df['peak'] = portfolio_df['total_value'].cummax()
        portfolio_df['drawdown'] = (portfolio_df['total_value'] - portfolio_df['peak']) / portfolio_df['peak'] * 100
        
        # 回撤曲线
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=portfolio_df['date'],
            y=portfolio_df['drawdown'],
            mode='lines',
            name='回撤',
            line=dict(color='red', width=2),
            fill='tozeroy',
            fillcolor='rgba(255, 0, 0, 0.1)'
        ))
        
        fig.update_layout(
            title='回撤曲线',
            xaxis_title='日期',
            yaxis_title='回撤 (%)',
            hovermode='x unified',
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # 风险指标计算
        daily_returns = portfolio_df['return'].dropna()
        
        if len(daily_returns) > 0:
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                volatility = daily_returns.std() * np.sqrt(252)
                st.metric("年化波动率", f"{volatility:.2f}%")
            
            with col2:
                var_95 = np.percentile(daily_returns, 5)
                st.metric("VaR (95%)", f"{var_95:.2f}%")
            
            with col3:
                positive_days = (daily_returns > 0).sum()
                win_rate = positive_days / len(daily_returns) * 100
                st.metric("日胜率", f"{win_rate:.1f}%")
            
            with col4:
                if daily_returns[daily_returns < 0].any():
                    profit_loss_ratio = abs(daily_returns[daily_returns > 0].mean() / 
                                           daily_returns[daily_returns < 0].mean())
                    st.metric("盈亏比", f"{profit_loss_ratio:.2f}")
                else:
                    st.metric("盈亏比", "N/A")
        
        # 滚动指标
        st.header("滚动风险指标")
        
        window = st.slider("滚动窗口 (天)", 5, 60, 20)
        
        # 计算滚动指标
        portfolio_df['rolling_return'] = portfolio_df['return'].rolling(window).mean() * 252
        portfolio_df['rolling_volatility'] = portfolio_df['return'].rolling(window).std() * np.sqrt(252)
        portfolio_df['rolling_sharpe'] = (portfolio_df['rolling_return'] / 
                                         portfolio_df['rolling_volatility'].replace(0, np.nan))
        
        # 绘图
        fig2 = go.Figure()
        
        fig2.add_trace(go.Scatter(
            x=portfolio_df['date'],
            y=portfolio_df['rolling_volatility'],
            mode='lines',
            name='滚动波动率',
            line=dict(color='orange', width=2)
        ))
        
        fig2.update_layout(
            title=f'{window}日滚动波动率',
            xaxis_title='日期',
            yaxis_title='年化波动率 (%)',
            hovermode='x unified',
            height=400
        )
        
        st.plotly_chart(fig2, use_container_width=True)
    else:
        st.warning("数据不足，无法进行风险分析")

# Tab 5: 交易模式
with tab5:
    st.header("交易模式分析")
    
    # 分析交易模式
    patterns = log_parser.analyze_trading_patterns(parsed_data)
    
    if patterns:
        # 按时间分析
        col1, col2 = st.columns(2)
        
        with col1:
            # 按小时分布
            if patterns.get('by_hour'):
                hour_df = pd.DataFrame(
                    list(patterns['by_hour'].items()),
                    columns=['hour', 'count']
                )
                fig = px.bar(
                    hour_df,
                    x='hour',
                    y='count',
                    title='交易时段分布',
                    labels={'hour': '小时', 'count': '交易次数'}
                )
                st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # 按星期分布
            if patterns.get('by_weekday'):
                weekday_df = pd.DataFrame(
                    list(patterns['by_weekday'].items()),
                    columns=['weekday', 'count']
                )
                weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
                weekday_df['weekday_name'] = weekday_df['weekday'].map(
                    lambda x: weekday_names[x] if x < len(weekday_names) else str(x)
                )
                fig = px.bar(
                    weekday_df,
                    x='weekday_name',
                    y='count',
                    title='交易日分布',
                    labels={'weekday_name': '星期', 'count': '交易次数'}
                )
                st.plotly_chart(fig, use_container_width=True)
        
        # 按股票分析
        if patterns.get('by_symbol'):
            st.subheader("热门交易股票")
            
            symbol_data = []
            for symbol, stats in patterns['by_symbol'].items():
                symbol_data.append({
                    '股票代码': symbol,
                    '交易量': stats['quantity'],
                    '交易额': f"¥{stats['value']:,.2f}",
                    '平均价格': f"¥{stats['price']:.2f}"
                })
            
            symbol_df = pd.DataFrame(symbol_data)
            symbol_df = symbol_df.sort_values('交易量', ascending=False).head(20)
            
            st.dataframe(symbol_df, use_container_width=True, hide_index=True)
    
    # 信号分析
    st.header("信号分析")
    
    signal_stats = log_parser.get_signal_statistics(parsed_data)
    
    if signal_stats:
        col1, col2 = st.columns(2)
        
        with col1:
            st.metric("总信号数", signal_stats.get('total', 0))
            
            # 信号类型分布
            if signal_stats.get('by_type'):
                type_df = pd.DataFrame(
                    list(signal_stats['by_type'].items()),
                    columns=['type', 'count']
                )
                fig = px.pie(
                    type_df,
                    values='count',
                    names='type',
                    title='信号类型分布'
                )
                st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # 热门信号股票
            if signal_stats.get('by_symbol'):
                st.subheader("信号最多的股票")
                symbol_signal_df = pd.DataFrame(
                    list(signal_stats['by_symbol'].items())[:10],
                    columns=['股票', '信号数']
                ).sort_values('信号数', ascending=False)
                
                st.dataframe(symbol_signal_df, use_container_width=True, hide_index=True)
    else:
        st.info("无信号数据")

# 错误和警告
if parsed_data.get('errors') or parsed_data.get('warnings'):
    st.header("⚠️ 错误和警告")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if parsed_data.get('errors'):
            st.error(f"发现 {len(parsed_data['errors'])} 个错误")
            with st.expander("查看错误详情"):
                for error in parsed_data['errors'][:10]:
                    st.write(f"**{error['datetime']}**: {error['message']}")
    
    with col2:
        if parsed_data.get('warnings'):
            st.warning(f"发现 {len(parsed_data['warnings'])} 个警告")
            with st.expander("查看警告详情"):
                for warning in parsed_data['warnings'][:10]:
                    st.write(f"**{warning['datetime']}**: {warning['message']}") 