"""
策略管理页面 - 管理和配置交易策略
"""
import streamlit as st
import pandas as pd
import sys
import os
import yaml
import importlib.util
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from quant_app.core.db_config_manager import db_config_manager
from quant_app.core.data_handler import data_handler

# 页面配置
st.set_page_config(page_title="策略管理 - 量化交易系统", page_icon="🔧", layout="wide")

# 标题
st.title("🔧 策略管理")
st.markdown("管理和配置交易策略，设置策略参数")
st.markdown("---")

def load_config():
    """加载配置文件"""
    try:
        with open('config.yaml', 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        st.error(f"加载配置文件失败: {str(e)}")
        return {}

def save_config(config):
    """保存配置文件"""
    try:
        with open('config.yaml', 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        return True
    except Exception as e:
        st.error(f"保存配置文件失败: {str(e)}")
        return False

def get_available_strategies():
    """获取可用策略列表"""
    strategies = []
    
    # 项目根目录
    root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    
    # 查找策略文件
    strategy_files = []
    
    # 检查项目根目录下的策略文件
    for file in os.listdir(root_dir):
        if file.endswith('.py') and 'strategy' in file.lower():
            strategy_files.append(os.path.join(root_dir, file))
    
    # 检查strategies目录
    strategies_dir = os.path.join(root_dir, 'strategies')
    if os.path.exists(strategies_dir):
        for file in os.listdir(strategies_dir):
            if file.endswith('.py'):
                strategy_files.append(os.path.join(strategies_dir, file))
    
    # 检查quant_app/strategies目录
    quant_strategies_dir = os.path.join(root_dir, 'quant_app', 'strategies')
    if os.path.exists(quant_strategies_dir):
        for file in os.listdir(quant_strategies_dir):
            if file.endswith('.py'):
                strategy_files.append(os.path.join(quant_strategies_dir, file))
    
    # 解析策略文件
    for file_path in strategy_files:
        try:
            filename = os.path.basename(file_path)
            if filename.startswith('__'):
                continue
                
            module_name = filename[:-3]  # 去掉.py扩展名
            
            # 尝试加载模块获取策略类
            spec = importlib.util.spec_from_file_location(module_name, file_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 查找策略类
            strategy_classes = []
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and 
                    hasattr(attr, '__bases__') and 
                    any('Strategy' in str(base) for base in attr.__bases__)):
                    strategy_classes.append(attr_name)
            
            if strategy_classes:
                strategies.append({
                    'name': module_name,
                    'file': filename,
                    'path': file_path,
                    'classes': strategy_classes,
                    'description': f"策略文件: {filename}"
                })
                
        except Exception as e:
            st.warning(f"解析策略文件 {filename} 失败: {str(e)}")
    
    return strategies

# 创建标签页
tab1, tab2, tab3, tab4 = st.tabs([
    "📋 策略列表", "⚙️ 策略配置", "🔧 参数设置", "📊 策略验证"
])

# Tab 1: 策略列表
with tab1:
    st.header("可用策略列表")
    
    # 获取策略列表
    strategies = get_available_strategies()
    
    if not strategies:
        st.warning("未找到可用的策略文件")
    else:
        # 显示策略表格
        strategy_data = []
        for strategy in strategies:
            strategy_data.append({
                "策略名称": strategy['name'],
                "文件名": strategy['file'],
                "策略类": ', '.join(strategy['classes']),
                "描述": strategy['description']
            })
        
        df = pd.DataFrame(strategy_data)
        st.dataframe(df, use_container_width=True)
        
        # 策略详情
        st.subheader("策略详情")
        
        selected_strategy = st.selectbox(
            "选择策略查看详情",
            options=[s['name'] for s in strategies],
            help="选择要查看详情的策略"
        )
        
        if selected_strategy:
            strategy_info = next(s for s in strategies if s['name'] == selected_strategy)
            
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.write(f"**文件路径**: {strategy_info['path']}")
                st.write(f"**策略类**: {', '.join(strategy_info['classes'])}")
                
                # 尝试读取策略文件的文档字符串
                try:
                    with open(strategy_info['path'], 'r', encoding='utf-8') as f:
                        content = f.read()
                        # 提取文档字符串
                        import re
                        docstring_match = re.search(r'"""(.*?)"""', content, re.DOTALL)
                        if docstring_match:
                            st.write(f"**策略说明**:")
                            st.text(docstring_match.group(1).strip())
                except Exception as e:
                    st.write(f"无法读取策略说明: {str(e)}")
            
            with col2:
                if st.button("设为活跃策略", key=f"activate_{selected_strategy}"):
                    # 更新配置
                    config = load_config()
                    if 'strategy_config' not in config:
                        config['strategy_config'] = {}
                    
                    config['strategy_config']['active_strategy'] = selected_strategy
                    
                    if save_config(config):
                        st.success(f"已设置 {selected_strategy} 为活跃策略")
                        st.rerun()

# Tab 2: 策略配置
with tab2:
    st.header("策略配置")
    
    # 加载当前配置
    config = load_config()
    strategy_config = config.get('strategy_config', {})
    
    # 当前活跃策略
    active_strategy = strategy_config.get('active_strategy', '')
    
    if active_strategy:
        st.success(f"当前活跃策略: **{active_strategy}**")
    else:
        st.warning("未设置活跃策略")
    
    # 策略配置表单
    st.subheader("策略基本配置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # 策略选择
        strategies = get_available_strategies()
        strategy_names = [s['name'] for s in strategies]
        
        if strategy_names:
            new_active_strategy = st.selectbox(
                "选择活跃策略",
                options=strategy_names,
                index=strategy_names.index(active_strategy) if active_strategy in strategy_names else 0
            )
        else:
            st.error("未找到可用策略")
            new_active_strategy = ""
    
    with col2:
        # 策略描述
        strategy_description = st.text_input(
            "策略描述",
            value=strategy_config.get('strategies', {}).get(active_strategy, {}).get('description', ''),
            help="为策略添加描述信息"
        )
    
    # 保存策略配置
    if st.button("保存策略配置", type="primary"):
        if new_active_strategy:
            if 'strategy_config' not in config:
                config['strategy_config'] = {}
            if 'strategies' not in config['strategy_config']:
                config['strategy_config']['strategies'] = {}
            
            config['strategy_config']['active_strategy'] = new_active_strategy
            
            # 更新策略信息
            if new_active_strategy not in config['strategy_config']['strategies']:
                config['strategy_config']['strategies'][new_active_strategy] = {}
            
            config['strategy_config']['strategies'][new_active_strategy]['description'] = strategy_description
            
            if save_config(config):
                st.success("策略配置已保存")
                st.rerun()

# Tab 3: 参数设置
with tab3:
    st.header("策略参数设置")
    
    config = load_config()
    strategy_config = config.get('strategy_config', {})
    active_strategy = strategy_config.get('active_strategy', '')
    
    if not active_strategy:
        st.warning("请先设置活跃策略")
    else:
        st.info(f"当前策略: {active_strategy}")
        
        # 获取当前策略的参数
        current_params = strategy_config.get('strategies', {}).get(active_strategy, {}).get('parameters', {})
        
        st.subheader("策略参数")
        
        # 根据策略类型显示不同的参数设置
        if 'kdj' in active_strategy.lower():
            st.write("**KDJ策略参数**")
            
            col1, col2 = st.columns(2)
            
            with col1:
                kdj_period = st.number_input(
                    "KDJ周期", 
                    min_value=5, max_value=30, 
                    value=current_params.get('kdj_period', 9)
                )
                
                j_oversold = st.number_input(
                    "J值超卖线", 
                    min_value=0, max_value=50, 
                    value=current_params.get('j_oversold', 20)
                )
                
                volume_amplify_ratio = st.number_input(
                    "成交量放大倍数", 
                    min_value=1.0, max_value=3.0, step=0.1,
                    value=current_params.get('volume_amplify_ratio', 1.3)
                )
            
            with col2:
                volume_ma_period = st.number_input(
                    "成交量均线周期", 
                    min_value=5, max_value=50, 
                    value=current_params.get('volume_ma_period', 20)
                )
                
                j_overbought = st.number_input(
                    "J值超买线", 
                    min_value=50, max_value=100, 
                    value=current_params.get('j_overbought', 80)
                )
                
                profit_target = st.number_input(
                    "止盈目标", 
                    min_value=0.01, max_value=0.20, step=0.01,
                    value=current_params.get('profit_target', 0.05)
                )
            
            # 保存参数
            if st.button("保存KDJ参数"):
                new_params = {
                    'kdj_period': kdj_period,
                    'volume_ma_period': volume_ma_period,
                    'j_oversold': j_oversold,
                    'j_overbought': j_overbought,
                    'volume_amplify_ratio': volume_amplify_ratio,
                    'profit_target': profit_target
                }
                
                if 'strategies' not in config['strategy_config']:
                    config['strategy_config']['strategies'] = {}
                if active_strategy not in config['strategy_config']['strategies']:
                    config['strategy_config']['strategies'][active_strategy] = {}
                
                config['strategy_config']['strategies'][active_strategy]['parameters'] = new_params
                
                if save_config(config):
                    st.success("KDJ策略参数已保存")
        
        elif 'rsi' in active_strategy.lower() or 'connors' in active_strategy.lower():
            st.write("**RSI策略参数**")
            
            col1, col2 = st.columns(2)
            
            with col1:
                rsi_period = st.number_input(
                    "RSI周期", 
                    min_value=2, max_value=20, 
                    value=current_params.get('rsi_period', 3)
                )
                
                rsi_upper = st.number_input(
                    "RSI上限", 
                    min_value=50, max_value=90, 
                    value=current_params.get('rsi_upper', 70)
                )
            
            with col2:
                rsi_lower = st.number_input(
                    "RSI下限", 
                    min_value=10, max_value=50, 
                    value=current_params.get('rsi_lower', 30)
                )
                
                stop_loss = st.number_input(
                    "止损比例", 
                    min_value=0.01, max_value=0.10, step=0.01,
                    value=current_params.get('stop_loss', 0.02)
                )
            
            # 保存参数
            if st.button("保存RSI参数"):
                new_params = {
                    'rsi_period': rsi_period,
                    'rsi_upper': rsi_upper,
                    'rsi_lower': rsi_lower,
                    'stop_loss': stop_loss
                }
                
                if 'strategies' not in config['strategy_config']:
                    config['strategy_config']['strategies'] = {}
                if active_strategy not in config['strategy_config']['strategies']:
                    config['strategy_config']['strategies'][active_strategy] = {}
                
                config['strategy_config']['strategies'][active_strategy]['parameters'] = new_params
                
                if save_config(config):
                    st.success("RSI策略参数已保存")
        
        else:
            st.write("**通用策略参数**")
            
            # 通用参数设置
            col1, col2 = st.columns(2)
            
            with col1:
                profit_target = st.number_input(
                    "止盈目标", 
                    min_value=0.01, max_value=0.20, step=0.01,
                    value=current_params.get('profit_target', 0.05)
                )
                
                position_size = st.number_input(
                    "仓位大小", 
                    min_value=0.1, max_value=1.0, step=0.1,
                    value=current_params.get('position_size', 0.3)
                )
            
            with col2:
                stop_loss = st.number_input(
                    "止损比例", 
                    min_value=0.01, max_value=0.10, step=0.01,
                    value=current_params.get('stop_loss', 0.02)
                )
                
                max_positions = st.number_input(
                    "最大持仓数", 
                    min_value=1, max_value=10, 
                    value=current_params.get('max_positions', 3)
                )
            
            # 保存参数
            if st.button("保存通用参数"):
                new_params = {
                    'profit_target': profit_target,
                    'stop_loss': stop_loss,
                    'position_size': position_size,
                    'max_positions': max_positions
                }
                
                if 'strategies' not in config['strategy_config']:
                    config['strategy_config']['strategies'] = {}
                if active_strategy not in config['strategy_config']['strategies']:
                    config['strategy_config']['strategies'][active_strategy] = {}
                
                config['strategy_config']['strategies'][active_strategy]['parameters'] = new_params
                
                if save_config(config):
                    st.success("通用策略参数已保存")

# Tab 4: 策略验证
with tab4:
    st.header("策略验证")
    
    config = load_config()
    active_strategy = config.get('strategy_config', {}).get('active_strategy', '')
    
    if not active_strategy:
        st.warning("请先设置活跃策略")
    else:
        st.info(f"验证策略: {active_strategy}")
        
        # 策略文件检查
        st.subheader("策略文件检查")
        
        strategies = get_available_strategies()
        strategy_info = next((s for s in strategies if s['name'] == active_strategy), None)
        
        if strategy_info:
            st.success(f"✅ 策略文件存在: {strategy_info['file']}")
            st.success(f"✅ 策略类: {', '.join(strategy_info['classes'])}")
        else:
            st.error(f"❌ 策略文件不存在: {active_strategy}")
        
        # 参数检查
        st.subheader("参数检查")
        
        strategy_config = config.get('strategy_config', {})
        strategy_params = strategy_config.get('strategies', {}).get(active_strategy, {}).get('parameters', {})
        
        if strategy_params:
            st.success(f"✅ 策略参数已配置 ({len(strategy_params)} 个参数)")
            
            # 显示参数
            param_df = pd.DataFrame([
                {"参数名": k, "参数值": v, "类型": type(v).__name__}
                for k, v in strategy_params.items()
            ])
            st.dataframe(param_df, use_container_width=True)
        else:
            st.warning("⚠️ 策略参数未配置，将使用默认参数")
        
        # 配置完整性检查
        st.subheader("配置完整性检查")
        
        required_configs = ['backtest', 'logging', 'strategy']
        missing_configs = []
        
        for req_config in required_configs:
            if req_config in config:
                st.success(f"✅ {req_config} 配置存在")
            else:
                st.error(f"❌ {req_config} 配置缺失")
                missing_configs.append(req_config)
        
        if not missing_configs:
            st.success("🎉 所有配置检查通过，策略可以正常使用")
        else:
            st.error(f"❌ 缺失配置: {', '.join(missing_configs)}")

# 侧边栏 - 快速操作
st.sidebar.header("快速操作")

if st.sidebar.button("重新加载策略"):
    st.rerun()

if st.sidebar.button("导出策略配置"):
    config = load_config()
    strategy_config = config.get('strategy_config', {})
    
    if strategy_config:
        config_str = yaml.dump(strategy_config, default_flow_style=False, allow_unicode=True)
        st.sidebar.download_button(
            label="下载配置文件",
            data=config_str,
            file_name=f"strategy_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml",
            mime="text/yaml"
        )
    else:
        st.sidebar.error("没有策略配置可导出")

# 状态信息
st.sidebar.header("状态信息")
config = load_config()
active_strategy = config.get('strategy_config', {}).get('active_strategy', '')

if active_strategy:
    st.sidebar.success(f"活跃策略: {active_strategy}")
else:
    st.sidebar.warning("未设置活跃策略")

strategies = get_available_strategies()
st.sidebar.info(f"可用策略: {len(strategies)} 个")
