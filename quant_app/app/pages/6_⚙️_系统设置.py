"""
系统设置页面 - 管理系统配置
"""
import streamlit as st
import pandas as pd
import json
import yaml
from datetime import datetime
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from quant_app.core.db_config_manager import db_config_manager
from quant_app.core.task_manager import task_manager
from quant_app.core.data_handler import data_handler

# 页面配置
st.set_page_config(page_title="系统设置 - 量化交易系统", page_icon="⚙️", layout="wide")

# 标题
st.title("⚙️ 系统设置")
st.markdown("管理系统配置参数")
st.markdown("---")

# 创建标签页
tab1, tab2, tab3, tab4, tab5 = st.tabs([
    "🔌 交易接口", "💼 风险管理", "📊 回测设置", 
    "🖥️ 系统参数", "📋 配置管理"
])

# Tab 1: 交易接口设置
with tab1:
    st.header("QMT交易接口配置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("QMT连接设置")
        
        # QMT连接状态检查
        try:
            from xtquant import xtdata
            qmt_available = True
            st.success("✅ xtquant模块已安装")
        except ImportError:
            qmt_available = False
            st.error("❌ xtquant模块未安装")
        
        account_id = st.text_input(
            "交易账户ID",
            value=db_config_manager.get_config('qmt', 'account_id', ''),
            help="您的QMT交易账户ID"
        )
        
        # 交易模式选择
        is_live = st.selectbox(
            "交易模式",
            options=[False, True],
            format_func=lambda x: "实盘交易" if x else "回测模式",
            index=1 if db_config_manager.get_config('qmt', 'is_live', False) else 0,
            help="选择交易模式：回测或实盘"
        )
        
        # 测试连接按钮
        if st.button("测试QMT连接", type="primary"):
            if qmt_available:
                try:
                    from xtquant import xtdata
                    test_result = xtdata.get_stock_list_in_sector('沪深A股')
                    if test_result is not None and len(test_result) > 0:
                        st.success("✅ QMT连接正常")
                        st.info(f"获取到 {len(test_result)} 只股票数据")
                    else:
                        st.error("❌ QMT连接失败：无法获取股票数据")
                except Exception as e:
                    st.error(f"❌ QMT连接失败：{str(e)}")
            else:
                st.error("❌ 请先安装xtquant模块")
    
    with col2:
        st.subheader("使用说明")
        
        # 这里可以显示使用相关信息
        st.info("""
        **QMT实盘交易要求:**
        - 安装并启动QMT客户端
        - 安装xtquant Python库
        - 登录QMT账户
        - 确保数据权限正常
        
        **回测模式:**
        - 使用历史数据进行策略验证
        - 不涉及真实资金
        - 支持批量回测
        """)
        
        st.warning("""
        **⚠️ 实盘交易风险提醒:**
        - 实盘交易涉及真实资金
        - 请充分测试策略后再使用
        - 建议先小资金试运行
        """)
        
        # 保存按钮
        if st.button("保存QMT设置"):
            db_config_manager.set_config('qmt', 'account_id', account_id)
            db_config_manager.set_config('qmt', 'is_live', is_live)
            st.success("QMT设置已保存")

# Tab 2: 风险管理
with tab2:
    st.header("风险管理参数")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("仓位控制")
        
        max_position_ratio = st.slider(
            "最大仓位比例",
            min_value=0.1,
            max_value=1.0,
            value=float(db_config_manager.get_config('risk', 'max_position_ratio', 0.8)),
            step=0.05,
            format="%.2f",
            help="总资金的最大使用比例"
        )
        
        single_stock_limit = st.slider(
            "单只股票最大仓位",
            min_value=0.05,
            max_value=0.5,
            value=float(db_config_manager.get_config('risk', 'single_stock_limit', 0.2)),
            step=0.05,
            format="%.2f",
            help="单只股票占总资金的最大比例"
        )
        
        max_holding_stocks = st.number_input(
            "最大持仓股票数",
            min_value=1,
            max_value=50,
            value=int(db_config_manager.get_config('risk', 'max_holding_stocks', 10)),
            help="同时持有的最大股票数量"
        )
    
    with col2:
        st.subheader("止损止盈")
        
        stop_loss_ratio = st.slider(
            "止损比例",
            min_value=0.01,
            max_value=0.2,
            value=float(db_config_manager.get_config('risk', 'stop_loss_ratio', 0.05)),
            step=0.01,
            format="%.2f",
            help="单笔交易的最大亏损比例"
        )
        
        take_profit_ratio = st.slider(
            "止盈比例",
            min_value=0.05,
            max_value=0.5,
            value=float(db_config_manager.get_config('risk', 'take_profit_ratio', 0.2)),
            step=0.05,
            format="%.2f",
            help="单笔交易的目标盈利比例"
        )
        
        trailing_stop = st.checkbox(
            "启用移动止损",
            value=db_config_manager.get_config('risk', 'trailing_stop', False),
            help="根据价格变化动态调整止损位"
        )
    
    if st.button("保存风险管理设置"):
        db_config_manager.set_config('risk', 'max_position_ratio', max_position_ratio)
        db_config_manager.set_config('risk', 'single_stock_limit', single_stock_limit)
        db_config_manager.set_config('risk', 'max_holding_stocks', max_holding_stocks)
        db_config_manager.set_config('risk', 'stop_loss_ratio', stop_loss_ratio)
        db_config_manager.set_config('risk', 'take_profit_ratio', take_profit_ratio)
        db_config_manager.set_config('risk', 'trailing_stop', trailing_stop)
        st.success("风险管理设置已保存")

# Tab 3: 回测设置
with tab3:
    st.header("回测参数设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("资金设置")
        
        initial_capital = st.number_input(
            "初始资金 (元)",
            min_value=10000,
            max_value=100000000,
            value=int(db_config_manager.get_config('backtest', 'initial_cash', 1000000)),
            step=10000,
            help="回测初始资金"
        )
        
        # 更新股票列表
        all_stock_codes = db_config_manager.get_config('backtest', 'stock_codes', [])
        stock_codes = st.text_area(
            "股票代码列表",
            value='\n'.join(all_stock_codes) if isinstance(all_stock_codes, list) else all_stock_codes,
            height=100,
            help="每行一个股票代码，例如: 000001.SZ"
        )
    
    with col2:
        st.subheader("交易成本设置")
        
        commission = st.number_input(
            "佣金费率",
            min_value=0.0,
            max_value=0.01,
            value=float(db_config_manager.get_config('backtest', 'commission.commission', 0.0003)),
            step=0.0001,
            format="%.5f",
            help="买卖股票的佣金费率"
        )
        
        stamp_duty = st.number_input(
            "印花税",
            min_value=0.0,
            max_value=0.01,
            value=float(db_config_manager.get_config('backtest', 'commission.stamp_duty', 0.001)),
            step=0.0001,
            format="%.5f",
            help="卖出股票的印花税率"
        )
        
        transfer_fee = st.number_input(
            "过户费",
            min_value=0.0,
            max_value=0.01,
            value=float(db_config_manager.get_config('backtest', 'commission.transfer_fee', 0.00002)),
            step=0.00001,
            format="%.6f",
            help="上海股票的过户费率"
        )
        
        slippage = st.number_input(
            "滑点设置",
            min_value=0.0,
            max_value=0.01,
            value=float(db_config_manager.get_config('backtest', 'slippage', 0.002)),
            step=0.001,
            format="%.3f",
            help="考虑市场冲击的价格滑点"
        )
        
        dismiss5 = st.checkbox(
            "豁免五元以下佣金",
            value=db_config_manager.get_config('backtest', 'commission.dismiss5', True),
            help="佣金低于5元时仍然收取5元"
        )
    
    if st.button("保存回测设置"):
        # 处理股票代码列表
        stock_list = [code.strip() for code in stock_codes.split('\n') if code.strip()]
        
        # 保存设置
        db_config_manager.set_config('backtest', 'initial_cash', initial_capital)
        db_config_manager.set_config('backtest', 'stock_codes', stock_list)
        db_config_manager.set_config('backtest', 'commission.commission', commission)
        db_config_manager.set_config('backtest', 'commission.stamp_duty', stamp_duty)
        db_config_manager.set_config('backtest', 'commission.transfer_fee', transfer_fee)
        db_config_manager.set_config('backtest', 'slippage', slippage)
        db_config_manager.set_config('backtest', 'commission.dismiss5', dismiss5)
        
        st.success("回测设置已保存")

# Tab 4: 系统参数
with tab4:
    st.header("系统参数设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("日志设置")
        
        log_level = st.selectbox(
            "日志级别",
            options=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
            index=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"].index(
                db_config_manager.get_config('logging', 'level', "INFO")
            ),
            help="设置日志记录的详细程度"
        )
        
        log_dir = st.text_input(
            "日志目录",
            value=db_config_manager.get_config('logging', 'log_dir', "logs"),
            help="日志文件保存的目录"
        )
        
        enable_log_analysis = st.checkbox(
            "启用日志分析",
            value=db_config_manager.get_config('logging', 'enable_log_analysis', True),
            help="自动分析交易日志"
        )
    
    with col2:
        st.subheader("性能设置")
        
        max_workers = st.number_input(
            "最大工作线程",
            min_value=1,
            max_value=32,
            value=int(db_config_manager.get_config('system', 'max_workers', 4)),
            help="数据处理和回测的最大并行线程数"
        )
        
        data_cache_days = st.number_input(
            "数据缓存天数",
            min_value=1,
            max_value=90,
            value=int(db_config_manager.get_config('system', 'data_cache_days', 7)),
            help="本地缓存行情数据的天数"
        )
        
        enable_plot_save = st.checkbox(
            "保存回测图表",
            value=db_config_manager.get_config('backtest', 'enable_plot_save', True),
            help="回测后保存交易图表"
        )
    
    if st.button("保存系统设置"):
        db_config_manager.set_config('logging', 'level', log_level)
        db_config_manager.set_config('logging', 'log_dir', log_dir)
        db_config_manager.set_config('logging', 'enable_log_analysis', enable_log_analysis)
        db_config_manager.set_config('system', 'max_workers', max_workers)
        db_config_manager.set_config('system', 'data_cache_days', data_cache_days)
        db_config_manager.set_config('backtest', 'enable_plot_save', enable_plot_save)
        st.success("系统设置已保存")

# Tab 5: 配置管理
with tab5:
    st.header("配置管理")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("配置导出/导入")
        
        # 导出配置到YAML
        if st.button("导出配置到YAML"):
            try:
                # 获取项目根目录
                root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
                yaml_path = os.path.join(root_dir, 'config.yaml')
                
                # 备份原配置文件
                if os.path.exists(yaml_path):
                    backup_path = f"{yaml_path}.{datetime.now().strftime('%Y%m%d%H%M%S')}.bak"
                    os.rename(yaml_path, backup_path)
                    st.info(f"已备份原配置文件到: {os.path.basename(backup_path)}")
                
                # 导出配置
                if db_config_manager.export_to_yaml(yaml_path):
                    st.success(f"配置已导出到: {os.path.basename(yaml_path)}")
                else:
                    st.error("导出配置失败")
            except Exception as e:
                st.error(f"导出配置出错: {str(e)}")
        
        # 从YAML导入配置
        uploaded_file = st.file_uploader("上传YAML配置文件", type=['yaml', 'yml'])
        if uploaded_file is not None:
            try:
                # 保存上传的文件
                temp_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'temp_config.yaml')
                with open(temp_path, 'wb') as f:
                    f.write(uploaded_file.getbuffer())
                
                # 导入配置
                if st.button("导入YAML配置"):
                    if db_config_manager.import_from_yaml(temp_path):
                        st.success("配置导入成功")
                        st.rerun()  # 刷新页面显示新配置
                    else:
                        st.error("导入配置失败")
            except Exception as e:
                st.error(f"导入配置出错: {str(e)}")
    
    with col2:
        st.subheader("当前配置")
        
        # 获取所有配置分组
        sections = db_config_manager.get_sections()
        
        if sections:
            # 添加"全部"选项
            section_options = ["全部"] + [section['section'] for section in sections]
            selected_section = st.selectbox("选择配置分组", section_options)
            
            # 显示选定分组的配置
            if selected_section == "全部":
                # 显示所有配置
                all_configs = db_config_manager.get_all_config()
                for section, section_data in all_configs.items():
                    st.write(f"**{section}**")
                    config_df = pd.DataFrame(
                        [(key, str(value)) for key, value in section_data.items()],
                        columns=["配置项", "值"]
                    )
                    st.dataframe(config_df, use_container_width=True)
            else:
                # 显示选定分组的配置
                section_data = db_config_manager.get_section(selected_section)
                if section_data:
                    config_df = pd.DataFrame(
                        [(key, str(value)) for key, value in section_data.items()],
                        columns=["配置项", "值"]
                    )
                    st.dataframe(config_df, use_container_width=True)
                else:
                    st.info(f"分组 '{selected_section}' 没有配置项")
        else:
            st.info("没有找到配置项")
        
        # 添加清空配置的功能
        if st.button("清空数据库配置", help="清空所有配置，谨慎操作！", type="secondary"):
            if st.checkbox("我已了解风险，确认清空"):
                try:
                    # 获取数据库连接
                    import sqlite3
                    conn = sqlite3.connect(db_config_manager.db_path)
                    cursor = conn.cursor()
                    cursor.execute("DELETE FROM config")
                    cursor.execute("DELETE FROM config_sections")
                    conn.commit()
                    conn.close()
                    st.success("已清空所有配置")
                    st.warning("请重新导入配置或刷新页面设置新的配置")
                except Exception as e:
                    st.error(f"清空配置失败: {str(e)}")

# 页脚
st.markdown("---")
st.caption(f"配置数据库路径: {db_config_manager.db_path}")
st.caption(f"最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}") 