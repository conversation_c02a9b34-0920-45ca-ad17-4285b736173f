"""
量化交易系统主页
"""
import streamlit as st
import pandas as pd
import time
import os
import sys
from datetime import datetime
import plotly.express as px

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入项目模块
from quant_app.core.db_config_manager import db_config_manager
from quant_app.core.task_manager import task_manager

# 页面配置
st.set_page_config(
    page_title="量化交易系统",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 标题和简介
st.title("📈 量化交易系统")
st.markdown("""
欢迎使用量化交易系统！本系统集成了回测、交易和分析功能，支持多种交易策略和数据来源。
""")

# 系统状态卡片
st.header("🔔 系统状态")
col1, col2, col3, col4 = st.columns(4)

# 获取系统状态
try:
    # 获取交易状态
    qmt_connected = False
    try:
        from xtquant import xtdata
        test_result = xtdata.get_stock_list_in_sector('沪深A股')
        qmt_connected = test_result is not None and len(test_result) > 0
    except:
        qmt_connected = False
    
    # 获取任务统计
    stats = task_manager.get_task_statistics()
    active_tasks = stats.get('by_status', {}).get('running', 0)
    
    # 显示状态卡片
    with col1:
        st.metric("QMT连接状态", "已连接" if qmt_connected else "未连接", 
                 delta="正常" if qmt_connected else "异常",
                 delta_color="normal" if qmt_connected else "inverse")
    
    with col2:
        st.metric("活跃任务", active_tasks)
    
    with col3:
        # 获取今日完成的任务数
        today = datetime.now().date()
        today_tasks = len([t for t in task_manager.list_tasks() 
                          if t.get('finished_at') and 
                          datetime.fromisoformat(t['finished_at']).date() == today])
        st.metric("今日完成任务", today_tasks)
    
    with col4:
        # 获取数据库大小
        try:
            db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                                  'quant_app', 'data', 'system.db')
            db_size = os.path.getsize(db_path) / (1024 * 1024)  # MB
            st.metric("数据库大小", f"{db_size:.2f} MB")
        except:
            st.metric("数据库大小", "未知")

except Exception as e:
    st.error(f"获取系统状态时出错: {str(e)}")
    st.info("系统初始化中，请稍后刷新页面...")

# 功能导航
st.header("🧭 功能导航")

col1, col2, col3 = st.columns(3)

with col1:
    st.subheader("📊 数据分析")
    st.markdown("""
    - **市场数据**: 查看和分析市场数据
    - **选股**: 使用各种指标筛选股票
    - **数据导入/导出**: 导入导出交易数据
    """)
    if st.button("前往数据分析", key="btn_data"):
        st.switch_page("pages/1_📊_总览.py")

with col2:
    st.subheader("🔬 回测分析")
    st.markdown("""
    - **策略回测**: 测试交易策略的历史表现
    - **参数优化**: 优化策略参数
    - **绩效分析**: 分析交易策略的表现
    """)
    if st.button("前往回测分析", key="btn_backtest"):
        st.switch_page("pages/2_🎯_策略回测.py")

with col3:
    st.subheader("🔴 实盘交易")
    st.markdown("""
    - **实盘监控**: 监控实盘交易状态
    - **实盘交易**: 执行实盘交易
    - **风险管理**: 管理交易风险
    """)
    if st.button("前往实盘交易", key="btn_trading"):
        st.switch_page("pages/5_🔴_实盘交易.py")

# 最近任务
st.header("📋 最近任务")

try:
    # 获取最近10个任务
    recent_tasks = task_manager.list_tasks(limit=10)
    
    if recent_tasks:
        # 转换为DataFrame
        df = pd.DataFrame(recent_tasks)
        
        # 选择显示的列
        display_columns = ['task_id', 'task_type', 'strategy_name', 'status', 'created_at', 'finished_at']
        
        # 创建显示DataFrame
        display_df = df[display_columns].copy()
        
        # 重命名列
        display_df.columns = ['任务ID', '类型', '策略', '状态', '创建时间', '完成时间']
        
        # 格式化
        type_map = {
            'backtest': '回测',
            'trading': '交易',
            'data_update': '数据更新',
            'analysis': '分析'
        }
        display_df['类型'] = display_df['类型'].map(lambda x: type_map.get(x, x))
        
        status_map = {
            'pending': '🟡 等待中',
            'running': '🔵 运行中',
            'completed': '🟢 已完成',
            'failed': '🔴 失败',
            'cancelled': '⚫ 已取消'
        }
        display_df['状态'] = display_df['状态'].map(lambda x: status_map.get(x, x))
        
        # 时间格式化
        for col in ['创建时间', '完成时间']:
            display_df[col] = pd.to_datetime(display_df[col]).dt.strftime('%Y-%m-%d %H:%M:%S')
            display_df[col] = display_df[col].fillna('-')
        
        # 显示表格
        st.dataframe(
            display_df,
            use_container_width=True,
            hide_index=True
        )
        
        # 查看更多按钮
        st.markdown("[查看所有任务 →](3_📋_任务管理)")
    else:
        st.info("暂无任务记录")
except Exception as e:
    st.error(f"获取任务列表时出错: {str(e)}")

# 系统信息
st.sidebar.header("系统信息")
st.sidebar.info(f"""
- **系统版本**: 1.0.0
- **上次更新**: {datetime.now().strftime('%Y-%m-%d')}
- **运行时间**: {datetime.now().strftime('%H:%M:%S')}
""")

# 快速操作
st.sidebar.header("快速操作")
if st.sidebar.button("系统设置"):
    st.switch_page("pages/6_⚙️_系统设置.py")

if st.sidebar.button("任务管理"):
    st.switch_page("pages/3_📋_任务管理.py")

# 自动刷新
if st.sidebar.checkbox("自动刷新", value=False):
    refresh_interval = st.sidebar.slider("刷新间隔(秒)", 5, 60, 30)
    time.sleep(refresh_interval)
    st.rerun() 