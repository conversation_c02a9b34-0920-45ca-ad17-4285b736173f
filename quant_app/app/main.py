"""
量化交易系统 - Web界面
"""
import streamlit as st
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 页面配置
st.set_page_config(
    page_title="量化交易系统",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': None,
        'Report a bug': None,
        'About': "# 量化交易系统\n基于QMT的量化交易平台"
    }
)

# 自定义CSS样式
st.markdown("""
<style>
    /* 主题色调整 */
    .stApp {
        background-color: #f8f9fa;
    }
    
    /* 侧边栏样式 */
    .css-1d391kg {
        background-color: #ffffff;
    }
    
    /* 指标卡片样式 */
    div[data-testid="metric-container"] {
        background-color: #ffffff;
        border: 1px solid #e0e0e0;
        padding: 15px;
        border-radius: 5px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    /* 表格样式 */
    .dataframe {
        font-size: 14px;
    }
    
    /* 按钮样式 */
    .stButton > button {
        background-color: #007bff;
        color: white;
        border-radius: 5px;
        border: none;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s;
    }
    
    .stButton > button:hover {
        background-color: #0056b3;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    /* 成功按钮 */
    .stButton > button[kind="primary"] {
        background-color: #28a745;
    }
    
    /* 警告按钮 */
    .stButton > button[kind="secondary"] {
        background-color: #ffc107;
        color: #212529;
    }
    
    /* 危险按钮 */
    div[data-testid="stHorizontalBlock"] button:nth-child(3) {
        background-color: #dc3545;
    }
    
    /* 选择框样式 */
    .stSelectbox > div > div {
        background-color: #ffffff;
        border-radius: 5px;
    }
    
    /* 标题样式 */
    h1 {
        color: #212529;
        font-weight: 600;
    }
    
    h2 {
        color: #495057;
        font-weight: 500;
        border-bottom: 2px solid #007bff;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    
    h3 {
        color: #6c757d;
        font-weight: 500;
    }
    
    /* 信息框样式 */
    .stAlert {
        border-radius: 5px;
        border-left: 5px solid;
    }
    
    /* 进度条样式 */
    .stProgress > div > div > div > div {
        background-color: #007bff;
    }
    
    /* 选项卡样式 */
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
    }
    
    .stTabs [data-baseweb="tab"] {
        height: 50px;
        padding-left: 20px;
        padding-right: 20px;
        background-color: #ffffff;
        border-radius: 5px 5px 0 0;
    }
    
    .stTabs [aria-selected="true"] {
        background-color: #007bff;
        color: white;
    }
</style>
""", unsafe_allow_html=True)

# 主页面内容
st.title("🚀 量化交易系统")
st.markdown("---")

# 系统介绍
col1, col2, col3 = st.columns(3)

with col1:
    st.info("""
    ### 📊 数据驱动
    - 实时市场数据接入
    - 历史数据回测分析
    - 多维度数据可视化
    """)

with col2:
    st.success("""
    ### 🤖 智能策略
    - 多种内置交易策略
    - 策略参数优化
    - 自定义策略开发
    """)

with col3:
    st.warning("""
    ### 💼 风险管理
    - 实时风控监控
    - 止损止盈设置
    - 仓位管理系统
    """)

st.markdown("---")

# 快速开始
st.header("🎯 快速开始")

col1, col2 = st.columns([2, 1])

with col1:
    st.markdown("""
    ### 使用步骤
    
    1. **系统设置** - 在左侧菜单中选择"系统设置"，配置交易接口和风控参数
    2. **策略回测** - 选择"策略回测"页面，测试和优化您的交易策略
    3. **任务管理** - 在"任务管理"页面监控所有运行中的任务
    4. **分析报告** - 查看详细的交易分析和性能报告
    5. **实盘交易** - 确认策略表现后，可以启动实盘交易
    
    ### 主要功能
    
    - **总览页面** - 查看账户状态、市场概况和最近任务
    - **策略回测** - 测试策略在历史数据上的表现
    - **任务管理** - 统一管理所有回测和交易任务
    - **分析报告** - 深入分析交易结果和策略表现
    - **系统设置** - 配置系统参数和交易接口
    """)

with col2:
    st.markdown("### 📚 相关文档")
    st.markdown("""
    - [系统使用说明](交易系统使用说明.md)
    - [策略配置指南](策略配置系统使用说明.md)
    - [实盘交易指南](实盘量化系统开发.md)
    - [API文档](TRADING_GUIDE.md)
    """)
    
    st.markdown("### 🔧 常用工具")
    if st.button("🔄 刷新数据", use_container_width=True):
        st.rerun()
    
    if st.button("📥 导出配置", use_container_width=True):
        st.info("配置导出功能开发中...")
    
    if st.button("📤 导入配置", use_container_width=True):
        st.info("配置导入功能开发中...")

st.markdown("---")

# 系统状态
st.header("💻 系统状态")

col1, col2, col3, col4 = st.columns(4)

with col1:
    st.metric("系统版本", "v2.0.0", "稳定版")

with col2:
    st.metric("运行状态", "正常", "✓")

with col3:
    import psutil
    cpu_percent = psutil.cpu_percent(interval=1)
    st.metric("CPU使用率", f"{cpu_percent}%", 
              f"{'+' if cpu_percent > 80 else ''}{cpu_percent - 50:.1f}%")

with col4:
    memory = psutil.virtual_memory()
    st.metric("内存使用率", f"{memory.percent}%", 
              f"{'+' if memory.percent > 80 else ''}{memory.percent - 50:.1f}%")

# 页脚
st.markdown("---")
st.markdown("""
<div style='text-align: center; color: #6c757d;'>
    <p>量化交易系统 v2.0 | 基于QMT框架开发 | © 2024</p>
</div>
""", unsafe_allow_html=True) 