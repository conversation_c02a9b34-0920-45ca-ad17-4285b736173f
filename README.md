# 股票批量回测系统

这是一个支持批量回测多日期、多股票的回测系统，主要聚焦于可转债交易策略测试。

## 主要功能

1. **批量日期回测**：
   - 支持按日期范围进行批量回测
   - 自动检查交易日，非交易日自动跳过
   - 支持从指定日期向前回溯特定天数

2. **单日回测**：
   - 指定特定日期进行回测
   - 自动修改配置文件中的回测时间范围

3. **自动股票选择**：
   - 基于特定日期的市场数据选择股票
   - 支持历史数据回溯分析
   - 选股结果缓存功能，提高回测效率

4. **结果汇总与分析**：
   - 每日回测结果保存至独立文件夹
   - 汇总所有日期的回测结果
   - 提供按股票和按日期的统计分析

## 系统组件

- `run_batch_backtest.py`：批量回测主程序
- `select_stocks.py`：股票选择程序
- `run_backtest.py`：单次回测执行程序
- `my_connors_rsi_strategy.py`：交易策略实现

## 使用方法

### 批量回测

批量回测多个交易日：

```bash
python run_batch_backtest.py --start_date 20250501 --end_date 20250530
```

指定回溯天数的批量回测：

```bash
python run_batch_backtest.py --end_date 20250530 --days 20
```

### 单日回测

回测指定日期：

```bash
python run_batch_backtest.py --date 20250520
```

### 配置文件

系统使用 `config.yaml` 作为配置文件，可以调整以下参数：

- 回测参数：初始资金、手续费率、滑点等
- 股票代码列表：可由系统自动选取或手动指定
- 策略参数：止盈止损比例、仓位控制等
- 日志设置：日志级别、保存位置等
- 选股缓存参数：是否使用缓存、缓存目录等

### 选股缓存功能

系统提供选股结果缓存功能，可以大幅提高多次回测的效率：

```yaml
backtest:
  # 其他配置...
  cache_dir: select_stocks  # 选股结果缓存目录
  use_stock_cache: true     # 是否使用缓存的选股结果
```

- 当 `use_stock_cache` 设置为 `true` 时，系统会优先尝试从缓存中读取已有的选股结果
- 当设置为 `false` 或缓存不存在时，会重新进行选股并生成新的缓存文件
- 缓存文件位于 `cache_dir` 指定的目录中，以 `stocks_YYYYMMDD.yaml` 命名

## 结果目录结构

```
results/
  └── backtest_YYYYMMDD_HHMMSS/    # 批量回测结果目录
      ├── YYYYMMDD/                # 每日回测结果子目录
      │   ├── backtest_results_YYYYMMDD.csv   # 该日回测结果
      │   └── *.log                # 该日回测日志
      ├── combined_results.csv     # 所有日期回测结果汇总
      ├── stock_statistics.csv     # 按股票统计的结果
      ├── daily_statistics.csv     # 按日期统计的结果
      └── config.yaml             # 运行时使用的配置文件备份
      
select_stocks/                    # 选股结果缓存目录
  └── stocks_YYYYMMDD.yaml        # 各日期的选股结果缓存
```

## 参数说明

`run_batch_backtest.py` 命令行参数：

- `--start_date`: 开始日期，格式为YYYYMMDD
- `--end_date`: 结束日期，格式为YYYYMMDD
- `--days`: 从结束日期回溯的天数（默认30天）
- `--date`: 单一日期回测，格式为YYYYMMDD

## 依赖库

- Python 3.6+
- pandas
- numpy
- backtrader
- xtquant (股票数据接口)
- yaml
- matplotlib (用于绘图)

## 注意事项

- 请确保在运行前xtquant环境已正确配置
- 非交易日将被自动跳过
- 回测结果将保存在results目录下
- 回测后原始配置文件会自动恢复 