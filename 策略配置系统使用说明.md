# 策略配置系统使用说明

## 概述

新的策略配置系统支持通过配置文件动态加载不同的策略文件，避免了硬编码策略导入的问题。您可以轻松地切换策略、配置参数，以及管理多个策略。

## 主要功能

### 1. 动态策略加载
- 支持通过配置文件动态加载策略
- 无需修改代码即可切换策略
- 支持自定义策略参数

### 2. 策略管理工具
- 列出可用策略
- 查看策略详细信息
- 切换当前使用的策略
- 验证策略配置
- 测试策略加载

### 3. 命令行支持
- 回测时可以临时指定策略
- 列出所有可用策略
- 灵活的参数配置

## 配置文件结构

在 `config.yaml` 中的策略配置部分：

```yaml
strategy_config:
  # 当前使用的策略
  active_strategy: 'optimized_kdj'
  
  # 可用策略列表
  strategies:
    # 优化的KDJ+成交量共振策略（默认策略）
    optimized_kdj:
      module: 'kdj_volume_resonance_strategy_optimized'
      class: 'OptimizedKDJVolumeStrategy'
      description: '优化的KDJ+成交量共振策略'
      parameters: {}  # 使用策略默认参数
    
    # 三重共振策略
    triple_resonance:
      module: 'my_connors_rsi_strategy'
      class: 'TripleResonanceStrategy'
      description: '三重共振策略（ConnorsRSI + MACD + Volume）'
      parameters: {}  # 使用策略默认参数
    
    # 自定义参数的策略配置示例
    optimized_kdj_custom:
      module: 'kdj_volume_resonance_strategy_optimized'
      class: 'OptimizedKDJVolumeStrategy'
      description: '优化的KDJ+成交量共振策略（自定义参数）'
      parameters:
        kdj_period: 14
        volume_ma_period: 20
        j_oversold: 15
        j_overbought: 85
        profit_target_1: 0.03
        stop_loss: 0.015
```

## 使用方法

### 1. 策略管理工具 (strategy_manager.py)

#### 列出所有可用策略
```bash
python strategy_manager.py --list
```

#### 查看策略详细信息
```bash
python strategy_manager.py --info strategy_name
```

#### 设置当前活跃策略
```bash
python strategy_manager.py --set-active strategy_name
```

#### 验证所有策略配置
```bash
python strategy_manager.py --validate
```

#### 测试策略加载
```bash
python strategy_manager.py --test strategy_name
```

### 2. 回测程序 (run_backtest.py)

#### 列出可用策略
```bash
python run_backtest.py --list-strategies
```

#### 使用指定策略运行回测
```bash
python run_backtest.py --strategy strategy_name --stock-code 123093.SZ
```

#### 临时切换策略运行回测
```bash
python run_backtest.py --strategy optimized_kdj_custom --stock-code 123093.SZ --backtest-date 20250603
```

## 可用策略

目前系统中配置了以下策略：

### 1. optimized_kdj (默认)
- **描述**: 优化的KDJ+成交量共振策略
- **文件**: `kdj_volume_resonance_strategy_optimized.py`
- **类名**: `OptimizedKDJVolumeStrategy`
- **特点**: 经过优化的KDJ指标与成交量分析相结合

### 2. triple_resonance
- **描述**: 三重共振策略（ConnorsRSI + MACD + Volume）
- **文件**: `my_connors_rsi_strategy.py`
- **类名**: `TripleResonanceStrategy`
- **特点**: 结合ConnorsRSI、MACD和成交量的多重确认策略

### 3. kdj_volume
- **描述**: KDJ+成交量共振策略（原版）
- **文件**: `kdj_volume_resonance_strategy.py`
- **类名**: `KDJVolumeResonanceStrategy`
- **特点**: 原始版本的KDJ成交量共振策略

### 4. optimized_kdj_custom
- **描述**: 优化的KDJ+成交量共振策略（自定义参数）
- **文件**: `kdj_volume_resonance_strategy_optimized.py`
- **类名**: `OptimizedKDJVolumeStrategy`
- **特点**: 使用自定义参数的优化策略
- **自定义参数**:
  - `kdj_period: 14`
  - `volume_ma_period: 20`
  - `j_oversold: 15`
  - `j_overbought: 85`
  - `profit_target_1: 0.03`
  - `stop_loss: 0.015`

## 添加新策略

### 1. 创建策略文件
创建新的策略文件，确保策略类继承自 `bt.Strategy`：

```python
import backtrader as bt

class MyCustomStrategy(bt.Strategy):
    params = (
        ('param1', 10),
        ('param2', 0.02),
    )
    
    def __init__(self):
        # 策略初始化
        pass
    
    def next(self):
        # 策略逻辑
        pass
```

### 2. 在配置文件中添加策略
在 `config.yaml` 的 `strategy_config.strategies` 下添加新策略：

```yaml
my_custom_strategy:
  module: 'my_custom_strategy'  # 文件名（不含.py）
  class: 'MyCustomStrategy'     # 类名
  description: '我的自定义策略'
  parameters:
    param1: 20
    param2: 0.03
```

### 3. 验证策略配置
```bash
python strategy_manager.py --validate
```

### 4. 测试策略加载
```bash
python strategy_manager.py --test my_custom_strategy
```

## 最佳实践

### 1. 策略开发流程
1. 创建策略文件
2. 在配置文件中注册策略
3. 使用 `--validate` 验证配置
4. 使用 `--test` 测试加载
5. 进行回测验证

### 2. 参数配置
- 优先使用策略默认参数
- 只在配置文件中覆盖需要调整的参数
- 使用描述性的策略名称

### 3. 策略切换
- 使用策略管理工具切换默认策略
- 使用命令行参数临时切换策略
- 验证策略配置后再使用

## 注意事项

### 1. 策略文件要求
- 策略类必须继承自 `bt.Strategy`
- 策略文件必须在当前工作目录中
- 策略类名必须与配置文件中的 `class` 字段匹配

### 2. 参数配置要求
- 参数名必须与策略类中定义的参数名匹配
- 参数类型必须与策略期望的类型兼容
- 无效参数会产生警告但不会阻止策略运行

### 3. 兼容性
- 系统会自动回退到默认策略（如果配置不存在）
- 支持向后兼容，旧版本的代码依然可以正常运行

## 故障排除

### 1. 策略加载失败
- 检查策略文件是否存在
- 确认策略类名是否正确
- 验证策略类是否继承自 `bt.Strategy`

### 2. 参数配置问题
- 使用 `--validate` 检查配置
- 查看警告信息
- 对比策略类的参数定义

### 3. 配置文件问题
- 检查YAML语法是否正确
- 确认缩进是否正确
- 验证字段名是否拼写正确

## 示例用法

### 快速开始
```bash
# 1. 查看可用策略
python strategy_manager.py --list

# 2. 切换到三重共振策略
python strategy_manager.py --set-active triple_resonance

# 3. 运行回测
python run_backtest.py --stock-code 123093.SZ

# 4. 临时使用自定义参数策略
python run_backtest.py --strategy optimized_kdj_custom --stock-code 123093.SZ
```

### 策略验证
```bash
# 验证所有策略配置
python strategy_manager.py --validate

# 测试特定策略加载
python strategy_manager.py --test optimized_kdj
```

这个策略配置系统大大提高了量化交易系统的灵活性和可维护性，让您可以轻松地管理和切换不同的交易策略。 