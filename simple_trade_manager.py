#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的交易管理器
直接使用subprocess启动独立的交易进程，更可靠和简单
"""

import subprocess
import logging
import os
import sys
import time
import yaml
import psutil
from datetime import datetime
from utils import WechatNotifier, ConfigUtils
from stock_data_manager import format_stock_code_with_name

class SimpleTradeManager:
    def __init__(self):
        """初始化简化交易管理器"""
        # 加载配置
        with open('config.yaml', 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 创建日志目录
        if not os.path.exists(self.config['logging']['log_dir']):
            os.makedirs(self.config['logging']['log_dir'])
        
        # 设置日志
        self.logger = self._setup_logger("simple_trade_manager")
        
        # 进程状态文件
        self.state_file = 'simple_trade_processes.txt'
        
        self.logger.info("简化交易管理器初始化完成")

    def _setup_logger(self, name):
        """设置日志"""
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, self.config['logging']['level']))
        
        # 清除已有的handlers，避免重复
        if logger.handlers:
            logger.handlers.clear()
        
        # 添加文件处理器
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f'{self.config["logging"]["log_dir"]}/simple_trade_manager_{timestamp}.log'
        fh = logging.FileHandler(log_filename, encoding='utf-8')
        fh.setLevel(getattr(logging, self.config['logging']['file_level']))
        
        # 添加控制台处理器
        ch = logging.StreamHandler()
        ch.setLevel(getattr(logging, self.config['logging']['console_level']))
        
        # 创建formatter
        file_formatter = logging.Formatter(
            self.config['logging']['file_format'],
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_formatter = logging.Formatter(
            self.config['logging']['console_format'],
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        fh.setFormatter(file_formatter)
        ch.setFormatter(console_formatter)
        
        # 添加handlers
        logger.addHandler(fh)
        logger.addHandler(ch)
        
        # 防止日志向上传播，避免重复输出
        logger.propagate = False
        
        return logger

    def add_stock(self, stock_code, initial_cash=None):
        """
        添加新的股票交易进程
        
        参数:
        stock_code: str, 股票代码
        initial_cash: float, 初始资金，如果为None则使用配置文件中的值
        """
        try:
            formatted_code = format_stock_code_with_name(stock_code)
            
            # 更严格的重复启动检查
            if self._is_stock_trading(stock_code):
                self.logger.warning(f"股票 {formatted_code} 已经在交易中")
                return False
            
            # 检查状态文件中是否已有记录
            if self._check_stock_in_state_file(stock_code):
                self.logger.warning(f"股票 {formatted_code} 在状态文件中已存在，可能正在启动中")
                return False
            
            # 使用配置文件中的初始资金（如果未指定）
            if initial_cash is None:
                initial_cash = self.config['backtest']['initial_cash']
            
            # 构建启动命令
            python_path = sys.executable
            cmd = [
                python_path,
                'run_backtest.py',
                '--stock-code', stock_code,
                '--initial-cash', str(initial_cash),
                '--live'  # 实盘模式
            ]
            
            self.logger.info(f"启动股票 {formatted_code} 的交易进程: {' '.join(cmd)}")
            
            # 启动进程（后台运行）
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=os.getcwd()
            )
            
            # 等待更长时间确保进程启动
            time.sleep(3)
            
            # 检查进程是否成功启动
            if process.poll() is None:
                # 进程仍在运行，记录到状态文件
                self._save_process_info(stock_code, process.pid, initial_cash)
                
                self.logger.info(f"成功启动股票 {formatted_code} 的交易进程 (PID: {process.pid})")
                
                # 再次等待确保进程稳定运行
                time.sleep(2)
                
                # 最终检查进程是否仍在运行
                if process.poll() is None:
                    # 发送企业微信通知
                    notification_content = f"### ✅ 新增股票交易通知\n\n"
                    notification_content += f"**股票代码**: {formatted_code}\n\n"
                    notification_content += f"**添加时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                    notification_content += f"**初始资金**: {initial_cash:.2f}\n\n"
                    notification_content += f"**进程ID**: {process.pid}"
                    
                    WechatNotifier.send_notification(notification_content)
                    return True
                else:
                    # 进程启动后立即退出
                    stdout, stderr = process.communicate()
                    error_msg = f"股票 {formatted_code} 交易进程启动后立即退出"
                    if stderr:
                        error_msg += f": {stderr}"
                    self.logger.error(error_msg)
                    # 清理状态文件记录
                    self._remove_process_info(stock_code)
                    return False
            else:
                # 进程启动失败
                stdout, stderr = process.communicate()
                error_msg = f"股票 {formatted_code} 交易进程启动失败"
                if stderr:
                    error_msg += f": {stderr}"
                self.logger.error(error_msg)
                return False
                
        except Exception as e:
            formatted_code = format_stock_code_with_name(stock_code)
            error_msg = f"添加股票 {formatted_code} 失败: {str(e)}"
            self.logger.error(error_msg)
            
            # 发送企业微信通知
            notification_content = f"### ❌ 添加股票失败通知\n\n"
            notification_content += f"**股票代码**: {formatted_code}\n\n"
            notification_content += f"**错误时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            notification_content += f"**错误信息**: {str(e)}"
            
            WechatNotifier.send_notification(notification_content)
            return False

    def remove_stock(self, stock_code):
        """
        停止并移除股票交易进程
        
        参数:
        stock_code: str, 股票代码
        """
        try:
            formatted_code = format_stock_code_with_name(stock_code)
            # 查找进程
            processes = self._find_stock_processes(stock_code)
            if not processes:
                self.logger.warning(f"未找到股票 {formatted_code} 的交易进程")
                return False
            
            # 停止所有相关进程
            stopped_count = 0
            for proc_info in processes:
                try:
                    proc = psutil.Process(proc_info['pid'])
                    proc.terminate()
                    
                    # 等待进程结束
                    try:
                        proc.wait(timeout=10)
                    except psutil.TimeoutExpired:
                        # 强制杀死
                        proc.kill()
                        proc.wait(timeout=5)
                    
                    stopped_count += 1
                    self.logger.info(f"成功停止进程 PID={proc_info['pid']}")
                    
                except psutil.NoSuchProcess:
                    self.logger.info(f"进程 PID={proc_info['pid']} 已不存在")
                    stopped_count += 1
                except Exception as e:
                    self.logger.error(f"停止进程 PID={proc_info['pid']} 失败: {str(e)}")
            
            # 从状态文件中移除
            self._remove_process_info(stock_code)
            
            if stopped_count > 0:
                self.logger.info(f"成功停止股票 {formatted_code} 的 {stopped_count} 个交易进程")
                
                # 发送企业微信通知
                notification_content = f"### 🛑 停止股票交易通知\n\n"
                notification_content += f"**股票代码**: {formatted_code}\n\n"
                notification_content += f"**停止时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                notification_content += f"**停止进程数**: {stopped_count}"
                
                WechatNotifier.send_notification(notification_content)
                return True
            else:
                return False
                
        except Exception as e:
            formatted_code = format_stock_code_with_name(stock_code)
            error_msg = f"移除股票 {formatted_code} 失败: {str(e)}"
            self.logger.error(error_msg)
            return False

    def restart_stock(self, stock_code, initial_cash=None):
        """
        重启指定股票的交易进程
        
        参数:
        stock_code: str, 股票代码
        initial_cash: float, 初始资金，如果为None则使用配置文件中的值
        
        返回:
        bool: 重启是否成功
        """
        try:
            formatted_code = format_stock_code_with_name(stock_code)
            self.logger.info(f"开始重启股票 {formatted_code} 的交易进程")
            
            # 检查股票是否在交易中
            is_trading = self._is_stock_trading(stock_code)
            
            if is_trading:
                # 先停止现有进程
                self.logger.info(f"正在停止股票 {formatted_code} 的现有交易进程...")
                remove_success = self.remove_stock(stock_code)
                if not remove_success:
                    self.logger.warning(f"停止股票 {formatted_code} 的交易进程时出现问题，但继续尝试启动新进程")
                else:
                    self.logger.info(f"成功停止股票 {formatted_code} 的交易进程")
                
                # 等待一段时间确保进程完全停止
                time.sleep(2)
            else:
                self.logger.info(f"股票 {formatted_code} 当前未在交易中，直接启动新进程")
            
            # 启动新进程
            self.logger.info(f"正在启动股票 {formatted_code} 的新交易进程...")
            add_success = self.add_stock(stock_code, initial_cash)
            
            if add_success:
                self.logger.info(f"成功重启股票 {formatted_code} 的交易进程")
                
                # 发送企业微信通知
                notification_content = f"### 🔄 股票交易进程重启通知\n\n"
                notification_content += f"**股票代码**: {formatted_code}\n\n"
                notification_content += f"**重启时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                if initial_cash:
                    notification_content += f"**初始资金**: {initial_cash:.2f}\n\n"
                notification_content += f"**操作状态**: 重启成功"
                
                WechatNotifier.send_notification(notification_content)
                return True
            else:
                self.logger.error(f"重启股票 {formatted_code} 失败：无法启动新的交易进程")
                
                # 发送失败通知
                notification_content = f"### ❌ 股票交易进程重启失败\n\n"
                notification_content += f"**股票代码**: {formatted_code}\n\n"
                notification_content += f"**重启时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                notification_content += f"**失败原因**: 无法启动新的交易进程"
                
                WechatNotifier.send_notification(notification_content)
                return False
                
        except Exception as e:
            formatted_code = format_stock_code_with_name(stock_code)
            error_msg = f"重启股票 {formatted_code} 的交易进程失败: {str(e)}"
            self.logger.error(error_msg)
            
            # 发送异常通知
            notification_content = f"### ❌ 股票交易进程重启异常\n\n"
            notification_content += f"**股票代码**: {formatted_code}\n\n"
            notification_content += f"**重启时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            notification_content += f"**异常信息**: {str(e)}"
            
            WechatNotifier.send_notification(notification_content)
            return False

    def get_active_stocks(self):
        """获取当前正在交易的股票列表"""
        try:
            active_stocks = []
            
            # 查找所有运行中的交易进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'python' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline']
                        if cmdline and len(cmdline) > 1:
                            # 查找运行 run_backtest.py 的进程
                            if any('run_backtest.py' in arg for arg in cmdline):
                                # 提取股票代码
                                stock_code = None
                                for i, arg in enumerate(cmdline):
                                    if arg == '--stock-code' and i + 1 < len(cmdline):
                                        stock_code = cmdline[i + 1]
                                        break
                                
                                if stock_code and stock_code not in active_stocks:
                                    active_stocks.append(stock_code)
                                    
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            return active_stocks
            
        except Exception as e:
            self.logger.error(f"获取活跃股票列表失败: {str(e)}")
            return []

    def get_stock_status(self, stock_code):
        """
        获取指定股票的交易状态
        
        返回:
        dict: 包含股票交易状态的字典
        """
        try:
            processes = self._find_stock_processes(stock_code)
            
            if not processes:
                return {"status": "not_trading"}
            
            # 检查进程状态
            alive_processes = []
            for proc_info in processes:
                try:
                    proc = psutil.Process(proc_info['pid'])
                    if proc.is_running():
                        alive_processes.append(proc_info)
                except psutil.NoSuchProcess:
                    continue
            
            if not alive_processes:
                return {"status": "not_trading"}
            
            status = {
                "status": "trading",
                "process_count": len(alive_processes),
                "processes": alive_processes
            }
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取股票 {stock_code} 状态失败: {str(e)}")
            return {"status": "error", "error": str(e)}

    def init_from_config(self):
        """从配置文件初始化股票交易进程"""
        try:
            # 检查是否为实盘交易模式
            if not self.config['backtest']['is_live']:
                self.logger.warning("配置文件中设置为非实盘模式，跳过初始化")
                return False
            
            stock_codes = self.config['backtest']['stock_codes']
            initial_cash = self.config['backtest']['initial_cash']
            
            if not stock_codes:
                self.logger.warning("配置文件中没有股票代码，跳过初始化")
                return False
            
            formatted_codes = [format_stock_code_with_name(code) for code in stock_codes]
            self.logger.info(f"开始从配置文件初始化股票交易进程: {', '.join(formatted_codes)}")
            
            # 检查是否已有相同股票在运行，如果有则先清理
            existing_stocks = []
            for stock_code in stock_codes:
                if self._is_stock_trading(stock_code):
                    existing_stocks.append(stock_code)
            
            if existing_stocks:
                existing_formatted = [format_stock_code_with_name(code) for code in existing_stocks]
                self.logger.warning(f"发现已运行的股票进程: {', '.join(existing_formatted)}")
                
                # 询问是否要重启这些进程（在实际使用中，我们选择跳过已运行的）
                self.logger.info("跳过已运行的股票，只启动新的股票")
                
                # 过滤掉已运行的股票
                stock_codes = [code for code in stock_codes if code not in existing_stocks]
                
                if not stock_codes:
                    self.logger.info("所有股票都已在运行中，无需启动新进程")
                    return True
            
            success_count = 0
            failed_stocks = []
            
            for stock_code in stock_codes:
                formatted_code = format_stock_code_with_name(stock_code)
                self.logger.info(f"正在初始化股票 {formatted_code}")
                success = self.add_stock(stock_code, initial_cash)
                if success:
                    success_count += 1
                    self.logger.info(f"股票 {formatted_code} 初始化成功")
                else:
                    failed_stocks.append(stock_code)
                    self.logger.error(f"股票 {formatted_code} 初始化失败")
                
                # 每个股票之间间隔一点时间
                time.sleep(1)
            
            # 发送初始化完成通知
            notification_content = f"### 📊 简化交易系统初始化完成\n\n"
            notification_content += f"**初始化时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            notification_content += f"**成功启动**: {success_count} 只股票\n\n"
            if failed_stocks:
                failed_formatted = [format_stock_code_with_name(code) for code in failed_stocks]
                notification_content += f"**失败股票**: {', '.join(failed_formatted)}\n\n"
            if existing_stocks:
                existing_formatted = [format_stock_code_with_name(code) for code in existing_stocks]
                notification_content += f"**已运行股票**: {', '.join(existing_formatted)}\n\n"
            active_stocks = self.get_active_stocks()
            active_formatted = [format_stock_code_with_name(code) for code in active_stocks]
            notification_content += f"**活跃股票列表**: {', '.join(active_formatted)}\n\n"
            notification_content += f"**管理提示**: 使用 `python trade_cli.py list` 查看状态"
            
            WechatNotifier.send_notification(notification_content)
            
            total_success = success_count + len(existing_stocks)
            self.logger.info(f"初始化完成: 新启动 {success_count} 只，已运行 {len(existing_stocks)} 只，失败 {len(failed_stocks)} 只")
            return total_success > 0
            
        except Exception as e:
            error_msg = f"从配置文件初始化失败: {str(e)}"
            self.logger.error(error_msg)
            
            # 发送错误通知
            notification_content = f"### ❌ 简化交易系统初始化失败\n\n"
            notification_content += f"**错误时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            notification_content += f"**错误信息**: {str(e)}"
            
            WechatNotifier.send_notification(notification_content)
            return False

    def cleanup(self):
        """清理所有交易进程"""
        try:
            active_stocks = self.get_active_stocks()
            if not active_stocks:
                self.logger.info("没有活跃的交易进程需要清理")
                return
            
            self.logger.info(f"开始清理 {len(active_stocks)} 只股票的交易进程")
            
            for stock_code in active_stocks:
                self.remove_stock(stock_code)
            
            # 删除状态文件
            if os.path.exists(self.state_file):
                os.remove(self.state_file)
                self.logger.info(f"已删除状态文件 {self.state_file}")
            
            self.logger.info("所有交易进程已清理完成")
            
        except Exception as e:
            self.logger.error(f"清理交易进程失败: {str(e)}")

    def _is_stock_trading(self, stock_code):
        """检查股票是否已经在交易"""
        # 方法1：检查活跃进程
        active_stocks = self.get_active_stocks()
        if stock_code in active_stocks:
            return True
        
        # 方法2：检查状态文件
        if self._check_stock_in_state_file(stock_code):
            return True
        
        # 方法3：直接查找进程
        processes = self._find_stock_processes(stock_code)
        if processes:
            # 验证进程是否真的在运行
            for proc_info in processes:
                try:
                    proc = psutil.Process(proc_info['pid'])
                    if proc.is_running():
                        return True
                except psutil.NoSuchProcess:
                    continue
        
        return False

    def _find_stock_processes(self, stock_code):
        """查找指定股票的交易进程"""
        processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'python' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline']
                        if cmdline and len(cmdline) > 1:
                            # 查找运行 run_backtest.py 的进程
                            if any('run_backtest.py' in arg for arg in cmdline):
                                # 检查是否是指定的股票代码
                                for i, arg in enumerate(cmdline):
                                    if arg == '--stock-code' and i + 1 < len(cmdline):
                                        if cmdline[i + 1] == stock_code:
                                            processes.append({
                                                'pid': proc.info['pid'],
                                                'stock_code': stock_code,
                                                'cmdline': cmdline
                                            })
                                        break
                                        
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
        except Exception as e:
            self.logger.error(f"查找股票 {stock_code} 进程失败: {str(e)}")
        
        return processes

    def _save_process_info(self, stock_code, pid, initial_cash):
        """保存进程信息到状态文件"""
        try:
            timestamp = datetime.now().isoformat()
            info = f"{timestamp},{stock_code},{pid},{initial_cash}\n"
            
            with open(self.state_file, 'a', encoding='utf-8') as f:
                f.write(info)
                
        except Exception as e:
            self.logger.error(f"保存进程信息失败: {str(e)}")

    def _remove_process_info(self, stock_code):
        """从状态文件中移除进程信息"""
        try:
            if not os.path.exists(self.state_file):
                return
            
            # 读取所有行
            with open(self.state_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 过滤掉指定股票的行
            filtered_lines = []
            for line in lines:
                if line.strip():
                    parts = line.strip().split(',')
                    if len(parts) >= 2 and parts[1] != stock_code:
                        filtered_lines.append(line)
            
            # 重写文件
            with open(self.state_file, 'w', encoding='utf-8') as f:
                f.writelines(filtered_lines)
                
        except Exception as e:
            self.logger.error(f"移除进程信息失败: {str(e)}")

    def _check_stock_in_state_file(self, stock_code):
        """检查股票是否已经在状态文件中"""
        try:
            if not os.path.exists(self.state_file):
                return False
            
            with open(self.state_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                if line.strip():
                    parts = line.strip().split(',')
                    if len(parts) >= 2 and parts[1] == stock_code:
                        return True
            return False
            
        except Exception as e:
            self.logger.error(f"检查股票 {stock_code} 状态失败: {str(e)}")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='简化交易进程管理器')
    parser.add_argument('--init', action='store_true', help='从配置文件初始化交易进程')
    
    args = parser.parse_args()
    
    # 创建简化交易管理器
    manager = SimpleTradeManager()
    
    try:
        if args.init:
            # 初始化交易进程
            success = manager.init_from_config()
            if success:
                print("✅ 交易进程初始化成功")
                print("💡 提示: 使用 `python trade_cli.py list` 查看活跃股票")
            else:
                print("❌ 交易进程初始化失败")
                return 1
        else:
            print("请指定操作模式:")
            print("  --init     从配置文件初始化交易进程")
            return 1
            
    except KeyboardInterrupt:
        print("\n收到中断信号，正在清理...")
        manager.cleanup()
        return 0
    except Exception as e:
        print(f"❌ 运行时发生错误: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 