# 问题点
- [x] 企业微信通知还没成交就发出来了，需要排查订单状态与对应的微信通知（已实现实盘交易状态下不调用父类的buy方法，完全自己管理订单创建和状态以及通知）
- [x] 确认超时撤单功能
- [x] 订单重复提交问题（由于调试多进程管理工具，导致多个miniqmt对接进程运行，相同的策略同时运行导致重复下单）
- [x] 策略在第一次委托下单后卡住（策略在第一次下单给broker后，broker提交了订单并将订单状态置为已提交，在策略中有对订单检查逻辑，当前订单一直不为None导致next方法一直不运行直接返回）
- [x] 委托回调方法有日志输出，委托回调中的订单状态更新没有日志输出
- [x] 20250529精达转债110074，还有持仓是buy_price为0导致无法进行止盈止损计算
- [ ] 成交的交易重复发送多次消息
- [ ] 卖出委托订单，使用同步撤单接口撤销之前的卖出单时，会显示撤单成功，但是再次提交卖出委托后会显示可用数量不足，只能放在撤单回调方法里面（有些能撤单成功，可能由于网络延时问题，咱不修复）
- [x] 日志分析系统交易记录对不上，时间取得也不对，要取回测中的交易时间，不能时当前的

# 优化点
- [x] 单笔仓位限制和单股仓位限制
- [x] 单股当日连续亏损次数超过阈值自动停止当前股票进程
- [x] 手动增加实盘交易进程
- [x] 实盘交易进程手动终止
- [ ] 实盘交易量根据当前bar成交额做修正
- [ ] 实盘的止损线设置得太敏感，排查20250530交易，可以让卖出指标与买入信号指标使用不同周期的指标
- [x] 日志输出增加股票名称简称
- [ ] 使用开盘前五分钟的成交量或涨幅选股
- [ ] 调整macd周期
- [ ] 调整卖出逻辑，突破上轨且盈利超过目标的一半
- [ ] 买入信号触发与买入执行时间间隔->优化超时撤单机制
- [ ] 买入后卖出时间统计分析