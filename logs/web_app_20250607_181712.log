2025-06-07 18:17:12,181 - web_app - INFO - ==================================================
2025-06-07 18:17:12,181 - web_app - INFO - 启动量化交易系统Web界面
2025-06-07 18:17:12,181 - web_app - INFO - ==================================================
2025-06-07 18:17:12,181 - web_app - INFO - 初始化Web应用系统...
2025-06-07 18:17:12,386 - numexpr.utils - INFO - Note: NumExpr detected 32 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-06-07 18:17:12,386 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-06-07 18:17:12,632 - web_app - INFO - 检测到YAML配置文件: f:\git_repo\miniqmt_bt\config.yaml
2025-06-07 18:17:12,632 - web_app - INFO - 数据库配置已存在，跳过导入
2025-06-07 18:17:12,632 - web_app - INFO - 确保目录存在: quant_app/data
2025-06-07 18:17:12,632 - web_app - INFO - 确保目录存在: quant_app/data/market_data
2025-06-07 18:17:12,632 - web_app - INFO - 确保目录存在: quant_app/data/exports
2025-06-07 18:17:12,633 - web_app - INFO - 确保目录存在: quant_app/data/logs
2025-06-07 18:17:12,633 - web_app - INFO - 启动Streamlit Web应用...
2025-06-07 18:17:12,633 - web_app - INFO - 执行命令: streamlit run f:\git_repo\miniqmt_bt\quant_app\app\Home.py --server.port 8501 --server.address localhost --browser.serverAddress localhost --theme.base light
2025-06-07 18:17:12,721 - web_app - INFO - Streamlit进程已启动，PID: 35972
2025-06-07 18:17:13,297 - web_app - INFO - Streamlit: 
2025-06-07 18:17:13,298 - web_app - INFO - Streamlit: Welcome to Streamlit!
2025-06-07 18:17:13,298 - web_app - INFO - Streamlit: 
2025-06-07 18:17:13,298 - web_app - INFO - Streamlit: If you’d like to receive helpful onboarding emails, news, offers, promotions,
2025-06-07 18:17:13,298 - web_app - INFO - Streamlit: and the occasional swag, please enter your email address below. Otherwise,
2025-06-07 18:17:13,298 - web_app - INFO - Streamlit: leave this field blank.
2025-06-07 18:17:13,298 - web_app - INFO - Streamlit: 
