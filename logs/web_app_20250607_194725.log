2025-06-07 19:47:25,698 - web_app - INFO - ==================================================
2025-06-07 19:47:25,698 - web_app - INFO - 启动量化交易系统Web界面
2025-06-07 19:47:25,698 - web_app - INFO - ==================================================
2025-06-07 19:47:25,698 - web_app - INFO - 初始化Web应用系统...
2025-06-07 19:47:25,874 - numexpr.utils - INFO - Note: NumExpr detected 32 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-06-07 19:47:25,883 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-06-07 19:47:26,038 - web_app - INFO - 检测到YAML配置文件: f:\git_repo\miniqmt_bt\config.yaml
2025-06-07 19:47:26,039 - web_app - INFO - 数据库配置已存在，跳过导入
2025-06-07 19:47:26,039 - web_app - INFO - 确保目录存在: quant_app/data
2025-06-07 19:47:26,039 - web_app - INFO - 确保目录存在: quant_app/data/market_data
2025-06-07 19:47:26,039 - web_app - INFO - 确保目录存在: quant_app/data/exports
2025-06-07 19:47:26,039 - web_app - INFO - 确保目录存在: quant_app/data/logs
2025-06-07 19:47:26,039 - web_app - INFO - 启动Streamlit Web应用...
2025-06-07 19:47:26,040 - web_app - INFO - 执行命令: streamlit run f:\git_repo\miniqmt_bt\quant_app\app\Home.py --server.port 8501 --server.address localhost --browser.serverAddress localhost --theme.base light
2025-06-07 19:47:26,084 - web_app - INFO - Streamlit进程已启动，PID: 3732
2025-06-07 19:47:27,453 - web_app - INFO - Streamlit: 
2025-06-07 19:47:27,453 - web_app - INFO - Streamlit: You can now view your Streamlit app in your browser.
2025-06-07 19:47:27,453 - web_app - INFO - Streamlit: 
2025-06-07 19:47:27,453 - web_app - INFO - Streamlit: URL: http://localhost:8501
2025-06-07 19:47:27,453 - web_app - INFO - Streamlit: 
2025-06-07 20:00:55,394 - web_app - INFO - Streamlit: 运行回测失败: 'TaskManager' object has no attribute 'start_task'
2025-06-07 20:01:21,652 - web_app - INFO - Streamlit: 运行回测失败: 'TaskManager' object has no attribute 'start_task'
2025-06-07 20:05:37,950 - web_app - INFO - Streamlit: 运行回测失败: 'TaskManager' object has no attribute 'start_task'
