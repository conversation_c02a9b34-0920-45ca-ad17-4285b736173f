2025-06-07 16:33:19,131 - web_app - INFO - ==================================================
2025-06-07 16:33:19,131 - web_app - INFO - 启动量化交易系统Web界面
2025-06-07 16:33:19,131 - web_app - INFO - ==================================================
2025-06-07 16:33:19,131 - web_app - INFO - 初始化Web应用系统...
2025-06-07 16:33:19,321 - numexpr.utils - INFO - Note: NumExpr detected 32 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-06-07 16:33:19,323 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-06-07 16:33:19,478 - web_app - ERROR - 初始化配置失败: cannot import name 'import_config_from_yaml' from 'quant_app.core.import_config' (F:\git_repo\miniqmt_bt\quant_app\core\import_config.py)
2025-06-07 16:33:19,484 - web_app - INFO - 确保目录存在: quant_app/data
2025-06-07 16:33:19,484 - web_app - INFO - 确保目录存在: quant_app/data/market_data
2025-06-07 16:33:19,484 - web_app - INFO - 确保目录存在: quant_app/data/exports
2025-06-07 16:33:19,484 - web_app - INFO - 确保目录存在: quant_app/data/logs
2025-06-07 16:33:19,484 - web_app - INFO - 启动Streamlit Web应用...
2025-06-07 16:33:19,484 - web_app - ERROR - 主页文件不存在: F:\git_repo\miniqmt_bt\quant_app\app\Home.py
2025-06-07 16:33:19,484 - web_app - ERROR - 启动失败，退出程序
