2025-06-07 16:44:30,343 - web_app - INFO - ==================================================
2025-06-07 16:44:30,343 - web_app - INFO - 启动量化交易系统Web界面
2025-06-07 16:44:30,343 - web_app - INFO - ==================================================
2025-06-07 16:44:30,343 - web_app - INFO - 初始化Web应用系统...
2025-06-07 16:44:30,518 - numexpr.utils - INFO - Note: NumExpr detected 32 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-06-07 16:44:30,518 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-06-07 16:44:30,670 - web_app - INFO - 检测到YAML配置文件: F:\git_repo\miniqmt_bt\config.yaml
2025-06-07 16:44:30,670 - web_app - INFO - 数据库配置已存在，跳过导入
2025-06-07 16:44:30,670 - web_app - INFO - 确保目录存在: quant_app/data
2025-06-07 16:44:30,670 - web_app - INFO - 确保目录存在: quant_app/data/market_data
2025-06-07 16:44:30,670 - web_app - INFO - 确保目录存在: quant_app/data/exports
2025-06-07 16:44:30,685 - web_app - INFO - 确保目录存在: quant_app/data/logs
2025-06-07 16:44:30,685 - web_app - INFO - 启动Streamlit Web应用...
2025-06-07 16:44:30,685 - web_app - INFO - 执行命令: streamlit run F:\git_repo\miniqmt_bt\quant_app\app\Home.py --server.port 8501 --server.address localhost --browser.serverAddress localhost --theme.base light
2025-06-07 16:44:30,707 - web_app - INFO - Streamlit进程已启动，PID: 3264
2025-06-07 16:44:31,489 - web_app - INFO - Streamlit: Traceback (most recent call last):
2025-06-07 16:44:31,489 - web_app - INFO - Streamlit: File "C:\ProgramData\anaconda3\Scripts\streamlit-script.py", line 6, in <module>
2025-06-07 16:44:31,489 - web_app - INFO - Streamlit: from streamlit.cli import main
2025-06-07 16:44:31,489 - web_app - INFO - Streamlit: ModuleNotFoundError: No module named 'streamlit.cli'
2025-06-07 16:44:31,572 - web_app - INFO - Web应用已关闭
