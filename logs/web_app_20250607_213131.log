2025-06-07 21:31:31,151 - web_app - INFO - ==================================================
2025-06-07 21:31:31,151 - web_app - INFO - 启动量化交易系统Web界面
2025-06-07 21:31:31,151 - web_app - INFO - ==================================================
2025-06-07 21:31:31,151 - web_app - INFO - 初始化Web应用系统...
2025-06-07 21:31:31,351 - numexpr.utils - INFO - Note: NumExpr detected 32 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-06-07 21:31:31,351 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-06-07 21:31:31,508 - web_app - INFO - 检测到YAML配置文件: f:\git_repo\miniqmt_bt\config.yaml
2025-06-07 21:31:31,508 - web_app - INFO - 数据库配置已存在，跳过导入
2025-06-07 21:31:31,508 - web_app - INFO - 确保目录存在: quant_app/data
2025-06-07 21:31:31,508 - web_app - INFO - 确保目录存在: quant_app/data/market_data
2025-06-07 21:31:31,508 - web_app - INFO - 确保目录存在: quant_app/data/exports
2025-06-07 21:31:31,508 - web_app - INFO - 确保目录存在: quant_app/data/logs
2025-06-07 21:31:31,508 - web_app - INFO - 启动Streamlit Web应用...
2025-06-07 21:31:31,508 - web_app - INFO - 执行命令: streamlit run f:\git_repo\miniqmt_bt\quant_app\app\Home.py --server.port 8501 --server.address localhost --browser.serverAddress localhost --theme.base light
2025-06-07 21:31:31,555 - web_app - INFO - Streamlit进程已启动，PID: 5548
2025-06-07 21:31:32,935 - web_app - INFO - Streamlit: 
2025-06-07 21:31:32,935 - web_app - INFO - Streamlit: You can now view your Streamlit app in your browser.
2025-06-07 21:31:32,936 - web_app - INFO - Streamlit: 
2025-06-07 21:31:32,936 - web_app - INFO - Streamlit: URL: http://localhost:8501
2025-06-07 21:31:32,936 - web_app - INFO - Streamlit: 
2025-06-07 21:31:44,731 - web_app - INFO - Streamlit: 启动任务失败: 2cd3beeb-a1e6-4604-8be0-f0ceb20927d5, 错误: 缺少必要参数: stock_code
2025-06-07 21:32:23,353 - web_app - INFO - Streamlit: +------------------------- Traceback (most recent call last) --------------------------+
2025-06-07 21:32:23,354 - web_app - INFO - Streamlit: | F:\git_repo\miniqmt_bt\quant_app\core\data_handler.py:396 in get_market_overview     |
2025-06-07 21:32:23,354 - web_app - INFO - Streamlit: |                                                                                      |
2025-06-07 21:32:23,354 - web_app - INFO - Streamlit: |   393             for name, code in indices.items():                                 |
2025-06-07 21:32:23,354 - web_app - INFO - Streamlit: |   394                 try:                                                           |
2025-06-07 21:32:23,354 - web_app - INFO - Streamlit: |   395                     # 使用正确的QMT API获取数据                                |
2025-06-07 21:32:23,354 - web_app - INFO - Streamlit: | > 396                     data = self.qmt_client.get_kline_data(                     |
2025-06-07 21:32:23,354 - web_app - INFO - Streamlit: |   397                         code,                                                  |
2025-06-07 21:32:23,354 - web_app - INFO - Streamlit: |   398                         period='1d',                                           |
2025-06-07 21:32:23,354 - web_app - INFO - Streamlit: |   399                         count=1,                                               |
2025-06-07 21:32:23,354 - web_app - INFO - Streamlit: +--------------------------------------------------------------------------------------+
2025-06-07 21:32:23,355 - web_app - INFO - Streamlit: AttributeError: 'DataHandler' object has no attribute 'qmt_client'
2025-06-07 21:32:23,355 - web_app - INFO - Streamlit: 
2025-06-07 21:32:23,355 - web_app - INFO - Streamlit: During handling of the above exception, another exception occurred:
2025-06-07 21:32:23,355 - web_app - INFO - Streamlit: 
2025-06-07 21:32:23,355 - web_app - INFO - Streamlit: +------------------------- Traceback (most recent call last) --------------------------+
2025-06-07 21:32:23,355 - web_app - INFO - Streamlit: | F:\git_repo\miniqmt_bt\quant_app\core\data_handler.py:410 in get_market_overview     |
2025-06-07 21:32:23,355 - web_app - INFO - Streamlit: |                                                                                      |
2025-06-07 21:32:23,355 - web_app - INFO - Streamlit: |   407                             'change_pct': (data[0]['close'] - data[0]['open']) |
2025-06-07 21:32:23,355 - web_app - INFO - Streamlit: |   408                         }                                                      |
2025-06-07 21:32:23,355 - web_app - INFO - Streamlit: |   409                 except Exception as e:                                         |
2025-06-07 21:32:23,355 - web_app - INFO - Streamlit: | > 410                     logger.warning(f"获取指数 {code} 行情失败: {str(e)}")      |
2025-06-07 21:32:23,355 - web_app - INFO - Streamlit: |   411                     continue                                                   |
2025-06-07 21:32:23,356 - web_app - INFO - Streamlit: |   412                                                                                |
2025-06-07 21:32:23,356 - web_app - INFO - Streamlit: |   413             if not market_data:                                                |
2025-06-07 21:32:23,356 - web_app - INFO - Streamlit: +--------------------------------------------------------------------------------------+
2025-06-07 21:32:23,356 - web_app - INFO - Streamlit: NameError: name 'logger' is not defined
2025-06-07 21:32:23,356 - web_app - INFO - Streamlit: 
2025-06-07 21:32:23,356 - web_app - INFO - Streamlit: During handling of the above exception, another exception occurred:
2025-06-07 21:32:23,356 - web_app - INFO - Streamlit: 
2025-06-07 21:32:23,356 - web_app - INFO - Streamlit: +------------------------- Traceback (most recent call last) --------------------------+
2025-06-07 21:32:23,356 - web_app - INFO - Streamlit: | C:\ProgramData\anaconda3\Lib\site-packages\streamlit\runtime\scriptrunner\exec_code. |
2025-06-07 21:32:23,356 - web_app - INFO - Streamlit: | py:121 in exec_func_with_error_handling                                              |
2025-06-07 21:32:23,356 - web_app - INFO - Streamlit: |                                                                                      |
2025-06-07 21:32:23,356 - web_app - INFO - Streamlit: | C:\ProgramData\anaconda3\Lib\site-packages\streamlit\runtime\scriptrunner\script_run |
2025-06-07 21:32:23,356 - web_app - INFO - Streamlit: | ner.py:643 in code_to_exec                                                           |
2025-06-07 21:32:23,357 - web_app - INFO - Streamlit: |                                                                                      |
2025-06-07 21:32:23,357 - web_app - INFO - Streamlit: | C:\ProgramData\anaconda3\Lib\site-packages\streamlit\runtime\scriptrunner\script_run |
2025-06-07 21:32:23,357 - web_app - INFO - Streamlit: | ner.py:164 in _mpa_v1                                                                |
2025-06-07 21:32:23,357 - web_app - INFO - Streamlit: |                                                                                      |
2025-06-07 21:32:23,357 - web_app - INFO - Streamlit: | C:\ProgramData\anaconda3\Lib\site-packages\streamlit\navigation\page.py:297 in run   |
2025-06-07 21:32:23,357 - web_app - INFO - Streamlit: |                                                                                      |
2025-06-07 21:32:23,357 - web_app - INFO - Streamlit: | 2025-06-07 21:32:23.354 Uncaught app execution
2025-06-07 21:32:23,357 - web_app - INFO - Streamlit: Traceback (most recent call last):
2025-06-07 21:32:23,357 - web_app - INFO - Streamlit: File "F:\git_repo\miniqmt_bt\quant_app\core\data_handler.py", line 396, in get_market_overview
2025-06-07 21:32:23,357 - web_app - INFO - Streamlit: data = self.qmt_client.get_kline_data(
2025-06-07 21:32:23,357 - web_app - INFO - Streamlit: ^^^^^^^^^^^^^^^
2025-06-07 21:32:23,357 - web_app - INFO - Streamlit: AttributeError: 'DataHandler' object has no attribute 'qmt_client'
2025-06-07 21:32:23,358 - web_app - INFO - Streamlit: 
2025-06-07 21:32:23,358 - web_app - INFO - Streamlit: During handling of the above exception, another exception occurred:
2025-06-07 21:32:23,358 - web_app - INFO - Streamlit: 
2025-06-07 21:32:23,358 - web_app - INFO - Streamlit: Traceback (most recent call last):
2025-06-07 21:32:23,358 - web_app - INFO - Streamlit: File "F:\git_repo\miniqmt_bt\quant_app\core\data_handler.py", line 410, in get_market_overview
2025-06-07 21:32:23,358 - web_app - INFO - Streamlit: logger.warning(f"获取指数 {code} 行情失败: {str(e)}")
2025-06-07 21:32:23,358 - web_app - INFO - Streamlit: ^^^^^^
2025-06-07 21:32:23,359 - web_app - INFO - Streamlit: NameError: name 'logger' is not defined
2025-06-07 21:32:23,359 - web_app - INFO - Streamlit: 
2025-06-07 21:32:23,359 - web_app - INFO - Streamlit: During handling of the above exception, another exception occurred:
2025-06-07 21:32:23,359 - web_app - INFO - Streamlit: 
2025-06-07 21:32:23,359 - web_app - INFO - Streamlit: Traceback (most recent call last):
2025-06-07 21:32:23,359 - web_app - INFO - Streamlit: File "C:\ProgramData\anaconda3\Lib\site-packages\streamlit\runtime\scriptrunner\exec_code.py", line 121, in exec_func_with_error_handling
2025-06-07 21:32:23,360 - web_app - INFO - Streamlit: result = func()
2025-06-07 21:32:23,360 - web_app - INFO - Streamlit: ^^^^^^
2025-06-07 21:32:23,360 - web_app - INFO - Streamlit: File "C:\ProgramData\anaconda3\Lib\site-packages\streamlit\runtime\scriptrunner\script_runner.py", line 643, in code_to_exec
2025-06-07 21:32:23,360 - web_app - INFO - Streamlit: _mpa_v1(self._main_script_path)
2025-06-07 21:32:23,360 - web_app - INFO - Streamlit: File "C:\ProgramData\anaconda3\Lib\site-packages\streamlit\runtime\scriptrunner\script_runner.py", line 164, in _mpa_v1
2025-06-07 21:32:23,360 - web_app - INFO - Streamlit: page.run()
2025-06-07 21:32:23,360 - web_app - INFO - Streamlit: File "C:\ProgramData\anaconda3\Lib\site-packages\streamlit\navigation\page.py", line 297, in run
2025-06-07 21:32:23,360 - web_app - INFO - Streamlit: exec(code, module.__dict__)
2025-06-07 21:32:23,360 - web_app - INFO - Streamlit: File "F:\git_repo\miniqmt_bt\quant_app\app\pages\1_\U0001f4ca_总览.py", line 82, in <module>
2025-06-07 21:32:23,360 - web_app - INFO - Streamlit: market_data = data_handler.get_market_overview()
2025-06-07 21:32:23,361 - web_app - INFO - Streamlit: ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-07 21:32:23,361 - web_app - INFO - Streamlit: File "F:\git_repo\miniqmt_bt\quant_app\core\data_handler.py", line 419, in get_market_overview
2025-06-07 21:32:23,361 - web_app - INFO - Streamlit: logger.error(f"获取市场概览失败: {str(e)}")
2025-06-07 21:32:23,361 - web_app - INFO - Streamlit: ^^^^^^
2025-06-07 21:32:23,361 - web_app - INFO - Streamlit: NameError: name 'logger' is not defined
