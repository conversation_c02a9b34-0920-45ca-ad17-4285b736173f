#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动选股交易系统
集成选股和交易管理功能，实现完全自动化的交易流程
"""

import sys
import time
import signal
import subprocess
import argparse
import os
from datetime import datetime
from simple_trade_manager import SimpleTradeManager
import logging
import yaml

class AutoTradingSystem:
    def __init__(self):
        """初始化自动交易系统"""
        self.manager = SimpleTradeManager()
        self.running = False
        
        # 设置信号处理器，用于优雅退出
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # 设置日志
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """设置日志"""
        # 加载配置获取日志设置
        config = self.load_config()
        log_config = config.get('logging', {})
        
        logger = logging.getLogger("AutoTrading")
        logger.setLevel(getattr(logging, log_config.get('level', 'INFO')))
        
        # 清除已有的handlers，避免重复
        if logger.handlers:
            logger.handlers.clear()
        
        # 确保日志目录存在
        log_dir = log_config.get('log_dir', 'logs')
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 添加文件处理器
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f'{log_dir}/auto_trading_{timestamp}.log'
        fh = logging.FileHandler(log_filename, encoding='utf-8')
        fh.setLevel(getattr(logging, log_config.get('file_level', 'DEBUG')))
        
        # 添加控制台处理器
        ch = logging.StreamHandler()
        ch.setLevel(getattr(logging, log_config.get('console_level', 'INFO')))
        
        # 创建formatter
        file_format = log_config.get('file_format', 
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s')
        console_format = log_config.get('console_format', 
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        
        file_formatter = logging.Formatter(file_format, datefmt='%Y-%m-%d %H:%M:%S')
        console_formatter = logging.Formatter(console_format, datefmt='%Y-%m-%d %H:%M:%S')
        
        fh.setFormatter(file_formatter)
        ch.setFormatter(console_formatter)
        
        # 添加handlers
        logger.addHandler(fh)
        logger.addHandler(ch)
        
        # 防止日志向上传播，避免重复输出
        logger.propagate = False
        
        return logger
        
    def _signal_handler(self, signum, frame):
        """信号处理器，用于优雅退出"""
        print(f"\n收到退出信号 {signum}，正在停止自动交易系统...")
        self.stop()
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open('config.yaml', 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {str(e)}")
            return {}
    
    def auto_select_stocks(self, target_date=None, select_only=False):
        """执行自动选股"""
        try:
            self.logger.info("开始执行自动选股...")
            
            # 构建选股命令
            # 尝试使用当前Python解释器
            import sys
            python_executable = sys.executable
            cmd = [python_executable, "select_stocks.py"]
            if target_date:
                cmd.extend(["--date", target_date])
            if select_only:
                cmd.append("--select-only")
            
            self.logger.info(f"执行命令: {' '.join(cmd)}")
            
            # 执行选股脚本，实时显示输出
            process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.STDOUT,  # 将stderr重定向到stdout
                text=True, 
                bufsize=1,  # 行缓冲
                universal_newlines=True
            )
            
            # 实时读取并显示输出
            output_lines = []
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    output_line = output.strip()
                    output_lines.append(output_line)
                    # 同时输出到控制台和日志
                    print(f"[选股] {output_line}")
                    self.logger.info(f"选股输出: {output_line}")
            
            # 等待进程结束
            return_code = process.poll()
            
            if return_code == 0:
                self.logger.info("自动选股完成")
                return True
            else:
                self.logger.error(f"自动选股失败，退出代码: {return_code}")
                # 输出完整的错误信息
                if output_lines:
                    self.logger.error("选股脚本输出:")
                    for line in output_lines[-10:]:  # 显示最后10行
                        self.logger.error(f"  {line}")
                return False
                
        except Exception as e:
            self.logger.error(f"执行自动选股时出错: {str(e)}")
            return False
    
    def start_auto_trading(self, target_date=None, select_only=False):
        """启动自动交易系统"""
        print("=" * 60)
        print("[启动] 自动选股交易系统")
        print("=" * 60)
        
        # 检查配置
        config = self.load_config()
        is_live = config.get('backtest', {}).get('is_live', False)
        
        if not is_live and not select_only:
            print("[警告] 配置文件中 is_live 设置为 false，将只执行选股，不启动交易")
            select_only = True
        
        # 执行自动选股
        if not self.auto_select_stocks(target_date, select_only):
            print("[失败] 自动选股失败，退出程序")
            return False
        
        if select_only:
            print("[完成] 自动选股完成（仅选股模式）")
            return True
        
        # 等待一下，确保配置文件已更新
        time.sleep(2)
        
        # 重新加载配置，获取选中的股票
        config = self.load_config()
        stock_codes = config.get('backtest', {}).get('stock_codes', [])
        
        if not stock_codes:
            print("[失败] 未找到选中的股票，无法启动交易")
            return False
        
        print(f"[信息] 选中的股票: {', '.join(stock_codes)}")
        
        # 启动交易进程
        print("[启动] 交易进程...")
        success = self.manager.init_from_config()
        
        if success:
            self.running = True
            active_stocks = self.manager.get_active_stocks()
            print(f"[成功] 自动交易系统启动成功！")
            print(f"[信息] 当前交易股票: {', '.join(active_stocks)}")
            print(f"[时间] 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 60)
            return True
        else:
            print("[失败] 交易进程启动失败")
            return False
    

    
    def stop(self):
        """停止自动交易系统"""
        if not self.running:
            return
            
        print("\n[停止] 正在停止自动交易系统...")
        self.running = False
        
        # 获取当前活跃股票
        active_stocks = self.manager.get_active_stocks()
        if active_stocks:
            print(f"[信息] 停止 {len(active_stocks)} 只股票的交易进程...")
            
        # 清理所有资源
        self.manager.cleanup()
        
        print("[完成] 自动交易系统已安全停止")
        print(f"[时间] 停止时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='自动选股交易系统')
    parser.add_argument('--date', type=str, help='目标日期（格式：YYYYMMDD），不提供则使用当前日期')
    parser.add_argument('--select-only', action='store_true', help='仅执行选股，不启动交易')
    
    args = parser.parse_args()
    
    # 创建自动交易系统实例
    auto_trading = AutoTradingSystem()
    
    try:
        # 启动系统
        success = auto_trading.start_auto_trading(args.date, args.select_only)
        
        if success:
            print("\n[完成] 系统启动完成")
            if not args.select_only:
                print("[提示] 可使用以下命令管理交易进程:")
                print("   python trade_cli.py list      # 查看活跃股票")
                print("   python trade_cli.py status <股票代码>  # 查看股票状态")
                print("   python trade_cli.py stop-all  # 停止所有交易")
        
    except Exception as e:
        print(f"[失败] 系统运行出错: {str(e)}")
        
    finally:
        # 确保系统正确停止
        if not args.select_only:
            auto_trading.stop()

if __name__ == '__main__':
    main() 