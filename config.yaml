backtest:
  cache_dir: select_stocks
  commission:
    commission: 0.0003
    dismiss5: true
    percabs: true
    stamp_duty: 0.001
    transaction_fees: 7.0e-05
    transfer_fee: 2.0e-05
  enable_log_analysis: true
  enable_plot_save: true
  end_time: '**************'
  initial_cash: 100000
  is_live: false
  is_live_debug: false
  live_debug_size: 10
  slippage: 0.0
  start_time: '**************'
  stock_codes:
  - 123093.SZ
  - 123241.SZ
  - 128144.SZ
  - 127055.SZ
  - 127071.SZ
  use_stock_cache: true
logging:
  console_format: '%(asctime)s - %(levelname)s - %(message)s'
  console_level: INFO
  file_format: '%(asctime)s - %(levelname)s - %(message)s'
  file_level: DEBUG
  level: INFO
  log_dir: logs
qmt:
  account_id: ''
risk_management:
  max_holding_stocks: 10
  max_position_ratio: 0.8
  single_stock_limit: 0.2
  stop_loss_ratio: 0.05
  take_profit_ratio: 0.2
  trailing_stop: false
strategy:
  atr_stop_multiplier: 2
  batch_loss_target_1: 0.005
  batch_loss_target_2: 0.01
  batch_profit_target_1: 0.008
  batch_profit_target_2: 0.015
  batch_profit_target_3: 0.025
  cancel_on_clear_position: true
  clear_position_time: '14:55:00'
  enable_auto_cancel: true
  macd_fast: 8
  macd_fast_long: 24
  macd_signal: 9
  macd_signal_long: 27
  macd_slow: 17
  macd_slow_long: 54
  min_profit_to_sell: 0.008
  muti_num: 3
  order_timeout_seconds: 60
  position_limits:
    enable_amount_limits: true
    max_single_stock_total_amount: 80000
    max_single_trade_amount: 20000
    min_single_trade_amount: 1000
  position_size: 0.2
  profit_target: 0.05
  risk_control:
    enable_risk_control: true
    max_consecutive_losses_per_stock: 3
  risk_per_trade: 0.01
  roc_period: 10
  roc_weakening_threshold: 0.7
  stop_loss: 0.005
  trade_time_end: '14:55:00'
  trade_time_start: 09:40:00
  use_dynamic_position: true
  vol_ma_period: 20
  volume_shrink_threshold: 0.7
strategy_config:
  active_strategy: optimized_kdj
  strategies:
    kdj_volume:
      class: KDJVolumeResonanceStrategy
      description: KDJ+成交量共振策略（原版）
      module: kdj_volume_resonance_strategy
    optimized_kdj:
      class: OptimizedKDJVolumeStrategy
      description: 优化的KDJ+成交量共振策略
      module: kdj_volume_resonance_strategy_optimized
    optimized_kdj_custom:
      class: OptimizedKDJVolumeStrategy
      description: 优化的KDJ+成交量共振策略（自定义参数）
      module: kdj_volume_resonance_strategy_optimized
      parameters:
        j_overbought: 85
        j_oversold: 15
        kdj_period: 14
        profit_target_1: 0.03
        stop_loss: 0.015
        volume_ma_period: 20
    triple_resonance:
      class: TripleResonanceStrategy
      description: 三重共振策略（ConnorsRSI + MACD + Volume）
      module: my_connors_rsi_strategy
system:
  auto_update_data: false
  data_update_interval: 300
  enable_notifications: false
  log_level: INFO
  log_retention_days: 30
  max_concurrent_tasks: 5
  notification_email: ''
  task_timeout: 120
