import datetime
import time
import yaml
import pandas as pd
import os
import subprocess
import logging
import requests
from xtquant import xtdata
from utils import WechatNotifier, FileUtils, ConfigUtils
from stock_data_manager import get_stock_data_manager, format_stock_code_with_name
import sys

logger = None  # 将在main函数中初始化

# 配置日志
def setup_logger(log_dir=None):
    # 确定日志目录
    if log_dir:
        logs_dir = log_dir
    else:
        logs_dir = 'logs'
    
    # 确保logs目录存在
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
        
    logging.basicConfig(
        level=logging.INFO,  # 基础级别保持INFO
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'{logs_dir}/stock_selection_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def load_config():
    """加载配置文件"""
    # 检查是否有命令行参数指定的配置文件路径（多进程回测时使用）
    config_path = 'config.yaml'  # 默认配置文件路径
    
    # 从命令行参数中查找 --config-path
    if '--config-path' in sys.argv:
        try:
            config_path_index = sys.argv.index('--config-path')
            if config_path_index + 1 < len(sys.argv):
                config_path = sys.argv[config_path_index + 1]
                if 'logger' in globals() and logger is not None:
                    logger.info(f"使用命令行指定的配置文件: {config_path}")
        except (ValueError, IndexError) as e:
            if 'logger' in globals() and logger is not None:
                logger.warning(f"解析配置文件路径参数失败: {str(e)}，使用默认配置文件")
    
    # 使用ConfigUtils加载配置
    config = ConfigUtils.load_yaml_config(config_path)
    if not config:
        if 'logger' in globals() and logger is not None:
            logger.error(f"加载配置文件 {config_path} 失败，使用默认配置")
        config = {"backtest": {"cache_dir": "select_stocks"}}
    
    # 记录使用的配置文件路径
    if 'logger' in globals() and logger is not None:
        logger.debug(f"使用配置文件: {config_path}")
        if config['backtest'].get('start_time'):
            logger.debug(f"配置文件中的回测时间: {config['backtest']['start_time']} - {config['backtest'].get('end_time', '')}")
    
    return config

def update_config(config, stock_codes):
    """更新配置文件中的股票代码列表"""
    config['backtest']['stock_codes'] = stock_codes
    
    # 获取配置文件路径 - 优先使用命令行参数指定的路径
    config_path = 'config.yaml'  # 默认配置文件路径
    
    # 从命令行参数中查找 --config-path
    if '--config-path' in sys.argv:
        try:
            config_path_index = sys.argv.index('--config-path')
            if config_path_index + 1 < len(sys.argv):
                config_path = sys.argv[config_path_index + 1]
                if 'logger' in globals() and logger is not None:
                    logger.info(f"使用命令行指定的配置文件进行更新: {config_path}")
        except (ValueError, IndexError) as e:
            if 'logger' in globals() and logger is not None:
                logger.warning(f"解析配置文件路径参数失败: {str(e)}，使用默认配置文件")
    
    # 确保logger存在
    if 'logger' in globals() and logger is not None:
        logger.info(f"更新配置文件: {config_path}")
    
    # 使用ConfigUtils保存配置到指定路径
    if ConfigUtils.save_yaml_config(config, config_path):
        # 格式化股票代码显示，包含中文简称
        formatted_codes = [format_stock_code_with_name(code) for code in stock_codes]
        if 'logger' in globals() and logger is not None:
            logger.info(f"配置文件已更新，用于回测的股票：{', '.join(formatted_codes)}")
            logger.info(f"配置文件路径: {config_path}")
            logger.info(f"回测时间: {config['backtest'].get('start_time', 'N/A')} - {config['backtest'].get('end_time', 'N/A')}")
    else:
        if 'logger' in globals() and logger is not None:
            logger.error(f"更新配置文件 {config_path} 失败")
    
    # 如果是实盘模式，发送微信通知
    if config['backtest'].get('is_live', False):
        # 获取目标日期 - 从配置或当前日期
        target_date = None
        if config['backtest'].get('start_time'):
            start_time_str = config['backtest']['start_time']
            if len(start_time_str) >= 8:
                target_date = start_time_str[:8]  # 提取YYYYMMDD部分
        
        if not target_date:
            target_date = datetime.datetime.now().strftime('%Y%m%d')
            
        WechatNotifier.send_stock_selection_notification(stock_codes, is_live=True, target_date=target_date)

def cache_stocks(stock_codes, target_date):
    """缓存选股结果
    
    Args:
        stock_codes: 选出的股票代码列表
        target_date: 目标日期，格式为YYYYMMDD
    """
    config = load_config()
    cache_dir = config['backtest'].get('cache_dir', 'select_stocks')
    
    # 确保缓存目录存在
    FileUtils.ensure_dir_exists(cache_dir)
    
    # 创建缓存文件名
    cache_file = os.path.join(cache_dir, f"stocks_{target_date}.yaml")
    
    # 构建缓存数据
    cache_data = {
        'date': target_date,
        'stock_codes': stock_codes,
        'timestamp': datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    }
    
    # 保存缓存
    if ConfigUtils.save_yaml_config(cache_data, cache_file):
        logger.info(f"选股结果已缓存到: {cache_file}")
    else:
        logger.error(f"缓存选股结果到 {cache_file} 失败")

def load_cached_stocks(target_date):
    """从缓存加载选股结果
    
    Args:
        target_date: 目标日期，格式为YYYYMMDD
        
    Returns:
        list: 选出的股票代码列表，如果没有缓存则返回None
    """
    config = load_config()
    cache_dir = config['backtest'].get('cache_dir', 'select_stocks')
    cache_file = os.path.join(cache_dir, f"stocks_{target_date}.yaml")
    
    if not os.path.exists(cache_file):
        logger.info(f"未找到日期 {target_date} 的选股缓存")
        return None
    
    try:
        with open(cache_file, 'r', encoding='utf-8') as f:
            cache_data = yaml.safe_load(f)
        
        stock_codes = cache_data.get('stock_codes', [])
        logger.info(f"从缓存加载日期 {target_date} 的选股结果：{', '.join(stock_codes)}")
        return stock_codes
    except Exception as e:
        logger.error(f"加载选股缓存 {cache_file} 时出错: {str(e)}")
        return None

def has_stock_cache(target_date):
    """检查是否存在指定日期的选股缓存
    
    Args:
        target_date: 目标日期，格式为YYYYMMDD
        
    Returns:
        bool: 是否存在缓存
    """
    config = load_config()
    cache_dir = config['backtest'].get('cache_dir', 'select_stocks')
    cache_file = os.path.join(cache_dir, f"stocks_{target_date}.yaml")
    
    return os.path.exists(cache_file)

def wait_until_market_open():
    """等待直到市场开盘前5分钟"""
    now = datetime.datetime.now()
    target_time = now.replace(hour=9, minute=35, second=0, microsecond=0)
    
    # 如果当前时间在9:25之前，等待到9:25
    if now < target_time:
        wait_seconds = (target_time - now).total_seconds()
        logger.info(f"当前时间: {now.strftime('%H:%M:%S')}，等待 {wait_seconds:.0f} 秒到 09:35:00")
        time.sleep(wait_seconds)
        logger.info("等待结束，准备获取市场数据")

def get_sh_convertible_bonds():
    """获取所有可转债列表"""
    # 使用xtquant API获取所有可转债
    logger.info("获取可转债列表")
    # 直接获取沪深转债列表
    all_cb = xtdata.get_stock_list_in_sector('沪深转债')
    # # 只保留上交所的可转债（以.SH结尾的代码）
    # sh_cb = [code for code in all_cb if code.endswith('.SH')]
    logger.info(f"获取到 {len(all_cb)} 只沪深转债")
    return all_cb

def on_progress(progress):
    """数据下载进度回调"""
    if progress % 10 == 0:
        logger.debug(f"下载进度: {progress}%")

def get_top_amount_stocks(sh_cb, limit=10, target_date=None):
    """获取成交额最大的股票
    
    Args:
        sh_cb: 可转债列表
        limit: 返回的股票数量限制
        target_date: 目标日期，格式为YYYYMMDD，不提供则使用当前日期
    
    Returns:
        list: 成交额最大的股票代码列表
    """
    # 如果未提供目标日期，则使用当前日期
    if not target_date:
        target_date = datetime.datetime.now().strftime('%Y%m%d')
    
    logger.info(f"获取 {target_date} 的市场数据，共 {len(sh_cb)} 只股票")
    
    try:
        # 创建空的数据字典
        dict1 = {}
        
        # 调试信息：记录所有股票代码
        logger.debug(f"股票列表前10个: {sh_cb[:10]}")
        
        # 一次查询一个股票，间隔0.01秒
        for idx, stock_code in enumerate(sh_cb):
            try:
                logger.debug(f"下载 {stock_code} 数据 ({idx+1}/{len(sh_cb)})")
                
                # 使用单一股票下载接口
                logger.debug(f"开始下载历史数据: {stock_code}")
                try:
                    rs = xtdata.download_history_data(stock_code, 'tick', start_time=f'{target_date}093500', end_time=f'{target_date}093505')
                    logger.debug(f"下载结果: {rs}")
                except Exception as download_err:
                    logger.error(f"下载历史数据出错: {str(download_err)}")
                
                # 获取tick数据
                logger.debug(f"开始获取市场数据: {stock_code}")
                try:
                    stock_data = xtdata.get_market_data_ex(field_list=[], stock_list=[stock_code], period='tick', 
                                                        start_time=f'{target_date}093500', end_time=f'{target_date}093505', 
                                                        count=-1, dividend_type='none', fill_data=True)
                    
                    # 记录获取到的数据结构
                    logger.debug(f"获取到的数据结构: {type(stock_data)}")
                    logger.debug(f"数据键值: {list(stock_data.keys()) if isinstance(stock_data, dict) else 'Not a dict'}")
                    
                    if stock_data and isinstance(stock_data, dict) and stock_code in stock_data:
                        data_sample = stock_data[stock_code]
                        logger.debug(f"获取到数据样例: {type(data_sample)}")
                        logger.debug(f"数据长度: {len(data_sample) if hasattr(data_sample, '__len__') else 'Unknown'}")
                        dict1[stock_code] = stock_data[stock_code]
                    else:
                        logger.warning(f"获取 {stock_code} 数据失败或无数据")
                        if isinstance(stock_data, dict):
                            for k in stock_data.keys():
                                logger.debug(f"  数据包含键: {k}")
                except Exception as market_err:
                    logger.error(f"获取市场数据出错: {str(market_err)}")
                
                # 添加0.01秒间隔
                time.sleep(0.01)
            except Exception as e:
                logger.error(f"处理 {stock_code} 数据时出错: {str(e)}", exc_info=True)  # 添加异常堆栈
                continue
        
        # 记录获取的数据数量
        logger.info(f"成功获取 {len(dict1)} 只股票的数据")
        
        # 如果没有获取到数据，则尝试获取前一天的数据
        if not dict1:
            logger.warning(f"未能获取 {target_date} 的市场数据，尝试获取前一天的数据")
            for i in range(1, 30):
                # 从目标日期回溯，而不是当前日期
                target_date_obj = datetime.datetime.strptime(target_date, '%Y%m%d')
                date = (target_date_obj - datetime.timedelta(days=i)).strftime('%Y%m%d')
                logger.info(f"尝试获取 {date} 的数据")
                
                for idx, stock_code in enumerate(sh_cb):
                    try:
                        logger.info(f"下载 {stock_code} 历史数据 ({date}) ({idx+1}/{len(sh_cb)})")
                        
                        # 使用单一股票下载接口
                        try:
                            rs = xtdata.download_history_data(stock_code, 'tick', start_time=f'{date}093500', end_time=f'{date}093505')
                            logger.debug(f"下载结果: {rs}")
                        except Exception as download_err:
                            logger.error(f"下载历史数据出错: {str(download_err)}")
                        
                        # 获取tick数据
                        try:
                            stock_data = xtdata.get_market_data_ex(field_list=[], stock_list=[stock_code], period='tick', 
                                                                 start_time=f'{date}093500', end_time=f'{date}093505', 
                                                                 count=-1, dividend_type='none', fill_data=True)
                            
                            # 详细记录数据结构
                            logger.debug(f"获取到的数据结构: {type(stock_data)}")
                            logger.debug(f"数据键值: {list(stock_data.keys()) if isinstance(stock_data, dict) else 'Not a dict'}")
                            
                            if stock_data and isinstance(stock_data, dict) and stock_code in stock_data:
                                data_sample = stock_data[stock_code]
                                logger.debug(f"获取到数据样例: {type(data_sample)}")
                                logger.debug(f"数据长度: {len(data_sample) if hasattr(data_sample, '__len__') else 'Unknown'}")
                                dict1[stock_code] = stock_data[stock_code]
                            else:
                                logger.warning(f"获取 {stock_code} 历史数据失败或无数据")
                        except Exception as market_err:
                            logger.error(f"获取历史市场数据出错: {str(market_err)}")
                        
                        # 添加0.01秒间隔
                        time.sleep(0.01)
                    except Exception as e:
                        logger.error(f"处理 {stock_code} 历史数据时出错: {str(e)}", exc_info=True)
                        continue
                
                # 记录获取的数据数量
                logger.info(f"成功获取 {date} 日 {len(dict1)} 只股票的数据")
                
                if dict1:
                    logger.info(f"获取到 {date} 的市场数据，共 {len(dict1)} 只股票")
                    break
        
        # 处理数据
        logger.info("开始处理获取到的数据")
        
        if not dict1:
            logger.error("未获取到任何股票数据，处理终止")
            return []
            
        try:
            # 记录字典的键
            logger.debug(f"数据字典包含以下股票: {list(dict1.keys())}")
            
            # 检查数据结构
            for k, v in list(dict1.items())[:3]:  # 只记录前3个样本
                logger.debug(f"股票 {k} 数据类型: {type(v)}")
                if hasattr(v, 'columns'):
                    logger.debug(f"股票 {k} 数据列: {list(v.columns)}")
                elif hasattr(v, 'keys'):
                    logger.debug(f"股票 {k} 数据键: {list(v.keys())}")
            
            # 使用安全的方式构建DataFrame
            daily_data = pd.DataFrame()
            for k, v in dict1.items():
                try:
                    # 确保v是一个DataFrame或可转换为DataFrame的对象
                    if not isinstance(v, pd.DataFrame):
                        logger.warning(f"股票 {k} 的数据不是DataFrame，尝试转换")
                        v = pd.DataFrame(v)
                    
                    # 添加code列
                    v['code'] = k
                    daily_data = pd.concat([daily_data, v], axis=0)
                except Exception as df_err:
                    logger.error(f"处理股票 {k} 数据时出错: {str(df_err)}")
        except Exception as process_err:
            logger.error(f"处理数据时出错: {str(process_err)}", exc_info=True)
            return []
        
        # 检查是否获取到数据
        if daily_data.empty:
            logger.error("数据处理后为空，无法继续分析")
            return []
        
        logger.info(f"处理后的数据形状: {daily_data.shape}")
        logger.debug(f"数据列: {list(daily_data.columns)}")
        
        try:
            # 排序并获取最新数据
            logger.debug("对数据进行排序...")
            daily_data_sorted = daily_data.sort_values(by=['code', 'time'], ascending=False)
            logger.debug("获取每只股票的最新记录...")
            daily_data_a1 = pd.DataFrame(daily_data.drop_duplicates('code'))
            
            # 记录结果数量
            logger.info(f"去重后的股票数量: {len(daily_data_a1)}")
            
            # 确保必要的列存在
            required_cols = ['lastPrice', 'lastClose', 'amount']
            for col in required_cols:
                if col not in daily_data_a1.columns:
                    logger.error(f"缺少必要的列: {col}")
                    return []
            
            # 添加标志和排名
            logger.debug("计算涨停标志...")
            daily_data_a1['limit_up'] = daily_data_a1.apply(lambda x: (x.lastPrice >= round(x.lastClose * 1.2, 3)) * 1, axis=1)
            logger.debug("计算成交额排名...")
            daily_data_a1['amount_rank'] = daily_data_a1['amount'].rank(ascending=False)
            logger.debug("计算涨幅...")
            daily_data_a1['increase_rate'] = daily_data_a1.apply(lambda x: (x.lastPrice - x.lastClose) / x.lastClose, axis=1)
            logger.debug("计算涨幅排名...")
            daily_data_a1['increase_rate_rank'] = daily_data_a1['increase_rate'].rank(ascending=False)
            
            # 筛选符合条件的股票
            logger.info("筛选符合条件的股票...")
            filtered_stocks = daily_data_a1[(daily_data_a1['limit_up'] == 0) &
                                           (daily_data_a1['lastPrice'] >= 90) &
                                           (daily_data_a1['lastPrice'] <= 500)]
            
            # 记录筛选后的数量
            logger.info(f"筛选后的股票数量: {len(filtered_stocks)}")
            
            # 检查筛选后是否有股票
            if filtered_stocks.empty:
                logger.warning("没有符合条件的股票")
                return []
            
            # 获取成交额最大的10只股票
            logger.info("获取成交额最大的股票...")
            top_stocks = filtered_stocks.sort_values('amount', ascending=False).head(limit)
            
            # 记录选中的股票信息
            logger.info(f"\n选中的股票信息 (共 {len(top_stocks)} 只):")
            for _, row in top_stocks.iterrows():
                formatted_code = format_stock_code_with_name(row['code'])
                logger.info(f"代码: {formatted_code}, 价格: {row['lastPrice']:.2f}, 涨幅: {row['increase_rate']*100:.2f}%, 成交额: {row['amount']/10000:.2f}万")
            
            # 返回股票代码列表
            selected_codes = top_stocks['code'].tolist()
            formatted_codes = [format_stock_code_with_name(code) for code in selected_codes]
            logger.info(f"返回选中的股票代码: {', '.join(formatted_codes)}")
            return selected_codes
            
        except Exception as analysis_err:
            logger.error(f"分析数据时出错: {str(analysis_err)}", exc_info=True)
            return []
    
    except Exception as e:
        logger.error(f"获取股票数据时出错: {str(e)}", exc_info=True)
        return []

def run_backtest(stock_codes, select_only=False, img_dir=None, log_dir=None, target_date=None):
    """运行回测脚本或启动实盘交易
    
    Args:
        stock_codes: 股票代码列表
        select_only: 是否仅执行选股，不启动交易
        img_dir: 图片保存目录，如果不指定则使用默认路径
        log_dir: 日志保存目录，如果不指定则使用默认路径
        target_date: 目标回测日期（格式：YYYYMMDD），用于日志文件命名
    """
    try:
        # 从配置文件中读取是否为实盘模式和目标日期
        config = load_config()
        is_live = config['backtest'].get('is_live', False)
        
        # 确定回测日期：优先使用传入的target_date，否则从配置中提取
        backtest_date = target_date
        if not backtest_date and 'start_time' in config['backtest'] and len(config['backtest']['start_time']) >= 8:
            backtest_date = config['backtest']['start_time'][:8]  # 提取YYYYMMDD部分
        
        # 获取当前使用的配置文件路径（用于传递给run_backtest.py）
        config_path = 'config.yaml'  # 默认配置文件路径
        
        # 从命令行参数中查找 --config-path
        if '--config-path' in sys.argv:
            try:
                config_path_index = sys.argv.index('--config-path')
                if config_path_index + 1 < len(sys.argv):
                    config_path = sys.argv[config_path_index + 1]
                    if 'logger' in globals() and logger is not None:
                        logger.info(f"将使用命令行指定的配置文件进行回测: {config_path}")
            except (ValueError, IndexError) as e:
                if 'logger' in globals() and logger is not None:
                    logger.warning(f"解析配置文件路径参数失败: {str(e)}，使用默认配置文件")
        
        if is_live and not select_only:
            # 实盘模式且非仅选股模式：启动交易管理器
            logger.info("实盘模式：启动交易管理器")
            start_live_trading(stock_codes)
        elif is_live and select_only:
            # 实盘模式但仅选股模式：只发送选股通知，不启动交易
            logger.info("实盘模式（仅选股）：发送选股通知")
            WechatNotifier.send_stock_selection_notification(stock_codes, is_live=True, target_date=backtest_date)
        else:
            # 回测模式：运行回测脚本
            logger.info(f"回测模式：开始运行回测，回测日期: {backtest_date}")
            
            # 构建命令行参数
            cmd = ["python", "run_backtest.py"]
            if img_dir:
                cmd.extend(["--img-dir", img_dir])
            if log_dir:
                cmd.extend(["--log-dir", log_dir])
            if backtest_date:
                cmd.extend(["--backtest-date", backtest_date])
            
            # 【关键修复】传递配置文件路径给run_backtest.py
            if config_path != 'config.yaml':  # 只有当使用非默认配置文件时才传递
                cmd.extend(["--config-path", config_path])
                if 'logger' in globals() and logger is not None:
                    logger.info(f"传递配置文件路径给run_backtest.py: {config_path}")
            
            subprocess.run(cmd, check=True)
            logger.info("回测脚本启动成功")
            
            # 回测完成后运行日志分析（如果启用且不是批量回测模式）
            if config['backtest'].get('enable_log_analysis', True):
                # 使用多种方法检查是否处于批量回测模式
                is_batch_mode = False
                
                try:
                    from batch_metadata import get_metadata_manager
                    
                    metadata_manager = get_metadata_manager()
                    
                    # 方法1：检查是否有正在运行的批量回测会话
                    if metadata_manager.is_batch_session_running():
                        is_batch_mode = True
                        logger.info("检测到正在运行的批量回测会话")
                    
                    # 方法2：检查img_dir路径是否属于批量回测会话
                    if img_dir and metadata_manager.is_path_in_batch_session(img_dir):
                        is_batch_mode = True
                        logger.info(f"检测到路径 {img_dir} 属于批量回测会话")
                        
                except ImportError:
                    logger.warning("无法导入批量回测元数据管理器，使用备用检测方法")
                    # 备用检测方法：检查img_dir路径是否符合批量回测的目录结构
                    if img_dir:
                        path_parts = os.path.normpath(img_dir).split(os.sep)
                        if len(path_parts) >= 3:
                            parent_dir = path_parts[-2]
                            current_dir = path_parts[-1]
                            if (parent_dir.startswith('backtest_') and 
                                len(current_dir) == 8 and current_dir.isdigit()):
                                is_batch_mode = True
                                logger.info(f"通过路径结构检测到批量回测模式: {img_dir}")
                                
                except Exception as e:
                    logger.error(f"检查批量回测模式时出错: {str(e)}")
                    # 发生错误时，使用备用方法
                    if img_dir:
                        path_parts = os.path.normpath(img_dir).split(os.sep)
                        if len(path_parts) >= 3:
                            parent_dir = path_parts[-2]
                            current_dir = path_parts[-1]
                            if (parent_dir.startswith('backtest_') and 
                                len(current_dir) == 8 and current_dir.isdigit()):
                                is_batch_mode = True
                                logger.info(f"通过备用方法检测到批量回测模式: {img_dir}")
                
                # 方法3：额外的批量回测检测逻辑
                if not is_batch_mode and img_dir:
                    # 检查img_dir是否包含results/backtest_时间戳/日期格式的路径
                    path_str = os.path.normpath(img_dir)
                    if 'results' in path_str and 'backtest_' in path_str:
                        path_parts = path_str.split(os.sep)
                        for i, part in enumerate(path_parts):
                            if part.startswith('backtest_') and i + 1 < len(path_parts):
                                next_part = path_parts[i + 1]
                                if len(next_part) == 8 and next_part.isdigit():
                                    is_batch_mode = True
                                    logger.info(f"通过results路径检测到批量回测模式: {img_dir}")
                                    break
                
                # 方法4：通过log_dir检测（如果img_dir检测失败）
                if not is_batch_mode and log_dir and log_dir != img_dir:
                    path_str = os.path.normpath(log_dir)
                    if 'results' in path_str and 'backtest_' in path_str:
                        path_parts = path_str.split(os.sep)
                        for i, part in enumerate(path_parts):
                            if part.startswith('backtest_') and i + 1 < len(path_parts):
                                next_part = path_parts[i + 1]
                                if len(next_part) == 8 and next_part.isdigit():
                                    is_batch_mode = True
                                    logger.info(f"通过log_dir路径检测到批量回测模式: {log_dir}")
                                    break
                
                logger.info(f"批量回测模式检测结果: {is_batch_mode}")
                
                if is_batch_mode:
                    logger.info("当前处于批量回测模式，跳过单独的日志分析")
                else:
                    logger.info("当前为单独回测模式，开始日志分析...")
                    
                    try:
                        # 使用新的集成接口运行日志分析
                        from log_analyzer import run_integrated_analysis
                        
                        # 运行分析
                        output_path = run_integrated_analysis(
                            source_script='select_stocks',
                            results_dir=img_dir if img_dir else None
                        )
                        
                        if output_path:
                            logger.info(f"日志分析完成: {output_path}")
                        else:
                            logger.warning("日志分析未生成文件")
                    except Exception as e:
                        logger.error(f"运行日志分析时出错: {str(e)}")
            else:
                logger.info("日志分析功能已禁用，跳过")
            
            # 回测模式下不发送微信通知（只有实盘模式才发送）
            logger.info("离线回测模式，跳过选股结果微信通知")
            
    except subprocess.CalledProcessError as e:
        logger.error(f"运行回测失败: {str(e)}")
    except Exception as e:
        logger.error(f"运行回测时发生异常: {str(e)}")

def start_live_trading(stock_codes):
    """启动实盘交易
    
    Args:
        stock_codes: 股票代码列表
        
    Returns:
        bool: 启动是否成功
    """
    try:
        # 格式化股票代码显示，包含中文简称
        formatted_codes = [format_stock_code_with_name(code) for code in stock_codes]
        logger.info(f"启动实盘交易，股票列表: {', '.join(formatted_codes)}")
        
        # 导入交易管理器
        from simple_trade_manager import SimpleTradeManager
        
        # 创建交易管理器实例
        manager = SimpleTradeManager()
        
        # 加载配置
        config = load_config()
        initial_cash = config['backtest'].get('initial_cash', 100000)
        
        # 启动每只股票的交易进程
        success_count = 0
        failed_stocks = []
        
        for stock_code in stock_codes:
            formatted_code = format_stock_code_with_name(stock_code)
            logger.info(f"启动股票 {formatted_code} 的交易进程")
            success = manager.add_stock(stock_code, initial_cash)
            if success:
                success_count += 1
                logger.info(f"股票 {formatted_code} 交易进程启动成功")
            else:
                failed_stocks.append(stock_code)
                logger.error(f"股票 {formatted_code} 交易进程启动失败")
        
        # 发送启动完成通知
        notification_content = f"### 🚀 自动选股交易启动完成\n\n"
        notification_content += f"**启动时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        notification_content += f"**选中股票**: {', '.join(formatted_codes)}\n\n"
        notification_content += f"**成功启动**: {success_count} 只股票\n\n"
        if failed_stocks:
            failed_formatted = [format_stock_code_with_name(code) for code in failed_stocks]
            notification_content += f"**失败股票**: {', '.join(failed_formatted)}\n\n"
        active_stocks = manager.get_active_stocks()
        active_formatted = [format_stock_code_with_name(code) for code in active_stocks]
        notification_content += f"**活跃交易**: {', '.join(active_formatted)}\n\n"
        notification_content += f"**管理提示**: 可使用 `python trade_cli.py list` 查看状态"
        
        WechatNotifier.send_notification(notification_content)
        
        logger.info(f"实盘交易启动完成: 成功 {success_count} 只，失败 {len(failed_stocks)} 只")
        logger.info("交易进程已在后台运行，可使用 trade_cli.py 进行管理")
        
        return success_count > 0
        
    except Exception as e:
        error_msg = f"启动实盘交易失败: {str(e)}"
        logger.error(error_msg)
        
        # 发送错误通知
        notification_content = f"### ❌ 自动选股交易启动失败\n\n"
        notification_content += f"**错误时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        formatted_codes = [format_stock_code_with_name(code) for code in stock_codes]
        notification_content += f"**选中股票**: {', '.join(formatted_codes)}\n\n"
        notification_content += f"**错误信息**: {str(e)}"
        
        WechatNotifier.send_notification(notification_content)
        return False

def filter_top_stocks(top_stocks, target_date=None, days=30):
    """过滤选出的股票，只保留在最近N天的缓存中出现次数大于1的股票
    
    Args:
        top_stocks: 选出的股票代码列表
        target_date: 目标日期，格式为YYYYMMDD，不提供则使用当前日期
        days: 检查的天数，默认30天
        
    Returns:
        list: 过滤后的股票代码列表
    """
    # if not top_stocks:
    #     logger.warning("输入的股票列表为空，无需过滤")
    #     return []
        
    # config = load_config()
    # cache_dir = config['backtest'].get('cache_dir', 'select_stocks')
    
    # if not os.path.exists(cache_dir):
    #     logger.warning(f"缓存目录 {cache_dir} 不存在，无法过滤")
    #     return top_stocks
    
    # # 获取基准日期
    # if target_date:
    #     try:
    #         base_date = datetime.datetime.strptime(target_date, '%Y%m%d')
    #     except ValueError:
    #         logger.error(f"无效的目标日期格式: {target_date}，使用当前日期")
    #         base_date = datetime.datetime.now()
    # else:
    #     base_date = datetime.datetime.now()
    
    # logger.info(f"使用基准日期 {base_date.strftime('%Y%m%d')} 进行过滤")
    
    # # 统计每个股票在缓存中出现的次数
    # stock_counts = {code: 0 for code in top_stocks}
    
    # # 遍历最近N天
    # for i in range(1, days+1):
    #     # 计算日期，从基准日期往前推
    #     past_date = (base_date - datetime.timedelta(days=i)).strftime('%Y%m%d')
    #     cache_file = os.path.join(cache_dir, f"stocks_{past_date}.yaml")
        
    #     if os.path.exists(cache_file):
    #         try:
    #             with open(cache_file, 'r', encoding='utf-8') as f:
    #                 cache_data = yaml.safe_load(f)
                
    #             cached_stocks = cache_data.get('stock_codes', [])
                
    #             # 更新计数
    #             for code in top_stocks:
    #                 if code in cached_stocks:
    #                     stock_counts[code] += 1
    #         except Exception as e:
    #             logger.error(f"读取缓存文件 {cache_file} 时出错: {str(e)}")
    
    # logger.info(f"近 {days} 天股票出现次数统计: {stock_counts}")
    
    # # 过滤出出现次数大于1的股票
    # filtered_stocks = [code for code, count in stock_counts.items() if count > 1]
    # 不过滤
    filtered_stocks = top_stocks[2:8]
    logger.info(f"过滤前股票数量: {len(top_stocks)}, 过滤后股票数量: {len(filtered_stocks)}")
    
    return filtered_stocks

def main(target_date=None, select_only=False, img_dir=None, log_dir=None):
    """主函数
    
    Args:
        target_date: 目标日期，格式为YYYYMMDD，不提供则使用当前日期
        select_only: 是否仅执行选股，不启动交易
        img_dir: 图片保存目录，如果不指定则使用默认路径
        log_dir: 日志保存目录，如果不指定则使用默认路径
    """
    # 设置日志，使用自定义日志目录
    global logger
    logger = setup_logger(log_dir)
    
    logger.info("股票筛选程序启动")
    
    try:
        # 获取股票数据管理器并更新基本信息
        stock_manager = get_stock_data_manager()
        logger.info("开始更新股票基本信息...")
        update_success = stock_manager.update_stock_basic_info()
        if update_success:
            logger.info("股票基本信息更新成功")
        else:
            logger.warning("股票基本信息更新失败，将使用数据库中的历史数据")
        
        # 如果未提供目标日期，则使用当前日期
        if not target_date:
            target_date = datetime.datetime.now().strftime('%Y%m%d')
            wait_until_market_open()
        
        # 加载配置
        config = load_config()
        use_stock_cache = config['backtest'].get('use_stock_cache', False)
        
        # 检查是否使用缓存的选股结果
        if use_stock_cache and has_stock_cache(target_date):
            logger.info(f"使用缓存的选股结果，日期: {target_date}")
            top_stocks = load_cached_stocks(target_date)
            
            # 更新配置文件
            if top_stocks:
                # 初始化股票缓存
                stock_manager.initialize_stock_cache(top_stocks)
                
                # 过滤选出的股票
                filtered_stocks = filter_top_stocks(top_stocks, target_date=target_date)
                
                if not filtered_stocks:
                    logger.info("过滤后没有符合条件的股票（在近30天中出现次数>1），跳过本次回测")
                    return
                
                update_config(config, filtered_stocks)
                
                # 运行回测脚本
                run_backtest(filtered_stocks, select_only=select_only, img_dir=img_dir, log_dir=log_dir, target_date=target_date)
                return
            else:
                logger.warning("缓存中未找到有效的选股结果，将重新选股")
        
        # 获取上交所可转债列表
        sh_cb = get_sh_convertible_bonds()
        
        if not sh_cb:
            logger.error("未能获取可转债列表，程序退出")
            return
        
        # 获取成交额最大的20只股票
        top_stocks = get_top_amount_stocks(sh_cb, limit=20, target_date=target_date)
        
        if top_stocks:
            # 初始化股票缓存
            stock_manager.initialize_stock_cache(top_stocks)
            
            # 缓存选股结果
            cache_stocks(top_stocks, target_date)
            
            # 过滤选出的股票
            filtered_stocks = filter_top_stocks(top_stocks, target_date=target_date)
            
            if not filtered_stocks:
                logger.info("过滤后没有符合条件的股票（在近30天中出现次数>1），跳过本次回测")
                return
            
            # 更新配置文件
            update_config(config, filtered_stocks)
            
            # 运行回测脚本
            run_backtest(filtered_stocks, select_only=select_only, img_dir=img_dir, log_dir=log_dir, target_date=target_date)
        else:
            logger.error("未能获取符合条件的股票，程序退出")
    except Exception as e:
        logger.error(f"执行主程序时发生异常: {str(e)}", exc_info=True)
        logger.info("尝试使用上次保存的配置继续运行")
        try:
            # 即使股票选择失败，也尝试用现有配置运行回测
            run_backtest([], select_only=select_only, img_dir=img_dir, log_dir=log_dir, target_date=target_date)
        except Exception as inner_e:
            logger.error(f"尝试使用现有配置运行回测失败: {str(inner_e)}")

if __name__ == "__main__":
    # 确保logs目录存在
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='股票选择和回测程序')
    parser.add_argument('--date', type=str, help='目标日期（格式：YYYYMMDD），不提供则使用当前日期')
    parser.add_argument('--select-only', action='store_true', help='仅执行选股，不启动交易（即使在实盘模式下）')
    parser.add_argument('--img-dir', type=str, help='图片保存目录，如果不指定则使用默认路径')
    parser.add_argument('--log-dir', type=str, help='日志保存目录，如果不指定则使用默认路径')
    parser.add_argument('--config-path', type=str, help='配置文件路径，如果不指定则使用默认的config.yaml')
    parser.add_argument('--no-cache', action='store_true', help='不使用选股缓存，强制重新选股')
    args = parser.parse_args()
    
    main(args.date, args.select_only, args.img_dir, args.log_dir) 