@echo off
chcp 65001 >nul
title 自动选股交易系统

echo.
echo ========================================
echo          自动选股交易系统
echo ========================================
echo.

:: 设置Python解释器路径
set PYTHON_PATH=C:/ProgramData/anaconda3/python.exe

:: 检查Python解释器是否存在
if not exist "%PYTHON_PATH%" (
    echo [错误] 找不到Python解释器
    echo    路径: %PYTHON_PATH%
    echo    请检查Anaconda是否正确安装
    echo.
    pause
    exit /b 1
)

:: 获取当前脚本所在目录
cd /d "%~dp0"

:: 检查auto_trading.py是否存在
if not exist "auto_trading.py" (
    echo [错误] 找不到auto_trading.py文件
    echo    当前目录: %CD%
    echo    请确保脚本在正确的目录中运行
    echo.
    pause
    exit /b 1
)

:: 检查config.yaml是否存在
if not exist "config.yaml" (
    echo [错误] 找不到config.yaml配置文件
    echo    请确保配置文件存在
    echo.
    pause
    exit /b 1
)

echo [成功] 环境检查通过
echo    Python路径: %PYTHON_PATH%
echo    工作目录: %CD%
echo.

:: 显示启动选项
echo 请选择启动模式:
echo.
echo "[1] 完整模式 - 自动选股 + 启动交易（推荐）"
echo "[2] 仅选股模式 - 只执行选股，不启动交易"
echo "[3] 指定日期选股 - 为特定日期执行选股"
echo "[4] 查看交易状态 - 查看当前运行的交易进程"
echo "[5] 停止所有交易 - 停止所有运行中的交易进程"
echo "[6] 退出"
echo.

set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" goto full_mode
if "%choice%"=="2" goto select_only
if "%choice%"=="3" goto date_mode
if "%choice%"=="4" goto check_status
if "%choice%"=="5" goto stop_all
if "%choice%"=="6" goto exit
echo 无效选择，请重新运行脚本
pause
exit /b 1

:full_mode
echo.
echo [启动] 完整模式（推荐）...
echo.
echo 第一步: 执行自动选股...
"%PYTHON_PATH%" auto_trading.py --select-only
if %ERRORLEVEL% NEQ 0 (
    echo [失败] 自动选股失败，无法启动交易进程
    goto end
)
echo.
echo 第二步: 启动交易进程...
"%PYTHON_PATH%" simple_trade_manager.py --init
if %ERRORLEVEL% EQU 0 (
    echo.
    echo [成功] 交易进程启动成功！
    echo.
    echo [提示] 管理命令:
    echo    - 查看活跃股票: python trade_cli.py list
    echo    - 查看股票状态: python trade_cli.py status ^<股票代码^>
    echo    - 停止所有交易: python trade_cli.py stop-all
    echo    - 实时监控: python monitor_trading.py
    echo.
    echo [信息] 当前活跃股票:
    "%PYTHON_PATH%" trade_cli.py list
    echo.
    echo 是否启动实时监控? (y/n)
    set /p monitor_choice=
    if /i "%monitor_choice%"=="y" (
        echo 启动实时监控...
        "%PYTHON_PATH%" monitor_trading.py
    )
) else (
    echo [失败] 交易进程启动失败
)
goto end

:select_only
echo.
echo [启动] 仅选股模式...
echo.
"%PYTHON_PATH%" auto_trading.py --select-only
goto end

:date_mode
echo.
set /p target_date=请输入目标日期 (格式: YYYYMMDD, 如: 20241201): 
if "%target_date%"=="" (
    echo 日期不能为空
    pause
    exit /b 1
)
echo.
echo [执行] 为日期 %target_date% 执行选股...
echo.
"%PYTHON_PATH%" auto_trading.py --date %target_date% --select-only
goto end

:check_status
echo.
echo [查询] 交易状态...
echo.
"%PYTHON_PATH%" trade_cli.py list
echo.
echo [提示] 使用以下命令获取更多信息:
echo    python trade_cli.py status ^<股票代码^>  # 查看具体股票状态
echo    python monitor_trading.py              # 启动实时监控
echo.
pause
goto end

:stop_all
echo.
echo [停止] 所有交易进程...
echo.
"%PYTHON_PATH%" trade_cli.py stop-all
echo.
pause
goto end

:end
echo.
echo ========================================
if %ERRORLEVEL% EQU 0 (
    echo [完成] 程序执行完成
) else (
    echo [错误] 程序执行出错，错误代码: %ERRORLEVEL%
)
echo ========================================
echo.
echo 按任意键退出...
pause >nul

:exit
echo.
echo 再见！
timeout /t 2 >nul
exit /b 0 