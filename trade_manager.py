import multiprocessing as mp
from multiprocessing import Process, Pipe, Manager
import logging
import time
from datetime import datetime
from utils import WechatNotifier
from stock_data_manager import format_stock_code_with_name
import yaml
import os

class TradeManager:
    def __init__(self):
        """初始化交易管理器"""
        # 加载配置
        with open('config.yaml', 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 创建日志目录
        if not os.path.exists(self.config['logging']['log_dir']):
            os.makedirs(self.config['logging']['log_dir'])
        
        # 设置日志
        self.logger = self._setup_logger("trade_manager")
        
        # 存储所有进程和连接
        self.data_processes = {}  # 数据处理进程
        self.trading_processes = {}  # 交易进程
        self.connections = {}  # 进程间连接
        self.results = Manager().dict()  # 共享结果字典
        
        self.logger.info("交易管理器初始化完成")

    def init_from_config(self):
        """从配置文件初始化股票交易进程"""
        try:
            # 检查是否为实盘交易模式
            if not self.config['backtest']['is_live']:
                self.logger.warning("配置文件中设置为非实盘模式，跳过初始化")
                return False
            
            stock_codes = self.config['backtest']['stock_codes']
            initial_cash = self.config['backtest']['initial_cash']
            
            if not stock_codes:
                self.logger.warning("配置文件中没有股票代码，跳过初始化")
                return False
            
            formatted_codes = [format_stock_code_with_name(code) for code in stock_codes]
            self.logger.info(f"开始从配置文件初始化股票交易进程: {', '.join(formatted_codes)}")
            
            success_count = 0
            failed_stocks = []
            
            for stock_code in stock_codes:
                formatted_code = format_stock_code_with_name(stock_code)
                self.logger.info(f"正在初始化股票 {formatted_code}")
                success = self.add_stock(stock_code, initial_cash)
                if success:
                    success_count += 1
                    self.logger.info(f"股票 {formatted_code} 初始化成功")
                else:
                    failed_stocks.append(stock_code)
                    self.logger.error(f"股票 {formatted_code} 初始化失败")
            
            # 发送初始化完成通知
            notification_content = f"### 📊 交易系统初始化完成\n\n"
            notification_content += f"**初始化时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            notification_content += f"**成功启动**: {success_count} 只股票\n\n"
            if failed_stocks:
                failed_formatted = [format_stock_code_with_name(code) for code in failed_stocks]
                notification_content += f"**失败股票**: {', '.join(failed_formatted)}\n\n"
            active_stocks = self.get_active_stocks()
            active_formatted = [format_stock_code_with_name(code) for code in active_stocks]
            notification_content += f"**活跃股票列表**: {', '.join(active_formatted)}"
            
            WechatNotifier.send_notification(notification_content)
            
            self.logger.info(f"初始化完成: 成功 {success_count} 只，失败 {len(failed_stocks)} 只")
            return success_count > 0
            
        except Exception as e:
            error_msg = f"从配置文件初始化失败: {str(e)}"
            self.logger.error(error_msg)
            
            # 发送错误通知
            notification_content = f"### ❌ 交易系统初始化失败\n\n"
            notification_content += f"**错误时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            notification_content += f"**错误信息**: {str(e)}"
            
            WechatNotifier.send_notification(notification_content)
            return False

    def _setup_logger(self, name):
        """设置日志"""
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, self.config['logging']['level']))
        
        # 清除已有的handlers，避免重复
        if logger.handlers:
            logger.handlers.clear()
        
        # 添加文件处理器
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f'{self.config["logging"]["log_dir"]}/trade_manager_{timestamp}.log'
        fh = logging.FileHandler(log_filename, encoding='utf-8')
        fh.setLevel(getattr(logging, self.config['logging']['file_level']))
        
        # 添加控制台处理器
        ch = logging.StreamHandler()
        ch.setLevel(getattr(logging, self.config['logging']['console_level']))
        
        # 创建formatter
        file_formatter = logging.Formatter(
            self.config['logging']['file_format'],
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_formatter = logging.Formatter(
            self.config['logging']['console_format'],
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        fh.setFormatter(file_formatter)
        ch.setFormatter(console_formatter)
        
        # 添加handlers
        logger.addHandler(fh)
        logger.addHandler(ch)
        
        # 防止日志向上传播，避免重复输出
        logger.propagate = False
        
        return logger

    def add_stock(self, stock_code, initial_cash=None):
        """
        添加新的股票交易进程
        
        参数:
        stock_code: str, 股票代码
        initial_cash: float, 初始资金，如果为None则使用配置文件中的值
        """
        formatted_code = format_stock_code_with_name(stock_code)
        if stock_code in self.trading_processes:
            self.logger.warning(f"股票 {formatted_code} 已经在交易中")
            return False
        
        try:
            # 创建进程间连接
            parent_conn, child_conn = Pipe()
            self.connections[stock_code] = (parent_conn, child_conn)
            
            # 启动数据处理进程
            from run_backtest import live_stock_handler
            data_proc = Process(target=live_stock_handler, args=(stock_code, parent_conn))
            data_proc.daemon = True
            data_proc.start()
            self.data_processes[stock_code] = data_proc
            
            # 启动交易进程
            from run_backtest import trade_process_func
            initial_cash = initial_cash or self.config['backtest']['initial_cash']
            trade_proc = Process(target=trade_process_func, 
                               args=(stock_code, child_conn, initial_cash, self.results))
            trade_proc.daemon = True
            trade_proc.start()
            self.trading_processes[stock_code] = trade_proc
            
            self.logger.info(f"成功添加股票 {formatted_code} 的交易进程")
            
            # 发送企业微信通知
            notification_content = f"### ✅ 新增股票交易通知\n\n"
            notification_content += f"**股票代码**: {formatted_code}\n\n"
            notification_content += f"**添加时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            notification_content += f"**初始资金**: {initial_cash:.2f}"
            
            WechatNotifier.send_notification(notification_content)
            
            return True
            
        except Exception as e:
            error_msg = f"添加股票 {formatted_code} 失败: {str(e)}"
            self.logger.error(error_msg)
            
            # 发送企业微信通知
            notification_content = f"### ❌ 添加股票失败通知\n\n"
            notification_content += f"**股票代码**: {formatted_code}\n\n"
            notification_content += f"**错误时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            notification_content += f"**错误信息**: {str(e)}"
            
            WechatNotifier.send_notification(notification_content)
            
            # 清理资源
            self._cleanup_stock(stock_code)
            return False

    def remove_stock(self, stock_code):
        """
        停止并移除股票交易进程
        
        参数:
        stock_code: str, 股票代码
        """
        formatted_code = format_stock_code_with_name(stock_code)
        if stock_code not in self.trading_processes:
            self.logger.warning(f"股票 {formatted_code} 不在交易中")
            return False
        
        try:
            # 停止交易进程
            if stock_code in self.trading_processes:
                self.trading_processes[stock_code].terminate()
                self.trading_processes[stock_code].join()
                del self.trading_processes[stock_code]
            
            # 停止数据处理进程
            if stock_code in self.data_processes:
                self.data_processes[stock_code].terminate()
                self.data_processes[stock_code].join()
                del self.data_processes[stock_code]
            
            # 关闭连接
            if stock_code in self.connections:
                parent_conn, child_conn = self.connections[stock_code]
                parent_conn.close()
                child_conn.close()
                del self.connections[stock_code]
            
            self.logger.info(f"成功移除股票 {formatted_code} 的交易进程")
            
            # 发送企业微信通知
            notification_content = f"### 🛑 停止股票交易通知\n\n"
            notification_content += f"**股票代码**: {formatted_code}\n\n"
            notification_content += f"**停止时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            WechatNotifier.send_notification(notification_content)
            
            return True
            
        except Exception as e:
            error_msg = f"移除股票 {formatted_code} 失败: {str(e)}"
            self.logger.error(error_msg)
            
            # 发送企业微信通知
            notification_content = f"### ❌ 停止股票失败通知\n\n"
            notification_content += f"**股票代码**: {formatted_code}\n\n"
            notification_content += f"**错误时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            notification_content += f"**错误信息**: {str(e)}"
            
            WechatNotifier.send_notification(notification_content)
            
            return False

    def restart_stock(self, stock_code, initial_cash=None):
        """
        重启指定股票的交易进程
        
        参数:
        stock_code: str, 股票代码
        initial_cash: float, 初始资金，如果为None则使用配置文件中的值
        
        返回:
        bool: 重启是否成功
        """
        try:
            formatted_code = format_stock_code_with_name(stock_code)
            self.logger.info(f"开始重启股票 {formatted_code} 的交易进程")
            
            # 检查股票是否在交易中
            is_trading = stock_code in self.trading_processes
            
            if is_trading:
                # 先停止现有进程
                self.logger.info(f"正在停止股票 {formatted_code} 的现有交易进程...")
                remove_success = self.remove_stock(stock_code)
                if not remove_success:
                    self.logger.warning(f"停止股票 {formatted_code} 的交易进程时出现问题，但继续尝试启动新进程")
                else:
                    self.logger.info(f"成功停止股票 {formatted_code} 的交易进程")
                
                # 等待一段时间确保进程完全停止
                time.sleep(2)
            else:
                self.logger.info(f"股票 {formatted_code} 当前未在交易中，直接启动新进程")
            
            # 启动新进程
            self.logger.info(f"正在启动股票 {formatted_code} 的新交易进程...")
            add_success = self.add_stock(stock_code, initial_cash)
            
            if add_success:
                self.logger.info(f"成功重启股票 {formatted_code} 的交易进程")
                
                # 发送企业微信通知
                notification_content = f"### 🔄 股票交易进程重启通知\n\n"
                notification_content += f"**股票代码**: {formatted_code}\n\n"
                notification_content += f"**重启时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                if initial_cash:
                    notification_content += f"**初始资金**: {initial_cash:.2f}\n\n"
                notification_content += f"**操作状态**: 重启成功"
                
                WechatNotifier.send_notification(notification_content)
                return True
            else:
                self.logger.error(f"重启股票 {formatted_code} 失败：无法启动新的交易进程")
                
                # 发送失败通知
                notification_content = f"### ❌ 股票交易进程重启失败\n\n"
                notification_content += f"**股票代码**: {formatted_code}\n\n"
                notification_content += f"**重启时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                notification_content += f"**失败原因**: 无法启动新的交易进程"
                
                WechatNotifier.send_notification(notification_content)
                return False
                
        except Exception as e:
            formatted_code = format_stock_code_with_name(stock_code)
            error_msg = f"重启股票 {formatted_code} 的交易进程失败: {str(e)}"
            self.logger.error(error_msg)
            
            # 发送异常通知
            notification_content = f"### ❌ 股票交易进程重启异常\n\n"
            notification_content += f"**股票代码**: {formatted_code}\n\n"
            notification_content += f"**重启时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            notification_content += f"**异常信息**: {str(e)}"
            
            WechatNotifier.send_notification(notification_content)
            return False

    def _cleanup_stock(self, stock_code):
        """清理单个股票的所有资源"""
        try:
            if stock_code in self.trading_processes:
                self.trading_processes[stock_code].terminate()
                self.trading_processes[stock_code].join()
                del self.trading_processes[stock_code]
            
            if stock_code in self.data_processes:
                self.data_processes[stock_code].terminate()
                self.data_processes[stock_code].join()
                del self.data_processes[stock_code]
            
            if stock_code in self.connections:
                parent_conn, child_conn = self.connections[stock_code]
                parent_conn.close()
                child_conn.close()
                del self.connections[stock_code]
        except Exception as e:
            self.logger.error(f"清理股票 {stock_code} 资源时出错: {str(e)}")

    def get_active_stocks(self):
        """获取当前正在交易的股票列表"""
        return list(self.trading_processes.keys())

    def get_stock_status(self, stock_code):
        """
        获取指定股票的交易状态
        
        返回:
        dict: 包含股票交易状态的字典
        """
        if stock_code not in self.trading_processes:
            return {"status": "not_trading"}
        
        status = {
            "status": "trading",
            "data_process_alive": self.data_processes[stock_code].is_alive(),
            "trading_process_alive": self.trading_processes[stock_code].is_alive(),
            "result": self.results.get(stock_code, None)
        }
        
        return status

    def cleanup(self):
        """清理所有资源"""
        for stock_code in list(self.trading_processes.keys()):
            self.remove_stock(stock_code)
        
        self.logger.info("所有交易进程已清理完成") 