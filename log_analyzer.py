import os
import re
import pandas as pd
import logging
from datetime import datetime
import argparse
from pathlib import Path
import yaml
from typing import List, Dict, Optional, Tuple
import glob

def load_config():
    """加载配置文件"""
    try:
        with open('config.yaml', 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception:
        return {"backtest": {"enable_plot_save": True}}

CONFIG = load_config()

# 配置日志
def setup_analyzer_logger():
    """配置日志收集分析器的日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_analyzer_logger()

class TradeLogAnalyzer:
    """交易日志分析器"""
    
    def __init__(self):
        """初始化日志分析器"""
        self.logger = setup_analyzer_logger()
        self.trades = []
        self.pending_signals = {}  # 存储待匹配的卖出信号
        
    def parse_log_file(self, log_file_path: str) -> List[Dict]:
        """解析单个日志文件，提取交易信息"""
        trades = []
        
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            self.logger.info(f"开始解析日志文件: {log_file_path}, 总行数: {len(lines)}")
            
            # 改进的日志文件类型检测
            log_content = ''.join(lines)
            
            # 检查是否包含真实的信号信息
            has_signal_markers = (
                '[SIGNAL]' in log_content or 
                '触发买入信号' in log_content or 
                '触发卖出信号' in log_content or
                '触发止盈信号' in log_content or
                '触发固定止损信号' in log_content or
                '触发追踪止损信号' in log_content
            )
            
            # 检查是否只是交易明细汇总（包含交易明细但没有信号）
            has_trade_summary = '=== 交易明细 ===' in log_content
            
            self.logger.info(f"文件分析结果: 包含信号标记={has_signal_markers}, 包含交易明细={has_trade_summary}")
            
            # 如果只有交易明细汇总但没有真实信号，跳过这个文件
            if has_trade_summary and not has_signal_markers:
                self.logger.info(f"跳过回测汇总文件（无信号记录）: {log_file_path}")
                return []
            
            # 统计信号数量（用于调试）
            signal_count = log_content.count('[SIGNAL]')
            buy_signal_count = log_content.count('触发买入信号')
            sell_signal_count = (log_content.count('触发卖出信号') + 
                               log_content.count('触发止盈信号') + 
                               log_content.count('触发固定止损信号') + 
                               log_content.count('触发追踪止损信号'))
            self.logger.info(f"信号统计: [SIGNAL]标记={signal_count}, 买入信号={buy_signal_count}, 卖出信号={sell_signal_count}")
            
            # 更精确的回测汇总日志检测：
            # 1. 文件名包含 "backtest_" 且包含 "交易明细"
            # 2. 或者包含特定的回测汇总格式（时间: YYYY-MM-DD HH:MM:SS, 类型: BUY/SELL）
            # 3. 同时必须包含真实的信号信息
            filename = os.path.basename(log_file_path)
            is_backtest_summary = (
                ('backtest_' in filename and '=== 交易明细 ===' in log_content and has_signal_markers) or
                (re.search(r'时间: \d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}, 类型: (BUY|SELL)', log_content) and 
                 '=== 交易明细 ===' in log_content and
                 has_signal_markers)  # 必须包含真实信号
            )
            
            if is_backtest_summary:
                self.logger.info(f"检测到回测汇总日志: {log_file_path}")
                trades = self._parse_backtest_summary_log(lines, log_file_path)
            elif has_signal_markers:
                self.logger.info(f"检测到策略实时日志: {log_file_path}")
                # 原有的策略实时日志格式
                trades = self._parse_strategy_realtime_log(lines, log_file_path)
            else:
                self.logger.info(f"跳过无信号记录的日志文件: {log_file_path}")
                return []
                
            self.logger.info(f"文件解析完成: {log_file_path}, 提取到 {len(trades)} 条交易记录")
                
        except Exception as e:
            self.logger.error(f"解析日志文件 {log_file_path} 时出错: {str(e)}")
        
        return trades

    def _parse_backtest_summary_log(self, lines: List[str], log_file_path: str) -> List[Dict]:
        """解析回测汇总日志"""
        trades = []
        
        # 提取股票代码
        stock_code = self._extract_stock_code_from_filename(log_file_path)
        
        # 首先尝试从日志中提取信号信息
        buy_signals_map = {}
        sell_signals_map = {}
        
        # 查找信号部分（跳过交易明细汇总部分）
        skip_trade_summary = False
        for i, line in enumerate(lines):
            # 检查是否进入交易明细汇总部分，如果是则跳过
            if '=== 交易明细 ===' in line:
                skip_trade_summary = True
                self.logger.info("跳过交易明细汇总部分，只收集实际信号触发记录")
                continue
            
            # 如果在交易明细汇总部分，且行内容匹配汇总格式，则跳过
            if skip_trade_summary:
                # 检查是否是汇总格式的交易记录
                trade_summary_match = re.search(
                    r'时间: (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}), 类型: (BUY|SELL), 价格: ([\d.]+), 数量: (-?\d+), 收益率: ([-\d.]+)%',
                    line
                )
                if trade_summary_match:
                    # 这是汇总格式的交易记录，跳过
                    continue
                else:
                    # 不再是汇总格式，可能是其他内容，恢复正常处理
                    skip_trade_summary = False
            
            # 查找买入信号 - 修正格式匹配
            buy_signal_match = re.search(
                r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},?\d* - INFO - (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - \[SIGNAL\] 触发买入信号[:：]\s*(.+)',
                line
            )
            if buy_signal_match:
                timestamp_str = buy_signal_match.group(1)
                signal_reason = buy_signal_match.group(2).strip()
                buy_signals_map[timestamp_str] = signal_reason
                self.logger.debug(f"找到回测汇总买入信号: {timestamp_str} -> {signal_reason}")
                continue
                
            # 查找卖出信号 - 修正格式匹配
            sell_signal_match = re.search(
                r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},?\d* - INFO - (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - \[SIGNAL\] 触发(卖出信号|止盈信号|固定止损信号|追踪止损信号)[:：]\s*(.+)',
                line
            )
            if sell_signal_match:
                timestamp_str = sell_signal_match.group(1)
                signal_type = sell_signal_match.group(2)
                signal_reason = sell_signal_match.group(3).strip()
                
                # 根据信号类型调整信号原因的描述
                if signal_type == "止盈信号":
                    signal_reason = f"止盈, {signal_reason}"
                elif signal_type == "固定止损信号":
                    signal_reason = f"固定止损, {signal_reason}"
                elif signal_type == "追踪止损信号":
                    signal_reason = f"追踪止损, {signal_reason}"
                # 对于"卖出信号"，保持原样
                
                # 移除收益率和数量信息，只保留信号原因的核心部分
                signal_reason = re.sub(r',\s*(收益率|价格)[:：].*', '', signal_reason)
                sell_signals_map[timestamp_str] = signal_reason
                self.logger.debug(f"找到回测汇总卖出信号: {timestamp_str} -> {signal_reason}")
                continue
        
        # 现在处理实际的订单执行记录（BUY/SELL 订单执行）
        buy_records = []
        
        for i, line in enumerate(lines):
            # 跳过交易明细汇总部分
            if '=== 交易明细 ===' in line:
                continue
            
            # 检查是否是汇总格式的交易记录，如果是则跳过
            trade_summary_match = re.search(
                r'时间: (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}), 类型: (BUY|SELL), 价格: ([\d.]+), 数量: (-?\d+), 收益率: ([-\d.]+)%',
                line
            )
            if trade_summary_match:
                continue
            
            # 解析实际的BUY订单执行记录
            buy_order_match = re.search(
                r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},?\d* - INFO - (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - BUY 订单执行, 股票[:：]\s*(.+?),\s*价格[:：]\s*([\d.]+),\s*数量[:：]\s*(\d+),\s*成本[:：]\s*([\d.]+),\s*手续费[:：]\s*([\d.]+)',
                line
            )
            if buy_order_match:
                timestamp_str = buy_order_match.group(1)
                stock_info = buy_order_match.group(2).strip()
                price = float(buy_order_match.group(3))
                quantity = int(buy_order_match.group(4))
                cost = float(buy_order_match.group(5))
                commission = float(buy_order_match.group(6))
                
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                
                # 查找对应的买入信号，使用时间范围匹配（前后5分钟内）
                buy_signal = ''
                target_time = timestamp
                
                for signal_time_str, signal_reason in buy_signals_map.items():
                    signal_time = datetime.strptime(signal_time_str, '%Y-%m-%d %H:%M:%S')
                    # 允许信号时间在交易时间前后5分钟内
                    time_diff = abs((target_time - signal_time).total_seconds())
                    if time_diff <= 300:  # 5分钟 = 300秒
                        buy_signal = signal_reason
                        self.logger.debug(f"匹配买入信号: 交易时间={timestamp_str}, 信号时间={signal_time_str}, 差异={time_diff}秒")
                        break
                
                # 存储买入记录
                buy_records.append({
                    'timestamp': timestamp,
                    'price': price,
                    'quantity': abs(quantity),
                    'cost': cost,
                    'commission': commission,
                    'buy_signal': buy_signal
                })
                continue
            
            # 解析实际的SELL订单执行记录
            sell_order_match = re.search(
                r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},?\d* - INFO - (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - SELL 订单执行, 股票[:：]\s*(.+?),\s*价格[:：]\s*([\d.]+),\s*数量[:：]\s*(-?\d+),\s*成本[:：]\s*([\d.]+),\s*手续费[:：]\s*([\d.]+)',
                line
            )
            if sell_order_match and buy_records:
                timestamp_str = sell_order_match.group(1)
                stock_info = sell_order_match.group(2).strip()
                price = float(sell_order_match.group(3))
                quantity = int(sell_order_match.group(4))
                sell_cost = float(sell_order_match.group(5))
                sell_commission = float(sell_order_match.group(6))
                
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                
                # 匹配最近的买入记录
                buy_record = buy_records.pop(0) if buy_records else None
                
                if buy_record:
                    # 查找对应的卖出信号，使用时间范围匹配（前后5分钟内）
                    sell_signal = ''
                    target_time = timestamp
                    
                    for signal_time_str, signal_reason in sell_signals_map.items():
                        signal_time = datetime.strptime(signal_time_str, '%Y-%m-%d %H:%M:%S')
                        # 允许信号时间在交易时间前后5分钟内
                        time_diff = abs((target_time - signal_time).total_seconds())
                        if time_diff <= 300:  # 5分钟 = 300秒
                            sell_signal = signal_reason
                            self.logger.debug(f"匹配卖出信号: 交易时间={timestamp_str}, 信号时间={signal_time_str}, 差异={time_diff}秒")
                            break
                    
                    # 计算盈亏
                    buy_cost = buy_record['cost']
                    sell_amount = price * abs(quantity)
                    profit_loss = sell_amount - buy_cost - buy_record['commission'] - sell_commission
                    profit_rate = (profit_loss / buy_cost * 100) if buy_cost > 0 else 0.0
                    
                    # 根据信号情况确定信号类型
                    if buy_record['buy_signal'] or sell_signal:
                        signal_type = '完整交易'
                    else:
                        signal_type = '交易记录'  # 没有信号的交易记录
                    
                    trade_record = {
                        'timestamp': buy_record['timestamp'],
                        'stock_code': stock_code,
                        'buy_signal': buy_record['buy_signal'] if buy_record['buy_signal'] else '无记录',
                        'buy_price': buy_record['price'],
                        'buy_quantity': buy_record['quantity'],
                        'buy_cost': buy_cost,
                        'buy_commission': buy_record['commission'],
                        'sell_signal': sell_signal if sell_signal else '无记录',
                        'sell_price': price,
                        'sell_quantity': abs(quantity),
                        'profit_loss': profit_loss,
                        'profit_rate': profit_rate,
                        'all_sell_signals': [{'reason': sell_signal, 'timestamp': timestamp_str}] if sell_signal else [],
                        'sell_attempts': 1,
                        'execution_status': '已执行',
                        'signal_type': signal_type
                    }
                    trades.append(trade_record)
        
        self.logger.info(f"从回测汇总日志解析出 {len(trades)} 条交易记录")
        if buy_signals_map:
            self.logger.info(f"提取到 {len(buy_signals_map)} 个买入信号")
        elif trades:
            self.logger.info("未找到明确的买入信号记录，交易可能来自策略内部逻辑")
        if sell_signals_map:
            self.logger.info(f"提取到 {len(sell_signals_map)} 个卖出信号")
        elif trades:
            self.logger.info("未找到明确的卖出信号记录，交易可能来自策略内部逻辑")
        
        return trades

    def _extract_stock_code_from_filename(self, log_file_path: str) -> str:
        """从文件名中提取股票代码"""
        filename = os.path.basename(log_file_path)
        # 匹配文件名中的股票代码格式: backtest_127081.SZ_20250601_115534.log
        match = re.search(r'backtest_([^_]+\.[A-Z]{2})_', filename)
        if match:
            return match.group(1)
        
        # 匹配策略日志文件名格式: strategy_127081.SZ_20250530_20250601_164420.log
        match = re.search(r'strategy_([^_]+\.[A-Z]{2})_', filename)
        if match:
            return match.group(1)
        
        # 如果没有匹配到，尝试其他格式
        match = re.search(r'(\d{6}\.[A-Z]{2})', filename)
        if match:
            return match.group(1)
        
        return '未知股票'

    def _extract_target_date_from_filename(self, log_file_path: str) -> str:
        """从日志文件名中提取回测目标日期"""
        filename = os.path.basename(log_file_path)
        
        # 策略日志格式: strategy_STOCKCODE_TARGETDATE_SYSTEMDATE_TIME.log
        # 例如: strategy_127081.SZ_20250530_20250601_164420.log
        # 第一个日期(20250530)是回测目标日期，第二个日期时间(20250601_164420)是系统运行时间
        match = re.search(r'strategy_[^_]+\.[A-Z]{2}_(\d{8})_\d{8}_\d{6}\.log', filename)
        if match:
            target_date = match.group(1)
            self.logger.debug(f"从策略日志文件名 {filename} 提取目标日期: {target_date}")
            return target_date
            
        # 回测日志格式: backtest_STOCKCODE_TARGETDATE_SYSTEMDATE_TIME.log  
        match = re.search(r'backtest_[^_]+\.[A-Z]{2}_(\d{8})_\d{8}_\d{6}\.log', filename)
        if match:
            target_date = match.group(1)
            self.logger.debug(f"从回测日志文件名 {filename} 提取目标日期: {target_date}")
            return target_date
            
        # 旧格式或其他格式：尝试提取第一个日期
        match = re.search(r'(\d{8})', filename)
        if match:
            target_date = match.group(1)
            self.logger.debug(f"从日志文件名 {filename} 提取日期: {target_date}")
            return target_date
            
        return None

    def _parse_strategy_realtime_log(self, lines: List[str], log_file_path: str = None) -> List[Dict]:
        """解析策略实时日志（原有逻辑）"""
        trades = []
        
        # 从文件路径提取目标日期（如果提供）
        target_date = None
        if log_file_path:
            target_date = self._extract_target_date_from_filename(log_file_path)
        
        # 第一步：收集所有买入信号
        buy_signals = self._collect_all_buy_signals(lines)
        
        # 第二步：收集所有买入订单执行
        buy_executions = self._collect_all_buy_executions(lines)
        
        # 第三步：处理已执行的交易
        executed_signals = set()  # 记录已处理的信号
        
        for execution in buy_executions:
            # 查找对应的交易结束记录
            trade_end = self._find_next_trade_end(lines, execution['line_number'])
            
            if trade_end:
                # 找到与此执行最匹配的买入信号
                matching_signal = self._find_matching_buy_signal(buy_signals, execution)
                if matching_signal:
                    executed_signals.add(matching_signal['line_number'])
                
                # 从交易结束记录往前找最近的卖出信号
                sell_info = self._find_matching_sell_signal(lines, execution['line_number'], trade_end['end_line'])
                
                # 收集所有卖出信号
                all_sell_signals = self._collect_all_sell_signals(lines, execution['line_number'], trade_end['end_line'])
                
                trade_record = {
                    'timestamp': execution['timestamp'],
                    'stock_code': execution['stock_code'],
                    'buy_signal': matching_signal.get('reason', '') if matching_signal else '',
                    'buy_price': execution['price'],
                    'buy_quantity': execution['quantity'],
                    'buy_cost': execution['cost'],
                    'buy_commission': execution['commission'],
                    'sell_signal': sell_info.get('reason', '') if sell_info else '',
                    'sell_price': trade_end.get('sell_price', 0.0),
                    'sell_quantity': trade_end.get('sell_quantity', 0),
                    'profit_loss': trade_end.get('net_pnl', 0.0),
                    'profit_rate': trade_end.get('net_pnl_pct', 0.0),
                    'all_sell_signals': all_sell_signals,  # 记录所有卖出信号
                    'sell_attempts': len(all_sell_signals),  # 卖出尝试次数
                    'execution_status': '已执行',  # 新增：执行状态
                    'signal_type': '完整交易',  # 新增：信号类型
                    'target_date': target_date  # 新增：目标日期
                }
                trades.append(trade_record)
                self.logger.debug(f"找到已执行交易记录: {trade_record}")
        
        # 第四步：处理未执行的买入信号
        for signal in buy_signals:
            if signal['line_number'] not in executed_signals:
                # 这是一个未执行的买入信号
                unexecuted_record = {
                    'timestamp': signal['timestamp'],
                    'stock_code': self._extract_stock_code_from_signal(signal, lines),
                    'buy_signal': signal['reason'],
                    'buy_price': 0.0,
                    'buy_quantity': 0,
                    'buy_cost': 0.0,
                    'buy_commission': 0.0,
                    'sell_signal': '',
                    'sell_price': 0.0,
                    'sell_quantity': 0,
                    'profit_loss': 0.0,
                    'profit_rate': 0.0,
                    'all_sell_signals': [],
                    'sell_attempts': 0,
                    'execution_status': '未执行',  # 执行状态
                    'signal_type': '仅信号',  # 信号类型
                    'target_date': target_date  # 新增：目标日期
                }
                trades.append(unexecuted_record)
                self.logger.debug(f"找到未执行信号记录: {unexecuted_record}")
        
        return trades

    def _collect_all_buy_signals(self, lines: List[str]) -> List[Dict]:
        """收集所有买入信号"""
        buy_signals = []
        
        for i, line in enumerate(lines):
            # 修正：匹配实际的日志格式
            # 实际格式：2025-06-01 16:16:40,177 - INFO - 2025-05-30 10:00:51 - [SIGNAL] 触发买入信号: MACD零轴上金叉, ROC连续增长, 成交量突破
            buy_signal_match = re.search(
                r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},?\d* - INFO - (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - \[SIGNAL\] 触发买入信号[:：]\s*(.+)', 
                line
            )
            if buy_signal_match:
                signal_info = {
                    'timestamp': datetime.strptime(buy_signal_match.group(1), '%Y-%m-%d %H:%M:%S'),
                    'reason': buy_signal_match.group(2).strip(),
                    'line_number': i
                }
                buy_signals.append(signal_info)
                self.logger.debug(f"找到买入信号: {signal_info}")
        
        self.logger.info(f"总共找到 {len(buy_signals)} 个买入信号")
        return buy_signals

    def _collect_all_buy_executions(self, lines: List[str]) -> List[Dict]:
        """收集所有买入订单执行"""
        buy_executions = []
        
        for i, line in enumerate(lines):
            # 修正：匹配实际的日志格式
            # 实际格式：2025-06-01 16:16:40,xxx - INFO - 2025-05-30 10:00:51 - BUY 订单执行, 股票: xxx, 价格: xxx, 数量: xxx, 成本: xxx, 手续费: xxx
            buy_order_match = re.search(
                r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},?\d* - INFO - (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - BUY 订单执行, 股票[:：]\s*(.+?),\s*价格[:：]\s*([\d.]+),\s*数量[:：]\s*(\d+),\s*成本[:：]\s*([\d.]+),\s*手续费[:：]\s*([\d.]+)',
                line
            )
            if buy_order_match:
                execution_info = {
                    'timestamp': datetime.strptime(buy_order_match.group(1), '%Y-%m-%d %H:%M:%S'),
                    'stock_code': buy_order_match.group(2).strip(),
                    'price': float(buy_order_match.group(3)),
                    'quantity': int(buy_order_match.group(4)),
                    'cost': float(buy_order_match.group(5)),
                    'commission': float(buy_order_match.group(6)),
                    'line_number': i
                }
                buy_executions.append(execution_info)
                self.logger.debug(f"找到买入执行: {execution_info}")
        
        self.logger.info(f"总共找到 {len(buy_executions)} 个买入执行")
        return buy_executions

    def _find_matching_buy_signal(self, buy_signals: List[Dict], execution: Dict) -> Optional[Dict]:
        """为买入执行找到最匹配的买入信号"""
        # 找到执行时间前最近的买入信号
        matching_signals = [
            signal for signal in buy_signals 
            if signal['timestamp'] <= execution['timestamp']
        ]
        
        if matching_signals:
            # 返回时间最近的信号
            return max(matching_signals, key=lambda x: x['timestamp'])
        
        return None

    def _extract_stock_code_from_signal(self, signal: Dict, lines: List[str]) -> str:
        """从信号附近的日志中提取股票代码"""
        # 在信号前后几行内查找股票代码相关信息
        start_line = max(0, signal['line_number'] - 10)
        end_line = min(len(lines), signal['line_number'] + 20)
        
        for i in range(start_line, end_line):
            # 查找创建买入订单的记录
            order_match = re.search(r'股票: (.+?),|股票代码: (.+?)[,\s]', lines[i])
            if order_match:
                return order_match.group(1) or order_match.group(2)
            
            # 查找其他包含股票代码的记录
            code_match = re.search(r'(\d{6}\.[A-Z]{2})', lines[i])
            if code_match:
                return code_match.group(1)
        
        return '未知股票'

    def _find_recent_buy_signal(self, lines: List[str], buy_order_line: int) -> Optional[Dict]:
        """从买入订单执行往前查找最近的买入信号"""
        for i in range(buy_order_line - 1, max(0, buy_order_line - 20), -1):
            # 修正：提取实际交易时间而不是日志记录时间
            buy_signal_match = re.search(
                r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},?\d* - INFO - (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - \[SIGNAL\] 触发买入信号: (.+)', 
                lines[i]
            )
            if buy_signal_match:
                return {
                    'timestamp': buy_signal_match.group(1),
                    'reason': buy_signal_match.group(2)
                }
        return None

    def _find_matching_sell_signal(self, lines: List[str], start_line: int, end_line: int) -> Optional[Dict]:
        """从交易结束往前找最近的卖出信号"""
        # 从交易结束位置往前查找最近的卖出信号
        for i in range(end_line - 1, start_line, -1):
            # 匹配新格式的[SIGNAL]卖出信号（各种类型）
            sell_signal_match = re.search(
                r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},?\d* - INFO - (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - \[SIGNAL\] 触发(卖出信号|止盈信号|固定止损信号|追踪止损信号)[:：]\s*([^,]+)(?:,\s*收益率[:：]\s*([-\d.]+)%)?(?:,\s*数量[:：]\s*(\d+))?',
                lines[i]
            )
            if sell_signal_match:
                signal_type = sell_signal_match.group(2)
                signal_content = sell_signal_match.group(3).strip()
                return_rate_str = sell_signal_match.group(4)
                quantity_str = sell_signal_match.group(5)
                
                # 根据信号类型调整信号原因的描述
                if signal_type == "止盈信号":
                    reason = f"止盈, {signal_content}"
                elif signal_type == "固定止损信号":
                    reason = f"固定止损, {signal_content}"
                elif signal_type == "追踪止损信号":
                    reason = f"追踪止损, {signal_content}"
                else:  # 卖出信号
                    reason = signal_content
                
                result = {
                    'reason': reason,
                    'line_number': i
                }
                
                # 如果有收益率和数量信息，添加到结果中
                if return_rate_str:
                    result['return_rate'] = float(return_rate_str)
                if quantity_str:
                    result['quantity'] = int(quantity_str)
                
                return result
            
            # 匹配旧格式的卖出信号（兼容性）
            old_sell_signal_match = re.search(
                r'触发卖出信号: (.+?), 收益率: ([-\d.]+)%, 数量: (\d+)',
                lines[i]
            )
            if old_sell_signal_match:
                return {
                    'reason': old_sell_signal_match.group(1),
                    'return_rate': float(old_sell_signal_match.group(2)),
                    'quantity': int(old_sell_signal_match.group(3)),
                    'line_number': i
                }
            
            # 匹配其他卖出触发条件（兼容性）
            other_sell_match = re.search(
                r'触发(追踪止损|固定止损|止盈), .*?价格: ([\d.]+), .*?数量: (\d+)',
                lines[i]
            )
            if other_sell_match:
                return {
                    'reason': other_sell_match.group(1),
                    'price': float(other_sell_match.group(2)),
                    'quantity': int(other_sell_match.group(3)),
                    'line_number': i
                }
        return None

    def _collect_all_sell_signals(self, lines: List[str], start_line: int, end_line: int) -> List[Dict]:
        """收集买入和卖出之间的所有卖出信号"""
        sell_signals = []
        
        for i in range(start_line, end_line):
            # 修正：匹配实际的卖出信号格式（包括各种类型的卖出信号）
            # 实际格式：2025-06-01 16:16:40,181 - INFO - 2025-05-30 10:05:12 - [SIGNAL] 触发卖出信号: MACD死叉, 收益率: -0.04%, 数量: 180, 估计收益: -7.02
            # 新格式：[SIGNAL] 触发止盈信号: 收益率 x.xx%, 数量: xxx
            # 新格式：[SIGNAL] 触发固定止损信号: 收益率 x.xx%, 数量: xxx  
            # 新格式：[SIGNAL] 触发追踪止损信号: 价格 x.xx, 止损价: x.xx, 数量: xxx
            sell_signal_match = re.search(
                r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},?\d* - INFO - (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - \[SIGNAL\] 触发(卖出信号|止盈信号|固定止损信号|追踪止损信号)[:：]\s*([^,]+)(?:,\s*收益率[:：]\s*([-\d.]+)%)?(?:,\s*数量[:：]\s*(\d+))?',
                lines[i]
            )
            if sell_signal_match:
                timestamp_str = sell_signal_match.group(1)
                signal_type = sell_signal_match.group(2)
                signal_content = sell_signal_match.group(3).strip()
                return_rate_str = sell_signal_match.group(4)
                quantity_str = sell_signal_match.group(5)
                
                # 根据信号类型调整信号原因的描述
                if signal_type == "止盈信号":
                    reason = f"止盈, {signal_content}"
                elif signal_type == "固定止损信号":
                    reason = f"固定止损, {signal_content}"
                elif signal_type == "追踪止损信号":
                    reason = f"追踪止损, {signal_content}"
                else:  # 卖出信号
                    reason = signal_content
                
                signal_info = {
                    'timestamp': timestamp_str,
                    'reason': reason,
                    'line_number': i
                }
                
                # 如果有收益率和数量信息，添加到信号信息中
                if return_rate_str:
                    signal_info['return_rate'] = float(return_rate_str)
                if quantity_str:
                    signal_info['quantity'] = int(quantity_str)
                
                sell_signals.append(signal_info)
                self.logger.debug(f"找到卖出信号: {signal_info}")
                continue
            
            # 保留原有的其他卖出触发条件匹配（兼容性）
            other_sell_match = re.search(
                r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},?\d* - INFO - (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - 触发(追踪止损|固定止损|止盈|止损).*?价格[:：]\s*([\d.]+).*?数量[:：]\s*(\d+)',
                lines[i]
            )
            if other_sell_match:
                signal_info = {
                    'timestamp': other_sell_match.group(1),
                    'reason': other_sell_match.group(2),
                    'price': float(other_sell_match.group(3)),
                    'quantity': int(other_sell_match.group(4)),
                    'line_number': i
                }
                sell_signals.append(signal_info)
                self.logger.debug(f"找到其他卖出信号: {signal_info}")
        
        self.logger.info(f"在行 {start_line}-{end_line} 之间找到 {len(sell_signals)} 个卖出信号")
        return sell_signals

    def _find_next_trade_end(self, lines: List[str], start_line: int) -> Optional[Dict]:
        """从指定行开始查找下一个交易结束记录"""
        for i in range(start_line, min(start_line + 50, len(lines))):
            # 修正交易结束的正则表达式
            trade_end_match = re.search(
                r'交易结束\. 毛收益: ([-\d.]+) \(([-\d.]+)%\), 净收益: ([-\d.]+) \(([-\d.]+)%\)',
                lines[i]
            )
            if trade_end_match:
                # 找到交易结束记录后，往前找对应的SELL订单执行
                sell_order_info = self._find_recent_sell_order(lines, i)
                
                return {
                    'gross_pnl': float(trade_end_match.group(1)),
                    'gross_pnl_pct': float(trade_end_match.group(2)),
                    'net_pnl': float(trade_end_match.group(3)),
                    'net_pnl_pct': float(trade_end_match.group(4)),
                    'sell_price': sell_order_info.get('price', 0.0) if sell_order_info else 0.0,
                    'sell_quantity': sell_order_info.get('quantity', 0) if sell_order_info else 0,
                    'end_line': i  # 记录交易结束的行号
                }
        return None

    def _find_recent_sell_order(self, lines: List[str], trade_end_line: int) -> Optional[Dict]:
        """从交易结束记录往前找最近的SELL订单执行"""
        for i in range(trade_end_line - 1, max(0, trade_end_line - 10), -1):
            sell_order_match = re.search(
                r'SELL 订单执行, 股票: (.+?), 价格: ([\d.]+), 数量: (-?\d+)',
                lines[i]
            )
            if sell_order_match:
                return {
                    'stock_code': sell_order_match.group(1),
                    'price': float(sell_order_match.group(2)),
                    'quantity': abs(int(sell_order_match.group(3)))  # 取绝对值
                }
        return None

    def _find_next_sell_signal(self, lines: List[str], start_line: int) -> Optional[Dict]:
        """从指定行开始查找下一个卖出信号（保留兼容性）"""
        for i in range(start_line, min(start_line + 50, len(lines))):
            # 匹配卖出信号
            sell_signal_match = re.search(
                r'触发卖出信号: (.+?), 收益率: ([-\d.]+)%, 数量: (\d+)',
                lines[i]
            )
            if sell_signal_match:
                return {
                    'reason': sell_signal_match.group(1),
                    'return_rate': float(sell_signal_match.group(2)),
                    'quantity': int(sell_signal_match.group(3))
                }
            
            # 匹配其他卖出触发条件（如追踪止损、固定止损等）
            other_sell_match = re.search(
                r'触发(追踪止损|固定止损|止盈), .*?价格: ([\d.]+), .*?数量: (\d+)',
                lines[i]
            )
            if other_sell_match:
                return {
                    'reason': other_sell_match.group(1),
                    'price': float(other_sell_match.group(2)),
                    'quantity': int(other_sell_match.group(3))
                }
            
            # 匹配SELL订单执行（作为备选方案）
            sell_order_match = re.search(
                r'SELL 订单执行, 股票: .+?, 价格: ([\d.]+), 数量: (-?\d+)',
                lines[i]
            )
            if sell_order_match:
                return {
                    'reason': '卖出订单执行',
                    'price': float(sell_order_match.group(1)),
                    'quantity': abs(int(sell_order_match.group(2)))  # 卖出数量可能为负数，取绝对值
                }
        return None
    
    def analyze_logs(self, log_paths: List[str]) -> pd.DataFrame:
        """分析多个日志文件"""
        all_trades = []
        
        for log_path in log_paths:
            self.logger.info(f"开始解析日志文件: {log_path}")
            trades = self.parse_log_file(log_path)
            all_trades.extend(trades)
            self.logger.info(f"解析完成，共找到 {len(trades)} 条交易记录")
        
        if not all_trades:
            self.logger.warning("未找到任何交易记录")
            return pd.DataFrame()
        
        # 转换为DataFrame
        df = pd.DataFrame(all_trades)
        
        # 确保时间列为datetime类型
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 排序
        df = df.sort_values(['stock_code', 'timestamp'])
        
        # 重置索引
        df = df.reset_index(drop=True)
        
        self.logger.info(f"总共找到 {len(df)} 条交易记录，涉及 {df['stock_code'].nunique()} 只股票")
        
        return df
    
    def save_to_excel(self, df: pd.DataFrame, output_path: str):
        """保存分析结果到Excel文件，包含多个工作表"""
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 主数据表 - 包含所有交易记录
                df.to_excel(writer, sheet_name='交易记录', index=False)
                
                # 只对已执行的交易进行统计分析
                executed_df = df[df['execution_status'] == '已执行'].copy()
                
                if not executed_df.empty:
                    # 股票统计表
                    stock_stats = self._calculate_stock_statistics(executed_df)
                    stock_stats.to_excel(writer, sheet_name='股票统计', index=False)
                    
                    # 日期统计表
                    daily_stats = self._calculate_daily_statistics(executed_df)
                    daily_stats.to_excel(writer, sheet_name='日期统计', index=False)
                else:
                    # 如果没有已执行的交易，创建空的统计表
                    empty_df = pd.DataFrame({'说明': ['暂无已执行的交易记录']})
                    empty_df.to_excel(writer, sheet_name='股票统计', index=False)
                    empty_df.to_excel(writer, sheet_name='日期统计', index=False)
                
                # 信号分析表 - 包含所有信号（已执行和未执行）
                signal_stats = self._calculate_signal_analysis(df)
                signal_stats.to_excel(writer, sheet_name='信号分析', index=False)
                
                # 执行统计表 - 新增：执行状态统计
                execution_stats = self._calculate_execution_statistics(df)
                execution_stats.to_excel(writer, sheet_name='执行统计', index=False)
                
                # 格式化工作表
                for sheet_name in writer.sheets:
                    worksheet = writer.sheets[sheet_name]
                    
                    # 自动调整列宽
                    for column in worksheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter
                        for cell in column:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass
                        adjusted_width = min(max_length + 2, 50)
                        worksheet.column_dimensions[column_letter].width = adjusted_width
            
            self.logger.info(f"分析结果已保存到: {output_path}")
            
        except Exception as e:
            self.logger.error(f"保存Excel文件时出错: {str(e)}")

    def _calculate_execution_statistics(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算执行统计数据"""
        try:
            stats_data = []
            
            # 总体执行统计
            total_signals = len(df)
            executed_trades = len(df[df['execution_status'] == '已执行'])
            unexecuted_signals = len(df[df['execution_status'] == '未执行'])
            execution_rate = (executed_trades / total_signals * 100) if total_signals > 0 else 0
            
            stats_data.append({
                '统计项目': '总体执行情况',
                '总信号数': total_signals,
                '已执行': executed_trades,
                '未执行': unexecuted_signals,
                '执行率(%)': round(execution_rate, 2),
                '说明': f'总共{total_signals}个信号，执行{executed_trades}个，未执行{unexecuted_signals}个'
            })
            
            # 按股票分组的执行统计
            if not df.empty:
                stock_execution = df.groupby('stock_code').agg({
                    'execution_status': ['count', lambda x: sum(x == '已执行'), lambda x: sum(x == '未执行')]
                }).round(2)
                
                # 重命名列
                stock_execution.columns = ['总信号数', '已执行', '未执行']
                stock_execution['执行率(%)'] = (stock_execution['已执行'] / stock_execution['总信号数'] * 100).round(2)
                stock_execution = stock_execution.reset_index()
                
                # 添加股票级别统计
                for _, row in stock_execution.iterrows():
                    stats_data.append({
                        '统计项目': f'股票: {row["stock_code"]}',
                        '总信号数': int(row['总信号数']),
                        '已执行': int(row['已执行']),
                        '未执行': int(row['未执行']),
                        '执行率(%)': row['执行率(%)'],
                        '说明': f'该股票信号执行情况'
                    })
            
            # 按信号类型统计
            if '买入信号' in df.columns:
                signal_type_stats = df.groupby(['buy_signal', 'execution_status']).size().unstack(fill_value=0)
                if '已执行' in signal_type_stats.columns and '未执行' in signal_type_stats.columns:
                    signal_type_stats['总数'] = signal_type_stats['已执行'] + signal_type_stats['未执行']
                    signal_type_stats['执行率(%)'] = (signal_type_stats['已执行'] / signal_type_stats['总数'] * 100).round(2)
                    
                    # 添加信号类型统计
                    for signal_type, row in signal_type_stats.iterrows():
                        if signal_type:  # 跳过空的信号类型
                            stats_data.append({
                                '统计项目': f'信号: {signal_type}',
                                '总信号数': int(row.get('总数', 0)),
                                '已执行': int(row.get('已执行', 0)),
                                '未执行': int(row.get('未执行', 0)),
                                '执行率(%)': row.get('执行率(%)', 0),
                                '说明': f'该类型信号的执行情况'
                            })
            
            return pd.DataFrame(stats_data)
            
        except Exception as e:
            self.logger.error(f"计算执行统计时出错: {str(e)}")
            return pd.DataFrame({'错误': [f'计算执行统计时出错: {str(e)}']})

    def _calculate_signal_analysis(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算信号分析数据，包含所有信号类型"""
        try:
            signal_stats = []
            
            # 只对已执行的交易进行盈亏分析
            executed_df = df[df['execution_status'] == '已执行'].copy()
            
            # 按买入信号分组分析
            if not df.empty and 'buy_signal' in df.columns:
                signal_groups = df.groupby('buy_signal')
                
                for signal_type, group in signal_groups:
                    if not signal_type or pd.isna(signal_type):  # 跳过空的信号类型
                        continue
                    
                    # 基本统计
                    total_signals = len(group)
                    executed_count = len(group[group['execution_status'] == '已执行'])
                    unexecuted_count = len(group[group['execution_status'] == '未执行'])
                    execution_rate = (executed_count / total_signals * 100) if total_signals > 0 else 0
                    
                    # 对已执行的交易进行盈亏分析
                    executed_group = group[group['execution_status'] == '已执行']
                    
                    if len(executed_group) > 0:
                        profit_trades = len(executed_group[executed_group['profit_rate'] > 0])
                        win_rate = (profit_trades / len(executed_group) * 100) if len(executed_group) > 0 else 0
                        avg_profit_rate = executed_group['profit_rate'].mean()
                        max_profit_rate = executed_group['profit_rate'].max()
                        min_profit_rate = executed_group['profit_rate'].min()
                        total_profit = executed_group['profit_loss'].sum()
                    else:
                        profit_trades = 0
                        win_rate = 0
                        avg_profit_rate = 0
                        max_profit_rate = 0
                        min_profit_rate = 0
                        total_profit = 0
                    
                    signal_stats.append({
                        '买入信号': signal_type,
                        '总信号数': total_signals,
                        '已执行': executed_count,
                        '未执行': unexecuted_count,
                        '执行率(%)': round(execution_rate, 2),
                        '盈利次数': profit_trades,
                        '胜率(%)': round(win_rate, 2),
                        '平均收益率(%)': round(avg_profit_rate, 2),
                        '最大收益率(%)': round(max_profit_rate, 2),
                        '最小收益率(%)': round(min_profit_rate, 2),
                        '总盈亏': round(total_profit, 2)
                    })
            
            # 按卖出信号分组分析（只针对已执行的交易）
            if not executed_df.empty and 'sell_signal' in executed_df.columns:
                sell_signal_groups = executed_df.groupby('sell_signal')
                
                sell_signal_stats = []
                for signal_type, group in sell_signal_groups:
                    if not signal_type or pd.isna(signal_type):  # 跳过空的信号类型
                        continue
                    
                    profit_trades = len(group[group['profit_rate'] > 0])
                    win_rate = (profit_trades / len(group) * 100) if len(group) > 0 else 0
                    avg_profit_rate = group['profit_rate'].mean()
                    
                    sell_signal_stats.append({
                        '卖出信号': signal_type,
                        '触发次数': len(group),
                        '盈利次数': profit_trades,
                        '胜率(%)': round(win_rate, 2),
                        '平均收益率(%)': round(avg_profit_rate, 2),
                        '总盈亏': round(group['profit_loss'].sum(), 2)
                    })
                
                # 将卖出信号统计添加到结果中
                if sell_signal_stats:
                    sell_df = pd.DataFrame(sell_signal_stats)
                    # 在买入信号分析后添加一个分隔行，然后添加卖出信号分析
                    separator = pd.DataFrame({'买入信号': ['--- 卖出信号分析 ---'], 
                                            '总信号数': [''], '已执行': [''], '未执行': [''], '执行率(%)': [''],
                                            '盈利次数': [''], '胜率(%)': [''], '平均收益率(%)': [''], 
                                            '最大收益率(%)': [''], '最小收益率(%)': [''], '总盈亏': ['']})
                    
                    # 将卖出信号数据重命名列以匹配主表结构
                    sell_df_renamed = pd.DataFrame({
                        '买入信号': sell_df['卖出信号'],
                        '总信号数': sell_df['触发次数'],
                        '已执行': sell_df['触发次数'],  # 卖出信号都是已执行的
                        '未执行': [0] * len(sell_df),
                        '执行率(%)': [100] * len(sell_df),
                        '盈利次数': sell_df['盈利次数'],
                        '胜率(%)': sell_df['胜率(%)'],
                        '平均收益率(%)': sell_df['平均收益率(%)'],
                        '最大收益率(%)': [''] * len(sell_df),
                        '最小收益率(%)': [''] * len(sell_df),
                        '总盈亏': sell_df['总盈亏']
                    })
                    
                    signal_stats_df = pd.DataFrame(signal_stats)
                    result_df = pd.concat([signal_stats_df, separator, sell_df_renamed], ignore_index=True)
                    return result_df
            
            return pd.DataFrame(signal_stats)
            
        except Exception as e:
            self.logger.error(f"计算信号分析时出错: {str(e)}")
            return pd.DataFrame({'错误': [f'计算信号分析时出错: {str(e)}']})

    def _calculate_stock_statistics(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算按股票分组的统计信息"""
        stock_stats = []
        
        for stock_code in df['stock_code'].unique():
            stock_df = df[df['stock_code'] == stock_code]
            
            # 统计信息
            stats = {
                '股票代码': stock_code,
                '交易次数': len(stock_df),
                '平均买入价': stock_df['buy_price'].mean(),
                '平均卖出价': stock_df['sell_price'].mean() if stock_df['sell_price'].sum() > 0 else 0,
                '总毛收益': stock_df['profit_loss'].sum(),  # 这里用profit_loss代表净收益
                '总净收益': stock_df['profit_loss'].sum(),
                '平均收益率': stock_df['profit_rate'].mean(),
                '最大收益率': stock_df['profit_rate'].max(),
                '最小收益率': stock_df['profit_rate'].min(),
                '盈利次数': len(stock_df[stock_df['profit_loss'] > 0]),
                '亏损次数': len(stock_df[stock_df['profit_loss'] <= 0]),
                '胜率': len(stock_df[stock_df['profit_loss'] > 0]) / len(stock_df) * 100 if len(stock_df) > 0 else 0
            }
            stock_stats.append(stats)
        
        return pd.DataFrame(stock_stats)

    def _calculate_daily_statistics(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算按日期分组的统计信息"""
        daily_stats = []
        
        # 按日期分组
        df['date'] = df['timestamp'].dt.date
        
        for date in df['date'].unique():
            date_df = df[df['date'] == date]
            
            # 统计信息
            stats = {
                '日期': date,
                '交易次数': len(date_df),
                '总毛收益': date_df['profit_loss'].sum(),
                '总净收益': date_df['profit_loss'].sum(),
                '平均收益率': date_df['profit_rate'].mean(),
                '最大收益率': date_df['profit_rate'].max(),
                '最小收益率': date_df['profit_rate'].min(),
                '盈利次数': len(date_df[date_df['profit_loss'] > 0]),
                '亏损次数': len(date_df[date_df['profit_loss'] <= 0]),
                '胜率': len(date_df[date_df['profit_loss'] > 0]) / len(date_df) * 100 if len(date_df) > 0 else 0,
                '股票数量': date_df['stock_code'].nunique()
            }
            daily_stats.append(stats)
        
        return pd.DataFrame(daily_stats).sort_values('日期')

def find_latest_strategy_logs(logs_dir: str = "logs", stock_code: str = None, 
                             date_filter: str = None, time_range: Tuple[str, str] = None) -> List[str]:
    """查找最新的策略日志文件"""
    log_files = []
    
    # 构建搜索模式 - 支持新的命名格式，包括KDJ专用日志
    patterns = []
    if date_filter:
        # 新命名格式：component_stockcode_backtest_date_current_datetime.log
        if stock_code:
            # 查找特定股票的策略日志
            patterns.extend([
                f"{logs_dir}/strategy_{stock_code}_{date_filter}_*.log",
                f"{logs_dir}/backtest_{stock_code}_{date_filter}_*.log",
                f"{logs_dir}/kdj_optimized_{stock_code}_{date_filter}_*.log"
            ])
        else:
            # 查找所有股票的策略日志
            patterns.extend([
                f"{logs_dir}/strategy_*_{date_filter}_*.log",
                f"{logs_dir}/backtest_*_{date_filter}_*.log",
                f"{logs_dir}/batch_*_{date_filter}_*.log",
                f"{logs_dir}/main_{date_filter}_*.log",
                f"{logs_dir}/kdj_optimized_*_{date_filter}_*.log"
            ])
        
        # 也保留对旧格式的兼容性
        patterns.extend([
            f"{logs_dir}/strategy_{stock_code}_*.log" if stock_code else f"{logs_dir}/strategy_*.log",
            f"{logs_dir}/backtest_{stock_code}_*.log" if stock_code else f"{logs_dir}/backtest_*.log",
            f"{logs_dir}/kdj_optimized_{stock_code}_*.log" if stock_code else f"{logs_dir}/kdj_optimized_*.log"
        ])
    else:
        # 没有日期过滤器时，使用通用模式
        if stock_code:
            patterns.extend([
                f"{logs_dir}/strategy_{stock_code}_*.log",
                f"{logs_dir}/backtest_{stock_code}_*.log",
                f"{logs_dir}/kdj_optimized_{stock_code}_*.log"
            ])
        else:
            patterns.extend([
                f"{logs_dir}/strategy_*.log",
                f"{logs_dir}/backtest_*.log",
                f"{logs_dir}/batch_*.log",
                f"{logs_dir}/main_*.log",
                f"{logs_dir}/kdj_optimized_*.log"
            ])
    
    # 获取所有匹配的日志文件
    all_files = []
    for pattern in patterns:
        matched_files = glob.glob(pattern)
        logger.debug(f"模式 '{pattern}' 匹配到 {len(matched_files)} 个文件")
        all_files.extend(matched_files)
    
    # 去重
    all_files = list(set(all_files))
    
    if date_filter:
        # 精确过滤：优先使用新命名规则匹配的文件
        priority_files = []
        fallback_files = []
        
        for file in all_files:
            basename = os.path.basename(file)
            
            # 检查新命名规则：component_stockcode_backtest_date_current_datetime.log
            # 格式示例：strategy_127081.SZ_20250507_20250601_143022.log
            new_format_match = re.search(r'(strategy|backtest|batch|main|kdj_optimized)_([^_]+_)?(\d{8})_\d{8}_\d{6}\.log$', basename)
            if new_format_match and new_format_match.group(3) == date_filter:
                priority_files.append(file)
                logger.debug(f"新格式匹配: {basename}")
                continue
            
            # 检查简化的新命名规则：component_backtest_date_current_datetime.log
            # 格式示例：main_20250507_20250601_143022.log
            simple_new_format_match = re.search(r'(strategy|backtest|batch|main|kdj_optimized)_(\d{8})_\d{8}_\d{6}\.log$', basename)
            if simple_new_format_match and simple_new_format_match.group(2) == date_filter:
                priority_files.append(file)
                logger.debug(f"新格式（简化）匹配: {basename}")
                continue
            
            # 检查KDJ专用日志格式：kdj_optimized_stockcode_datetime.log
            # 格式示例：kdj_optimized_128144.SZ_20250604_140901.log
            kdj_format_match = re.search(r'kdj_optimized_([^_]+\.[A-Z]{2})_(\d{8})_\d{6}\.log$', basename)
            if kdj_format_match:
                # 这里我们需要从文件内容或其他方式判断日期，因为KDJ文件名中的日期是创建时间而不是回测日期
                # 暂时先加入到fallback_files中，后续可以进一步优化
                fallback_files.append(file)
                logger.debug(f"KDJ格式匹配: {basename}")
                continue
            
            # 检查旧命名规则中是否包含日期
            if date_filter in basename:
                # 验证是否真的匹配日期（避免误匹配）
                date_in_filename = re.search(r'(\d{8})', basename)
                if date_in_filename and date_in_filename.group(1) == date_filter:
                    fallback_files.append(file)
                    logger.debug(f"旧格式匹配: {basename}")
        
        # 优先使用新格式的文件，如果没有则使用旧格式
        if priority_files:
            all_files = priority_files
            logger.info(f"使用新格式日志文件，找到 {len(priority_files)} 个文件")
        elif fallback_files:
            all_files = fallback_files
            logger.info(f"使用旧格式日志文件，找到 {len(fallback_files)} 个文件")
        else:
            logger.warning(f"未找到日期 {date_filter} 的日志文件")
            all_files = []
    
    if time_range:
        # 按时间范围过滤
        start_time, end_time = time_range
        filtered_files = []
        for file in all_files:
            # 从文件名提取时间戳
            basename = os.path.basename(file)
            # 新格式：查找最后的日期时间部分
            time_match = re.search(r'(\d{8}_\d{6})\.log$', basename)
            if time_match:
                file_time = time_match.group(1)
                if start_time <= file_time <= end_time:
                    filtered_files.append(file)
        all_files = filtered_files
    
    if not all_files:
        logger.warning(f"在 {logs_dir} 目录下未找到符合条件的策略日志文件")
        if date_filter:
            logger.info(f"提示：确保日志文件命名包含日期 {date_filter}")
            logger.info(f"新格式示例：strategy_127081.SZ_{date_filter}_20250601_143022.log")
        return []
    
    # 按修改时间排序，获取最新的文件
    all_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    
    # 如果指定了日期过滤器，返回所有匹配的文件
    # 如果没有指定股票代码且没有日期过滤器，默认返回最新的10个文件
    if not date_filter and not stock_code:
        all_files = all_files[:10]
    
    logger.info(f"找到 {len(all_files)} 个策略日志文件")
    for file in all_files:
        logger.debug(f"  - {file}")
    
    return all_files

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='策略交易日志分析工具')
    parser.add_argument('--stock-code', type=str, help='指定股票代码')
    parser.add_argument('--date', type=str, help='指定日期（格式：YYYYMMDD）')
    parser.add_argument('--time-range', nargs=2, help='指定时间范围（格式：YYYYMMDD_HHMMSS YYYYMMDD_HHMMSS）')
    parser.add_argument('--logs-dir', type=str, default='logs', help='日志目录路径')
    parser.add_argument('--output-dir', type=str, help='输出目录路径')
    parser.add_argument('--output-name', type=str, help='输出文件名（不含扩展名）')
    
    args = parser.parse_args()
    
    # 检查是否启用日志收集功能
    if not CONFIG['backtest'].get('enable_log_analysis', True):
        logger.info("日志收集功能已禁用（enable_log_analysis: false），退出")
        return
    
    # 查找日志文件
    time_range = tuple(args.time_range) if args.time_range else None
    log_files = find_latest_strategy_logs(
        logs_dir=args.logs_dir,
        stock_code=args.stock_code,
        date_filter=args.date,
        time_range=time_range
    )
    
    if not log_files:
        logger.error("未找到符合条件的日志文件")
        return
    
    # 创建分析器
    analyzer = TradeLogAnalyzer()
    
    # 分析日志
    df = analyzer.analyze_logs(log_files)
    
    if df.empty:
        logger.warning("未找到任何交易记录")
        return
    
    # 确定输出路径
    if args.output_dir:
        output_dir = args.output_dir
    else:
        output_dir = 'logs'
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 确定输出文件名
    if args.output_name:
        output_filename = f"{args.output_name}.xlsx"
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if args.stock_code:
            output_filename = f"trade_analysis_{args.stock_code}_{timestamp}.xlsx"
        else:
            output_filename = f"trade_analysis_{timestamp}.xlsx"
    
    output_path = os.path.join(output_dir, output_filename)
    
    # 保存结果
    analyzer.save_to_excel(df, output_path)
    
    # 输出统计信息
    total_trades = len(df)
    profitable_trades = len(df[df['profit_loss'] > 0])
    total_pnl = df['profit_loss'].sum()
    
    logger.info(f"\n=== 分析结果统计 ===")
    logger.info(f"总交易次数: {total_trades}")
    logger.info(f"盈利交易次数: {profitable_trades}")
    logger.info(f"胜率: {profitable_trades/total_trades*100:.2f}%" if total_trades > 0 else "胜率: 0.00%")
    logger.info(f"总净收益: {total_pnl:.2f}")
    logger.info(f"平均单笔收益: {total_pnl/total_trades:.2f}" if total_trades > 0 else "平均单笔收益: 0.00")
    logger.info(f"已保存到: {output_path}")

def run_integrated_analysis(
    source_script: str, 
    results_dir: str = None, 
    date_filter: str = None,
    stock_code: str = None,
    time_range: Tuple[str, str] = None
) -> str:
    """
    集成模式运行日志分析
    
    参数:
    source_script: str, 调用的源脚本名称（'run_batch_backtest', 'run_backtest', 'select_stocks'）
    results_dir: str, 结果保存目录，如果为None则使用默认目录
    date_filter: str, 日期过滤器，格式为YYYYMMDD
    stock_code: str, 股票代码过滤器
    time_range: Tuple[str, str], 时间范围过滤器
    
    返回:
    str: 生成的Excel文件路径
    """
    # 检查配置是否启用日志分析
    config = load_config()
    if not config['backtest'].get('enable_log_analysis', True):
        logger.info("日志分析功能已禁用，跳过分析")
        return None
    
    # 增强的批量回测模式检测
    if source_script != 'run_batch_backtest' and results_dir:
        # 检测是否处于批量回测模式
        is_batch_mode = False
        
        try:
            from batch_metadata import get_metadata_manager
            
            metadata_manager = get_metadata_manager()
            
            # 方法1：检查是否有正在运行的批量回测会话
            if metadata_manager.is_batch_session_running():
                is_batch_mode = True
                logger.info("检测到正在运行的批量回测会话，跳过集成日志分析")
            
            # 方法2：检查results_dir路径是否属于批量回测会话
            if not is_batch_mode and metadata_manager.is_path_in_batch_session(results_dir):
                is_batch_mode = True
                logger.info(f"检测到路径 {results_dir} 属于批量回测会话，跳过集成日志分析")
                
        except ImportError:
            logger.debug("无法导入批量回测元数据管理器，使用备用检测方法")
        except Exception as e:
            logger.debug(f"检查批量回测模式时出错: {str(e)}")
        
        # 备用检测方法：检查results_dir路径是否符合批量回测的目录结构
        if not is_batch_mode and results_dir:
            path_str = os.path.normpath(results_dir)
            
            # 检查是否包含批量回测的典型路径模式
            if 'results' in path_str and 'backtest_' in path_str:
                path_parts = path_str.split(os.sep)
                for i, part in enumerate(path_parts):
                    if part.startswith('backtest_') and i + 1 < len(path_parts):
                        next_part = path_parts[i + 1]
                        if len(next_part) == 8 and next_part.isdigit():
                            is_batch_mode = True
                            logger.info(f"通过路径结构检测到批量回测模式，跳过集成日志分析: {results_dir}")
                            break
        
        # 如果检测到批量回测模式，直接返回
        if is_batch_mode:
            logger.info("当前处于批量回测模式，跳过集成日志分析（避免重复生成）")
            return None
    
    logger.info(f"开始集成日志分析，调用脚本: {source_script}")
    
    # 确定输出目录和文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    if source_script == 'run_batch_backtest' and results_dir:
        # 批量回测模式：保存到对应的results目录
        output_dir = results_dir
        output_name = f'trade_analysis_batch_{timestamp}'
    else:
        # 其他模式：保存到专用的日志分析目录
        if not results_dir:
            results_dir = 'results'
        output_dir = os.path.join(results_dir, 'logs_analysis')
        
        # 确保目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 根据调用脚本生成文件名
        script_names = {
            'run_backtest': 'single_backtest',
            'select_stocks': 'stock_selection',
            'standalone': 'manual_analysis'
        }
        script_suffix = script_names.get(source_script, source_script)
        
        if date_filter:
            output_name = f'trade_analysis_{script_suffix}_{date_filter}_{timestamp}'
        else:
            output_name = f'trade_analysis_{script_suffix}_{timestamp}'
    
    # 查找日志文件
    if source_script == 'run_batch_backtest' and results_dir:
        # 批量回测模式：在results_dir中查找日志文件
        # 注意：批量回测中日志文件使用系统时间而不是目标回测日期，所以不使用日期过滤器
        if os.path.exists(results_dir):
            log_files = find_latest_strategy_logs(
                logs_dir=results_dir,
                stock_code=stock_code,
                date_filter=None,  # 不使用日期过滤器
                time_range=time_range
            )
        else:
            log_files = []
        
        # 如果在results_dir中没找到，再尝试默认的logs目录
        if not log_files:
            logger.warning(f"在结果目录 {results_dir} 中未找到日志文件，尝试在默认logs目录查找")
            log_files = find_latest_strategy_logs(
                logs_dir="logs",
                stock_code=stock_code,
                date_filter=date_filter,  # 在默认目录中可以使用日期过滤器
                time_range=time_range
            )
    else:
        # 其他模式：在默认logs目录查找
        log_files = find_latest_strategy_logs(
            logs_dir="logs",
            stock_code=stock_code,
            date_filter=date_filter,
            time_range=time_range
        )
    
    if not log_files:
        logger.warning("未找到匹配的日志文件")
        return None
    
    logger.info(f"找到 {len(log_files)} 个日志文件")
    
    # 创建分析器并执行分析
    analyzer = TradeLogAnalyzer()
    
    # 分析日志
    df = analyzer.analyze_logs(log_files)
    
    if df.empty:
        logger.warning("未找到交易记录")
        return None
    
    # 生成输出文件路径
    output_path = os.path.join(output_dir, f'{output_name}.xlsx')
    
    # 保存结果
    analyzer.save_to_excel(df, output_path)
    
    # 输出统计信息
    executed_df = df[df['execution_status'] == '已执行'] if 'execution_status' in df.columns else df
    
    if not executed_df.empty:
        total_trades = len(executed_df)
        profit_trades = len(executed_df[executed_df['profit_loss'] > 0])
        win_rate = (profit_trades / total_trades * 100) if total_trades > 0 else 0
        total_profit = executed_df['profit_loss'].sum()
        avg_profit = total_profit / total_trades if total_trades > 0 else 0
        
        logger.info("\n=== 集成分析结果统计 ===")
        logger.info(f"总交易次数: {total_trades}")
        logger.info(f"盈利交易次数: {profit_trades}")
        logger.info(f"胜率: {win_rate:.2f}%")
        logger.info(f"总净收益: {total_profit:.2f}")
        logger.info(f"平均单笔收益: {avg_profit:.2f}")
        logger.info(f"已保存到: {output_path}")
    
    return output_path

def run_daily_analysis_for_batch(results_dir: str, date_filter: str = None) -> Dict[str, str]:
    """
    为批量回测运行每日分析
    
    参数:
    results_dir: str, 批量回测的结果目录
    date_filter: str, 日期过滤器，如果为None则分析所有日期目录
    
    返回:
    Dict[str, str]: 日期到Excel文件路径的映射
    """
    config = load_config()
    if not config['backtest'].get('enable_log_analysis', True):
        logger.info("日志分析功能已禁用，跳过分析")
        return {}
    
    logger.info(f"开始批量回测日志分析，结果目录: {results_dir}")
    
    analysis_results = {}
    
    # 查找所有日期目录
    if not os.path.exists(results_dir):
        logger.warning(f"结果目录不存在: {results_dir}")
        return analysis_results
    
    date_dirs = []
    for item in os.listdir(results_dir):
        item_path = os.path.join(results_dir, item)
        if os.path.isdir(item_path) and len(item) == 8 and item.isdigit():
            # 这是一个日期目录
            if date_filter is None or item == date_filter:
                date_dirs.append(item)
    
    if not date_dirs:
        logger.warning("未找到日期目录")
        return analysis_results
    
    logger.info(f"找到 {len(date_dirs)} 个日期目录: {', '.join(date_dirs)}")
    
    # 为每个日期运行分析
    for date_dir in sorted(date_dirs):
        try:
            logger.info(f"分析日期: {date_dir}")
            
            # 创建该日期目录的分析器实例
            analyzer = TradeLogAnalyzer()
            date_dir_path = os.path.join(results_dir, date_dir)
            
            # 查找该日期目录下的所有日志文件
            log_files = []
            if os.path.exists(date_dir_path):
                for file in os.listdir(date_dir_path):
                    if file.endswith('.log'):
                        log_file_path = os.path.join(date_dir_path, file)
                        
                        # 检查文件名中的目标日期是否匹配
                        target_date = analyzer._extract_target_date_from_filename(log_file_path)
                        if target_date == date_dir:
                            log_files.append(log_file_path)
                            logger.debug(f"匹配目标日期 {date_dir} 的日志文件: {file}")
                        else:
                            logger.debug(f"跳过日志文件 {file}，目标日期 {target_date} 不匹配当前分析日期 {date_dir}")
            
            if not log_files:
                logger.warning(f"日期 {date_dir} 目录下未找到匹配的日志文件")
                continue
            
            logger.info(f"日期 {date_dir} 找到 {len(log_files)} 个匹配的日志文件")
            
            # 分析该日期的日志文件
            df = analyzer.analyze_logs(log_files)
            
            if df.empty:
                logger.warning(f"日期 {date_dir} 未找到交易记录")
                continue
            
            # 生成输出文件路径
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f'trade_analysis_{date_dir}_{timestamp}.xlsx'
            output_path = os.path.join(date_dir_path, output_filename)
            
            # 保存分析结果
            analyzer.save_to_excel(df, output_path)
            
            analysis_results[date_dir] = output_path
            logger.info(f"日期 {date_dir} 分析完成: {output_path}")
                
        except Exception as e:
            logger.error(f"分析日期 {date_dir} 时出错: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    return analysis_results

def check_log_analysis_enabled() -> bool:
    """检查日志分析是否启用"""
    try:
        config = load_config()
        return config['backtest'].get('enable_log_analysis', True)
    except Exception as e:
        logger.error(f"检查日志分析配置时出错: {str(e)}")
        return False

if __name__ == "__main__":
    main() 