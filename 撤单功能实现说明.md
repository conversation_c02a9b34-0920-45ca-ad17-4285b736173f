# QMT Broker 撤单功能实现说明

## 概述

本次更新为QMT Broker和策略系统增加了完整的撤单功能，包括：
1. 策略清仓时自动撤销所有待处理订单
2. 超时订单自动撤销机制
3. 卖出单撤销后自动重新提交

## 功能特性

### 1. 配置文件新增参数

在 `config.yaml` 中新增了以下撤单相关配置：

```yaml
strategy:
  # 撤单相关配置
  order_timeout_seconds: 60  # 订单超时时间（秒）
  enable_auto_cancel: true   # 是否启用自动撤单
  cancel_on_clear_position: true  # 清仓时是否撤销所有订单
```

### 2. QMT Broker 新增功能

#### 2.1 订单提交时间记录
- 在 `buy()` 和 `sell()` 方法中记录每个订单的提交时间
- 使用 `order_submit_time` 字典存储 `{bt_order_id: submit_time}` 映射

#### 2.2 撤销待处理订单方法
```python
def cancel_pending_orders(self, stock_code=None, order_type=None):
    """
    撤销待处理订单
    :param stock_code: 股票代码，None表示所有股票
    :param order_type: 订单类型，None表示所有类型，23=买入，24=卖出
    :return: 撤销结果列表
    """
```

**功能说明：**
- 查询QMT中的可撤订单
- 支持按股票代码和订单类型过滤
- 返回详细的撤单结果信息
- 处理各种撤单错误情况

#### 2.3 超时订单检查和撤销方法
```python
def check_and_cancel_timeout_orders(self, timeout_seconds=60):
    """
    检查并撤销超时订单
    :param timeout_seconds: 超时时间（秒）
    :return: 撤销的订单信息
    """
```

**功能说明：**
- 检查backtrader待处理订单的提交时间
- 识别状态为 `Submitted` 或 `Partial` 的超时订单
- 自动撤销超时订单并更新状态
- 清理相关的映射记录

### 3. 策略集成

#### 3.1 清仓时撤单功能

在策略的 `next()` 方法中，收盘前清仓时会自动撤销所有待处理订单：

```python
# 每个交易日收盘前输出统计信息并清仓
if current_time >= self.p.clear_position_time:
    # 检查是否启用清仓时撤单功能
    if CONFIG['strategy'].get('cancel_on_clear_position', True):
        self.log('收盘前撤销所有待处理订单', level=logging.INFO)
        if hasattr(self.broker, 'cancel_pending_orders'):
            cancel_results = self.broker.cancel_pending_orders()
            if cancel_results:
                self.log(f'撤销了{len(cancel_results)}个订单', level=logging.INFO)
                for result in cancel_results:
                    if result['success']:
                        self.log(f"撤单成功: {result['stock_code']} {'买入' if result['order_type'] == 23 else '卖出'}", level=logging.INFO)
                    else:
                        self.log(f"撤单失败: {result['stock_code']} 错误码={result['cancel_result']}", level=logging.WARNING)
```

#### 3.2 超时订单自动撤销

在交易时间内，策略会定期检查并撤销超时订单：

```python
# 检查并撤销超时订单
if CONFIG['strategy'].get('enable_auto_cancel', True):
    timeout_seconds = CONFIG['strategy'].get('order_timeout_seconds', 60)
    if hasattr(self.broker, 'check_and_cancel_timeout_orders'):
        cancelled_orders = self.broker.check_and_cancel_timeout_orders(timeout_seconds)
        if cancelled_orders:
            for cancelled in cancelled_orders:
                self.log(f"撤销超时订单: {cancelled['stock_code']} {'买入' if cancelled['is_buy'] else '卖出'}, "
                       f"超时{cancelled['timeout_duration']:.1f}秒", level=logging.INFO)
                
                # 如果是卖出单被撤销，需要重新提交卖出订单
                if not cancelled['is_buy'] and self.has_position:
                    self.log(f"卖出单被撤销，重新提交卖出订单", level=logging.INFO)
                    self.order = self.sell(size=self.pos.size, price=self.dataclose[0])
                    return
```

## 错误处理

### QMT 撤单错误码说明

- `0`: 撤单成功
- `-1`: 委托已完成，撤单失败
- `-2`: 未找到对应委托编号，撤单失败
- `-3`: 账号未登录，撤单失败
- 其他: 未知错误

### 异常处理机制

1. **网络异常**: 捕获并记录网络连接错误
2. **数据异常**: 处理订单数据不完整的情况
3. **状态异常**: 处理订单状态不一致的情况
4. **映射异常**: 处理backtrader订单与QMT订单映射丢失的情况

## 使用场景

### 1. 日内交易策略
- 收盘前自动撤销所有未成交订单
- 避免隔夜持仓风险

### 2. 高频交易策略
- 超时订单自动撤销，避免价格滑点
- 卖出单撤销后立即重新提交，确保及时止损

### 3. 风险控制
- 系统异常时快速撤销所有订单
- 防止订单堆积造成资金占用

## 配置建议

### 保守配置
```yaml
strategy:
  order_timeout_seconds: 30   # 30秒超时
  enable_auto_cancel: true
  cancel_on_clear_position: true
```

### 激进配置
```yaml
strategy:
  order_timeout_seconds: 10   # 10秒超时
  enable_auto_cancel: true
  cancel_on_clear_position: true
```

### 关闭自动撤单
```yaml
strategy:
  order_timeout_seconds: 60
  enable_auto_cancel: false   # 关闭自动撤单
  cancel_on_clear_position: false  # 关闭清仓撤单
```

## 日志记录

系统会详细记录所有撤单操作：

```
2024-01-15 14:55:00 - INFO - 收盘前撤销所有待处理订单
2024-01-15 14:55:00 - INFO - 撤销了2个订单
2024-01-15 14:55:00 - INFO - 撤单成功: 000001.SZ 买入
2024-01-15 14:55:00 - INFO - 撤单成功: 000001.SZ 卖出
2024-01-15 10:30:30 - INFO - 撤销超时订单: 000001.SZ 买入, 超时65.2秒
2024-01-15 11:15:45 - INFO - 卖出单被撤销，重新提交卖出订单
```

## 测试验证

运行测试脚本验证功能：

```bash
python test_cancel_orders.py
```

测试内容包括：
1. 配置文件加载测试
2. 撤单功能模拟测试
3. 策略集成测试
4. 超时订单处理测试

## 注意事项

1. **实盘模式**: 撤单功能只在实盘模式下生效，回测模式会跳过
2. **网络延迟**: 考虑网络延迟，建议超时时间不要设置过短
3. **订单状态**: 只有状态为 `Submitted` 或 `Partial` 的订单才会被撤销
4. **资金安全**: 卖出单撤销后会立即重新提交，确保持仓能够及时平仓
5. **日志监控**: 建议密切关注撤单相关日志，及时发现异常情况

## 更新文件列表

1. `config.yaml` - 新增撤单配置参数
2. `qmt_bt/qmtbroker.py` - 新增撤单功能方法
3. `my_connors_rsi_strategy.py` - 集成撤单功能
4. `kdj_volume_resonance_strategy.py` - 集成撤单功能
5. `test_cancel_orders.py` - 撤单功能测试脚本
6. `撤单功能实现说明.md` - 本说明文档

## 总结

本次撤单功能的实现大大提升了策略系统的风险控制能力和交易效率：

- **风险控制**: 收盘前自动撤单，避免隔夜风险
- **交易效率**: 超时订单自动撤销，减少资金占用
- **系统稳定性**: 完善的错误处理和日志记录
- **灵活配置**: 支持多种配置模式，适应不同交易需求

撤单功能已经完全集成到现有的策略系统中，可以立即投入使用。 