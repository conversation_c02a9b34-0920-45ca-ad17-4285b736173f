import os
import sys
import datetime
import argparse
import yaml
import pandas as pd
import shutil
import logging
from pathlib import Path
import subprocess
import time
import requests
import multiprocessing
import psutil
from concurrent.futures import ProcessPoolExecutor, as_completed
from utils import WechatNotifier, FileUtils, ConfigUtils, DateUtils
from batch_metadata import get_metadata_manager
import glob

# 配置日志
def setup_logger(log_dir='logs'):
    """配置日志系统"""
    # 确保日志目录存在
    FileUtils.ensure_dir_exists(log_dir)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f'{log_dir}/batch_backtest_{timestamp}.log'
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)

# 全局日志对象
logger = setup_logger()

def load_config():
    """加载配置文件"""
    return ConfigUtils.load_yaml_config('config.yaml')

def save_config(config):
    """保存配置文件"""
    return ConfigUtils.save_yaml_config(config, 'config.yaml')

def update_config_dates(date_str):
    """更新配置文件中的开始和结束日期
    
    Args:
        date_str: 格式为YYYYMMDD的日期字符串
    """
    config = load_config()
    
    # 从原始配置中获取时间部分
    orig_start_time = config['backtest']['start_time']
    orig_end_time = config['backtest']['end_time']
    
    # 提取原始时间部分（时分秒）
    start_time_part = orig_start_time[-6:] if len(orig_start_time) >= 6 else "093000"
    end_time_part = orig_end_time[-6:] if len(orig_end_time) >= 6 else "150000"
    
    # 设置回测开始时间为当天，但保留原有的具体时间
    config['backtest']['start_time'] = f"{date_str}{start_time_part}"
    # 设置回测结束时间为当天，但保留原有的具体时间
    config['backtest']['end_time'] = f"{date_str}{end_time_part}"
    
    # 确保是回测模式
    config['backtest']['is_live'] = False
    
    # 保存配置
    if save_config(config):
        logger.info(f"已更新配置文件日期为: {date_str}, 开始时间:{config['backtest']['start_time']}, 结束时间:{config['backtest']['end_time']}")
    else:
        logger.error(f"更新配置文件日期失败: {date_str}")
    
    return config

def is_trading_day(date_str):
    """检查指定日期是否为交易日
    使用 xtdata.get_trading_calendar 接口获取交易日历
    
    Args:
        date_str: 格式为YYYYMMDD的日期字符串
    
    Returns:
        bool: 是否为交易日
    """
    try:
        from xtquant import xtdata
        
        # 获取包含指定日期的交易日历(前后各加1天确保覆盖)
        # 将日期转换为datetime对象以便进行日期计算
        date_obj = datetime.datetime.strptime(date_str, "%Y%m%d")
        
        # 计算前一天和后一天的日期
        prev_date = (date_obj - datetime.timedelta(days=1)).strftime("%Y%m%d")
        next_date = (date_obj + datetime.timedelta(days=1)).strftime("%Y%m%d")
        
        # 获取这段时间的交易日历
        trading_days = xtdata.get_trading_calendar('SZ', start_time=prev_date, end_time=next_date)
        
        # 检查目标日期是否在交易日历中
        is_trading = date_str in trading_days
        
        logger.info(f"日期 {date_str} {'是' if is_trading else '不是'}交易日")
        return is_trading
        
    except Exception as e:
        logger.error(f"检查交易日时出错: {str(e)}")
        return DateUtils.is_trading_day(date_str)  # 使用DateUtils的简化版判断作为备用

def get_trading_days(start_date, end_date):
    """获取指定日期范围内的交易日列表
    
    Args:
        start_date: 格式为YYYYMMDD的开始日期字符串
        end_date: 格式为YYYYMMDD的结束日期字符串
    
    Returns:
        list: 交易日列表
    """
    try:
        from xtquant import xtdata
        
        # 获取交易日列表
        trading_days = xtdata.get_trading_calendar('SZ', start_time=start_date, end_time=end_date)
        logger.info(f"获取到交易日列表: {start_date}至{end_date}，共{len(trading_days)}个交易日")
        return trading_days
        
    except Exception as e:
        logger.error(f"获取交易日列表时出错: {str(e)}")
        return []  # 出错返回空列表

def process_single_date_backtest(args_tuple):
    """处理单个日期的回测
    
    Args:
        args_tuple: 包含所有必要参数的元组
        
    Returns:
        dict: 包含回测结果的字典
    """
    # 参数验证和安全解包
    if args_tuple is None:
        error_msg = "参数元组为None"
        logger = logging.getLogger()
        logger.error(error_msg)
        return {
            'success': False,
            'error': error_msg,
            'date': 'unknown',
            'process_id': 0,
            'trades_count': 0,
            'stocks_count': 0,
            'elapsed_time': 0,
            'log_file': None
        }
    
    try:
        # 安全解包参数元组
        if len(args_tuple) != 7:
            error_msg = f"参数元组长度错误，期望7个参数，实际得到{len(args_tuple)}个"
            logger = logging.getLogger()
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'date': str(args_tuple[0]) if len(args_tuple) > 0 else 'unknown',
                'process_id': args_tuple[1] if len(args_tuple) > 1 else 0,
                'trades_count': 0,
                'stocks_count': 0,
                'elapsed_time': 0,
                'log_file': None
            }
        
        (date_str, process_id, session_id, base_results_dir, 
         date_results_dir, process_logs_dir, log_file) = args_tuple
         
        # 验证解包后的参数
        if not date_str or not isinstance(date_str, str):
            error_msg = f"无效的日期参数: {date_str}"
            logger = logging.getLogger()
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'date': str(date_str) if date_str else 'unknown',
                'process_id': process_id if process_id else 0,
                'trades_count': 0,
                'stocks_count': 0,
                'elapsed_time': 0,
                'log_file': log_file
            }
            
    except (ValueError, TypeError, IndexError) as e:
        error_msg = f"参数解包失败: {str(e)}, args_tuple={args_tuple}"
        logger = logging.getLogger()
        logger.error(error_msg)
        return {
            'success': False,
            'error': error_msg,
            'date': 'unknown',
            'process_id': 0,
            'trades_count': 0,
            'stocks_count': 0,
            'elapsed_time': 0,
            'log_file': None
        }
    
    # 设置进程日志
    logger = setup_logger(process_logs_dir)
    logger.info(f"进程 {process_id} 开始处理日期 {date_str}")
    
    # 初始化结果
    result = {
        'date': date_str,
        'process_id': process_id,
        'success': False,
        'error': None,
        'trades_count': 0,
        'stocks_count': 0,
        'elapsed_time': 0,
        'log_file': log_file
    }
    
    start_time = time.time()
    process_config_path = None
    task_id = f"{session_id}_{date_str}_{process_id}"
    
    try:
        # 获取元数据管理器
        metadata_manager = get_metadata_manager()
        
        # 注册进程和创建任务记录
        metadata_manager.register_process(session_id, process_id, os.getpid())
        metadata_manager.create_process_task(session_id, task_id, date_str, process_id, date_results_dir)
        metadata_manager.update_task_status(task_id, 'running', process_id)
        
        # === 创建独立的配置文件副本 ===
        original_config_path = 'config.yaml'
        process_config_path = os.path.join(date_results_dir, f'config_process_{process_id}.yaml')
        
        # 读取并更新配置文件
        import shutil
        from utils import ConfigUtils
        
        shutil.copy2(original_config_path, process_config_path)
        logger.info(f"已创建进程 {process_id} 的独立配置文件: {process_config_path}")
        
        # 读取并更新配置文件中的日期信息
        config = ConfigUtils.load_yaml_config(process_config_path)
        if not config:
            raise Exception(f"无法加载配置文件: {process_config_path}")
        
        # 更新回测日期到目标日期
        orig_start_time = config['backtest'].get('start_time', '20241201093000')
        orig_end_time = config['backtest'].get('end_time', '20241201150000')
        
        # 提取时间部分（HHMMSS）
        start_time_part = orig_start_time[-6:] if len(orig_start_time) >= 6 else "093000"
        end_time_part = orig_end_time[-6:] if len(orig_end_time) >= 6 else "150000"
        
        # 更新为目标日期
        config['backtest']['start_time'] = f"{date_str}{start_time_part}"
        config['backtest']['end_time'] = f"{date_str}{end_time_part}"
        config['backtest']['is_live'] = False  # 确保是回测模式
        
        logger.info(f"进程 {process_id} 更新配置文件日期为 {date_str}")
        logger.info(f"  start_time: {config['backtest']['start_time']}")
        logger.info(f"  end_time: {config['backtest']['end_time']}")
        
        # 保存更新后的配置文件
        if not ConfigUtils.save_yaml_config(config, process_config_path):
            raise Exception(f"无法保存配置文件: {process_config_path}")
        
        # 将配置信息存储到数据库
        with open(original_config_path, 'r', encoding='utf-8') as f:
            original_config_content = f.read()
        with open(process_config_path, 'r', encoding='utf-8') as f:
            updated_config_content = f.read()
        
        config_stored = metadata_manager.store_process_config(
            session_id, task_id, process_id, date_str, 
            process_config_path, original_config_content, updated_config_content
        )
        
        if not config_stored:
            logger.warning(f"配置信息存储到数据库失败，但继续执行")
        
        # === 执行选股脚本，确保使用正确的配置文件和日期 ===
        select_stocks_cmd = [
            'python', 'select_stocks.py',
            '--date', date_str,  # 显式传递日期参数
            '--config-path', process_config_path,  # 指定配置文件路径
            '--img-dir', date_results_dir,  # 指定输出目录
            '--log-dir', date_results_dir   # 指定日志目录
        ]
        
        # 检查是否需要强制重新选股
        if '--no-cache' in sys.argv:
            select_stocks_cmd.append('--no-cache')
        
        logger.info(f"执行选股命令: {' '.join(select_stocks_cmd)}")
        logger.info(f"工作目录: {os.getcwd()}")
        logger.info(f"使用配置文件: {process_config_path}")
        logger.info(f"目标日期: {date_str}")
        
        # 执行选股脚本
        subprocess_result = subprocess.run(
            select_stocks_cmd, 
            check=True, 
            capture_output=True,
            text=True
        )
        
        if subprocess_result.stdout:
            logger.info(f"select_stocks stdout: {subprocess_result.stdout}")
        if subprocess_result.stderr:
            logger.warning(f"select_stocks stderr: {subprocess_result.stderr}")
        
        logger.info(f"选股脚本执行成功")
        
        # 初始化统计信息
        trades_count = 0
        stocks_count = 0
        
        # 检测新生成的CSV文件作为交易统计
        csv_files = glob.glob('results/multi_stock_backtest_results.csv')
        
        # 统计交易数据
        for csv_file in csv_files:
            if os.path.exists(csv_file):
                logger.info(f"发现回测结果文件: {csv_file}")
                
                # 简单读取CSV统计信息
                try:
                    import pandas as pd
                    df = pd.read_csv(csv_file)
                    
                    if not df.empty:
                        # 过滤当前日期的数据
                        if 'date' in df.columns or 'Date' in df.columns:
                            date_col = 'date' if 'date' in df.columns else 'Date'
                            date_df = df[df[date_col].astype(str).str.contains(date_str)]
                            
                            if not date_df.empty:
                                trades_count = len(date_df)
                                # 统计涉及的股票数量
                                if 'symbol' in date_df.columns:
                                    stocks_count = date_df['symbol'].nunique()
                                elif 'stock_code' in date_df.columns:
                                    stocks_count = date_df['stock_code'].nunique()
                                else:
                                    stocks_count = 1
                        else:
                            # 如果没有日期列，假设所有数据都是当前日期的
                            trades_count = len(df)
                            if 'symbol' in df.columns:
                                stocks_count = df['symbol'].nunique()
                            elif 'stock_code' in df.columns:
                                stocks_count = df['stock_code'].nunique()
                            else:
                                stocks_count = 1
                            
                        logger.info(f"统计到 {trades_count} 条交易记录，涉及 {stocks_count} 只股票")
                except Exception as e:
                    logger.error(f"读取CSV文件统计信息时出错: {str(e)}")
                    trades_count = 0
                    stocks_count = 0
        
        # 更新结果
        result.update({
            'success': True,
            'trades_count': trades_count,
            'stocks_count': stocks_count,
            'elapsed_time': time.time() - start_time,
            'has_trades': trades_count > 0
        })
        
        # 更新任务状态为完成
        metadata_manager.update_task_status(task_id, 'completed', process_id)
        metadata_manager.update_task_result(task_id, {
            'trades_count': trades_count,
            'stocks_count': stocks_count,
            'success': True
        })
        
        logger.info(f"进程 {process_id} 完成日期 {date_str} 的处理，用时: {result['elapsed_time']:.2f}秒")
        
    except subprocess.CalledProcessError as e:
        error_msg = f"选股脚本执行失败 (退出代码: {e.returncode})"
        if e.stdout:
            error_msg += f", stdout: {e.stdout}"
        if e.stderr:
            error_msg += f", stderr: {e.stderr}"
        logger.error(error_msg)
        result['error'] = error_msg
        
        # 更新任务状态为失败
        try:
            metadata_manager = get_metadata_manager()
            metadata_manager.update_task_status(task_id, 'failed', process_id)
            metadata_manager.update_task_result(task_id, {'error': error_msg})
        except Exception as db_e:
            logger.error(f"更新数据库任务状态失败: {str(db_e)}")
            
    except Exception as e:
        error_msg = f"处理日期 {date_str} 时发生异常: {str(e)}"
        logger.error(error_msg)
        result['error'] = error_msg
        
        # 更新任务状态为失败
        try:
            metadata_manager = get_metadata_manager()
            metadata_manager.update_task_status(task_id, 'failed', process_id)
            metadata_manager.update_task_result(task_id, {'error': error_msg})
        except Exception as db_e:
            logger.error(f"更新数据库任务状态失败: {str(db_e)}")
    
    finally:
        # 清理进程专用的配置文件
        if process_config_path and os.path.exists(process_config_path):
            try:
                os.remove(process_config_path)
                logger.info(f"已清理进程 {process_id} 的配置文件: {process_config_path}")
            except Exception as e:
                logger.warning(f"清理配置文件失败: {str(e)}")
        
        # 清理数据库中的配置信息
        try:
            metadata_manager.cleanup_process_config(task_id)
        except Exception as e:
            logger.warning(f"清理数据库配置信息失败: {str(e)}")
        
        # 计算最终执行时间
        final_elapsed_time = time.time() - start_time
        result['elapsed_time'] = final_elapsed_time
    
    return result

def run_parallel_batch_backtest(start_date=None, end_date=None, days=30, max_processes=None):
    """运行多进程并行批量回测
    
    Args:
        start_date: 开始日期，格式为YYYYMMDD
        end_date: 结束日期，格式为YYYYMMDD  
        days: 回溯天数
        max_processes: 最大进程数，默认为10
    """
    # 记录开始时间
    start_time = time.time()
    
    # 确定最大进程数
    if max_processes is None:
        max_processes = min(multiprocessing.cpu_count(), 20)  # 限制最大进程数避免资源过度占用
    
    logger.info(f"启动多进程批量回测，最大进程数: {max_processes}")
    
    # 确保logs目录存在
    FileUtils.ensure_dir_exists('logs')
    
    # 确保results目录存在
    results_base_dir = 'results'
    FileUtils.ensure_dir_exists(results_base_dir)
    
    # 创建本次运行的结果目录
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = os.path.join(results_base_dir, f'backtest_parallel_{timestamp}')
    os.makedirs(results_dir)
    logger.info(f"创建结果目录: {results_dir}")
    
    # 获取元数据管理器并开始批量回测会话
    metadata_manager = get_metadata_manager()
    session_id = f"backtest_parallel_{timestamp}"
    
    # 复制配置和策略文件到结果目录
    original_config_path = 'config.yaml'
    if os.path.exists(original_config_path):
        shutil.copy2(original_config_path, os.path.join(results_dir, 'config.yaml'))
        logger.info("已复制配置文件到结果目录")
    else:
        logger.warning("未找到配置文件 config.yaml")
        return

    if os.path.exists('my_connors_rsi_strategy.py'):
        shutil.copy2('my_connors_rsi_strategy.py', os.path.join(results_dir, 'my_connors_rsi_strategy.py'))
        logger.info("已复制策略文件到结果目录")
    
    # 确定日期范围
    if start_date is None and end_date is None:
        end_date = datetime.datetime.now().strftime("%Y%m%d")
        start_date_obj = datetime.datetime.now() - datetime.timedelta(days=days)
        start_date = start_date_obj.strftime("%Y%m%d")
    elif start_date is None:
        end_date_obj = datetime.datetime.strptime(end_date, "%Y%m%d")
        start_date_obj = end_date_obj - datetime.timedelta(days=days)
        start_date = start_date_obj.strftime("%Y%m%d")
    elif end_date is None:
        end_date = datetime.datetime.now().strftime("%Y%m%d")
    
    logger.info(f"回测日期范围: {start_date} 到 {end_date}")
    
    # 获取交易日列表
    trading_days = get_trading_days(start_date, end_date)
    
    if not trading_days:
        logger.error("未能获取到交易日历，请检查日期范围或网络连接")
        return
    
    logger.info(f"将对以下交易日进行回测: {', '.join(trading_days)}")
    
    # 开始批量回测会话
    if not metadata_manager.start_batch_session(
        session_id=session_id, 
        results_dir=results_dir,
        start_date=start_date,
        end_date=end_date, 
        total_dates=len(trading_days),
        process_mode='parallel',
        max_processes=max_processes
    ):
        logger.error("无法启动批量回测会话")
        return
    
    try:
        # 准备进程参数
        process_args = []
        for i, date_str in enumerate(trading_days):
            process_id = i + 1
            
            # 创建日期专用结果目录
            date_results_dir = os.path.join(results_dir, date_str)
            os.makedirs(date_results_dir, exist_ok=True)
            
            # 创建进程专用日志目录
            process_logs_dir = os.path.join(results_dir, 'process_logs')
            os.makedirs(process_logs_dir, exist_ok=True)
            
            # 生成日志文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = f'process_{process_id}_{date_str}_{timestamp}.log'
            
            # 构建参数元组：(date_str, process_id, session_id, base_results_dir, date_results_dir, process_logs_dir, log_file)
            args_tuple = (
                date_str,           # 日期字符串
                process_id,         # 进程ID
                session_id,         # 会话ID
                results_dir,        # 基础结果目录
                date_results_dir,   # 日期专用结果目录
                process_logs_dir,   # 进程日志目录
                log_file           # 日志文件名
            )
            process_args.append(args_tuple)
            
            logger.info(f"准备进程 {process_id} 处理日期 {date_str}")
            logger.debug(f"日期结果目录: {date_results_dir}")
            logger.debug(f"进程日志目录: {process_logs_dir}")
        
        logger.info(f"准备完成，将使用 {len(process_args)} 个进程处理 {len(trading_days)} 个交易日")
        
        # 使用ProcessPoolExecutor执行多进程回测
        with ProcessPoolExecutor(max_workers=max_processes) as executor:
            logger.info(f"🚀 启动多进程并行回测模式")
            logger.info(f"最大进程数: {max_processes}")
            logger.info(f"待处理日期: {', '.join(trading_days)}")
            
            # 提交所有任务
            future_to_date = {}
            for args in process_args:
                if args and len(args) > 0:
                    date_str = args[0] if args[0] else 'unknown'
                    future = executor.submit(process_single_date_backtest, args)
                    future_to_date[future] = date_str
                else:
                    logger.error(f"无效的参数: {args}")
            
            # 实时显示进度
            completed_count = 0
            failed_dates = []
            results = []
            
            for future in as_completed(future_to_date):
                date_str = future_to_date[future]
                completed_count += 1
                
                try:
                    result = future.result()
                    results.append(result)
                    
                    # 确保result有正确的date字段
                    actual_date = result.get('date', date_str)
                    if actual_date != date_str and actual_date != 'unknown':
                        date_str = actual_date
                    
                    if result['success']:
                        status_emoji = "✅"
                        status_text = "成功"
                        if result.get('has_trades'):
                            status_detail = f"，包含交易数据"
                        else:
                            status_detail = f"，无交易数据"
                    else:
                        status_emoji = "❌"
                        status_text = "失败"
                        status_detail = f"，错误: {result.get('error', '未知错误')}"
                        if date_str != 'unknown':
                            failed_dates.append(date_str)
                    
                    # 更新会话进度
                    metadata_manager.update_session_progress(session_id, completed_count)
                    
                    logger.info(f"{status_emoji} 日期 {date_str} 处理{status_text} ({completed_count}/{len(trading_days)}){status_detail}")
                    
                    # 资源监控
                    if psutil:
                        try:
                            # 获取当前进程资源使用
                            current_process = psutil.Process()
                            cpu_percent = current_process.cpu_percent()
                            memory_percent = current_process.memory_percent()
                            
                            if completed_count % 3 == 0:  # 每3个任务输出一次
                                logger.info(f"📊 主进程资源使用 - CPU: {cpu_percent:.1f}%, 内存: {memory_percent:.1f}%")
                        except Exception as e:
                            logger.debug(f"获取资源使用信息失败: {str(e)}")
                
                except Exception as e:
                    logger.error(f"❌ 日期 {date_str} 处理异常: {str(e)}")
                    if date_str != 'unknown':
                        failed_dates.append(date_str)
                    results.append({
                        'success': False,
                        'date': date_str,
                        'error': str(e)
                    })
                    completed_count += 1
                    # 更新会话进度
                    metadata_manager.update_session_progress(session_id, completed_count)
        
        # 记录处理结果
        logger.info(f"🎉 多进程批量回测完成，成功处理 {len(results) - len(failed_dates)}/{len(trading_days)} 天")
        if failed_dates:
            logger.warning(f"❌ 失败的日期: {', '.join(failed_dates)}")
        
        # 记录结束时间和计算用时
        end_time = time.time()
        elapsed_time = end_time - start_time
        logger.info(f"⏱️ 多进程批量回测总用时: {elapsed_time:.2f}秒")
        
        # 结束批量回测会话
        session_status = 'completed' if len(results) > 0 else 'failed'
        metadata_manager.end_batch_session(
            session_id=session_id,
            status=session_status,
            completed_dates=len(results) - len(failed_dates),
            failed_dates=failed_dates
        )
        
        # 合并结果（与原有逻辑相同）
        logger.info("🔄 开始尝试合并回测结果...")
        try:
            results_combined = combine_results(results_dir, failed_dates)
            if results_combined:
                logger.info("✅ 成功生成CSV合并结果文件")
            else:
                logger.warning("⚠️ 未能生成CSV合并结果文件")
        except Exception as e:
            logger.error(f"❌ 合并CSV结果时出错: {str(e)}")
            results_combined = False
        
        # 运行日志分析
        if len(results) > 0 or results_combined:
            config = load_config()
            if config['backtest'].get('enable_log_analysis', True):
                try:
                    time.sleep(2)  # 短暂延迟确保文件写入完成
                    
                    from log_analyzer import run_daily_analysis_for_batch
                    logger.info("🔍 开始运行批量回测日志分析...")
                    
                    analysis_results = run_daily_analysis_for_batch(results_dir)
                    
                    if analysis_results:
                        logger.info(f"✅ 日志分析完成，生成了 {len(analysis_results)} 个分析文件")
                        
                        # 合并分析结果
                        combined_analysis_file = combine_log_analysis_results(results_dir)
                        if combined_analysis_file:
                            logger.info(f"✅ 成功生成合并的分析结果文件: {combined_analysis_file}")
                        else:
                            logger.warning("⚠️ 未能生成合并的分析结果文件")
                    else:
                        logger.warning("⚠️ 未生成任何日志分析文件")
                        
                except ImportError as e:
                    logger.error(f"❌ 无法导入日志分析模块: {str(e)}")
                except Exception as e:
                    logger.error(f"❌ 运行日志分析时出错: {str(e)}")
                    import traceback
                    logger.error(f"详细错误信息: {traceback.format_exc()}")
            else:
                logger.info("⚠️ 日志分析功能已禁用，跳过")
        else:
            logger.warning("⚠️ 没有成功的回测日期且未生成合并结果，跳过日志分析")

        # 发送回测完成通知
        stats = {}
        
        combined_csv = os.path.join(results_dir, 'combined_results.csv')
        if os.path.exists(combined_csv):
            stats["已生成汇总结果文件"] = "combined_results.csv"
            
            stock_stats_csv = os.path.join(results_dir, 'stock_statistics.csv')
            if os.path.exists(stock_stats_csv):
                try:
                    stock_stats_df = pd.read_csv(stock_stats_csv, encoding='utf-8-sig')
                    if not stock_stats_df.empty:
                        top_stocks = stock_stats_df.sort_values('平均收益率', ascending=False).head(3)
                        for i, (_, row) in enumerate(top_stocks.iterrows(), 1):
                            stats[f"最佳股票{i}"] = f"{row['股票代码']}: 收益率{row['平均收益率']:.2f}%, 交易{row['交易天数']}次"
                except Exception as e:
                    logger.error(f"读取股票统计结果时出错: {str(e)}")
        
        if failed_dates:
            stats["失败日期"] = f"{len(failed_dates)}个日期失败: {', '.join(failed_dates[:3])}{'...' if len(failed_dates) > 3 else ''}"
        
        # 添加多进程特有的统计信息
        stats["处理模式"] = f"多进程并行 (最大进程数: {max_processes})"
        stats["平均处理速度"] = f"{elapsed_time/len(trading_days):.1f}秒/天"
        
        WechatNotifier.send_backtest_notification(
            backtest_type='parallel_batch',
            date_range=(start_date, end_date),
            success_count=len(results) - len(failed_dates),
            total_count=len(trading_days),
            results_dir=results_dir,
            stats=stats,
            elapsed_time=elapsed_time
        )
        
    except Exception as e:
        logger.error(f"❌ 多进程批量回测时发生严重错误: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        
        # 尝试结束会话
        try:
            metadata_manager.end_batch_session(session_id, 'failed')
        except:
            pass

def run_batch_backtest(start_date=None, end_date=None, days=30):
    """运行批量回测
    
    Args:
        start_date: 开始日期，格式为YYYYMMDD，如果为None则使用当前日期
        end_date: 结束日期，格式为YYYYMMDD，如果为None则使用当前日期
        days: 当start_date为None时，回溯的天数
    """
    # 记录开始时间
    start_time = time.time()
    
    # 确保logs目录存在
    FileUtils.ensure_dir_exists('logs')
    
    # 确保results目录存在
    results_base_dir = 'results'
    FileUtils.ensure_dir_exists(results_base_dir)
    
    # 创建本次运行的结果目录
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = os.path.join(results_base_dir, f'backtest_{timestamp}')
    os.makedirs(results_dir)
    logger.info(f"创建结果目录: {results_dir}")
    
    # 获取元数据管理器并开始批量回测会话
    metadata_manager = get_metadata_manager()
    session_id = f"backtest_{timestamp}"
    
    # 复制当前配置文件到结果目录
    if os.path.exists('config.yaml'):
        shutil.copy2('config.yaml', os.path.join(results_dir, 'config.yaml'))
        logger.info("已复制配置文件到结果目录")
    else:
        logger.warning("未找到配置文件 config.yaml")

    # 复制当前策略文件到结果目录
    if os.path.exists('my_connors_rsi_strategy.py'):
        shutil.copy2('my_connors_rsi_strategy.py', os.path.join(results_dir, 'my_connors_rsi_strategy.py'))
        logger.info("已复制策略文件到结果目录")
    
    # 确定日期范围
    if start_date is None and end_date is None:
        # 使用当前日期作为结束日期，并回溯指定天数
        end_date = datetime.datetime.now().strftime("%Y%m%d")
        start_date_obj = datetime.datetime.now() - datetime.timedelta(days=days)
        start_date = start_date_obj.strftime("%Y%m%d")
    elif start_date is None:
        # 只指定了结束日期，从结束日期回溯
        end_date_obj = datetime.datetime.strptime(end_date, "%Y%m%d")
        start_date_obj = end_date_obj - datetime.timedelta(days=days)
        start_date = start_date_obj.strftime("%Y%m%d")
    elif end_date is None:
        # 只指定了开始日期，使用当前日期作为结束日期
        end_date = datetime.datetime.now().strftime("%Y%m%d")
    
    logger.info(f"回测日期范围: {start_date} 到 {end_date}")
    
    # 获取日期范围内的交易日
    trading_days = get_trading_days(start_date, end_date)
    
    if not trading_days:
        logger.error("未能获取到交易日历，请检查日期范围或网络连接")
        return
    
    logger.info(f"将对以下交易日进行回测: {', '.join(trading_days)}")
    
    # 保存原始配置
    original_config = load_config()
    
    # 开始批量回测会话
    if not metadata_manager.start_batch_session(
        session_id=session_id, 
        results_dir=results_dir,
        start_date=start_date,
        end_date=end_date, 
        total_dates=len(trading_days)
    ):
        logger.error("无法启动批量回测会话")
        return
    
    try:
        # 遍历每个交易日进行回测
        successful_days = 0
        failed_days = []
        
        for date_str in trading_days:
            try:
                logger.info(f"开始处理日期: {date_str}")
                
                # 更新配置文件中的日期
                config = update_config_dates(date_str)
                
                # 创建该日期的结果目录
                date_results_dir = os.path.join(results_dir, date_str)
                os.makedirs(date_results_dir, exist_ok=True)
                
                # 记录回测开始前的CSV文件时间戳，用于判断是否生成了新文件
                csv_files_before = []
                if os.path.exists('results'):
                    csv_files_before.extend([os.path.join('results', f) for f in os.listdir('results') 
                                           if f.endswith('.csv') and (f.startswith('backtest_results_') or f == 'multi_stock_backtest_results.csv')])
                csv_files_before.extend([f for f in os.listdir('.') 
                                       if f.endswith('.csv') and (f.startswith('backtest_results_') or f == 'multi_stock_backtest_results.csv')])
                
                before_timestamps = {f: os.path.getctime(f) for f in csv_files_before if os.path.exists(f)}
                
                # 执行选股脚本
                select_stocks_cmd = ['python', 'select_stocks.py', '--date', date_str]
                if '--no-cache' in sys.argv:
                    select_stocks_cmd.append('--no-cache')
                
                # 添加输出目录参数，确保结果保存到正确的位置
                select_stocks_cmd.extend(['--img-dir', date_results_dir])
                select_stocks_cmd.extend(['--log-dir', date_results_dir])
                
                logger.info(f"执行选股命令: {' '.join(select_stocks_cmd)}")
                logger.info(f"工作目录: {os.getcwd()}")
                logger.info(f"输出目录: {date_results_dir}")
                
                # 在当前目录中运行，避免路径问题
                subprocess_result = subprocess.run(
                    select_stocks_cmd, 
                    check=True, 
                    capture_output=True,
                    text=True
                )
                
                logger.info(f"选股脚本执行成功")
                
                if subprocess_result.stdout:
                    logger.debug(f"select_stocks stdout: {subprocess_result.stdout}")
                if subprocess_result.stderr:
                    logger.warning(f"select_stocks stderr: {subprocess_result.stderr}")
                
                # 复制根目录生成的结果文件到日期目录
                root_csv_files = glob.glob('results/multi_stock_backtest_results*.csv')
                for root_csv_file in root_csv_files:
                    if os.path.exists(root_csv_file):
                        # 生成目标文件名
                        filename = os.path.basename(root_csv_file)
                        target_file = os.path.join(date_results_dir, filename)
                        
                        try:
                            shutil.copy2(root_csv_file, target_file)
                            logger.info(f"已复制结果文件: {root_csv_file} -> {target_file}")
                        except Exception as copy_error:
                            logger.warning(f"复制结果文件失败: {str(copy_error)}")
                
                # 初始化统计变量
                trades_count = 0
                stocks_count = 0
                
                # 检测日期目录中的CSV文件进行统计
                csv_files = glob.glob(os.path.join(date_results_dir, '*.csv'))
                if not csv_files:
                    # 如果日期目录没有CSV文件，检查根目录
                    csv_files = glob.glob('results/multi_stock_backtest_results*.csv')
                    logger.warning(f"日期目录无CSV文件，使用根目录文件进行统计")
                
                # 统计交易数据
                for csv_file in csv_files:
                    if os.path.exists(csv_file):
                        logger.info(f"发现回测结果文件: {csv_file}")
                        
                        # 简单读取CSV统计信息
                        try:
                            import pandas as pd
                            df = pd.read_csv(csv_file)
                            
                            if not df.empty:
                                # 过滤当前日期的数据
                                if 'date' in df.columns or 'Date' in df.columns:
                                    date_col = 'date' if 'date' in df.columns else 'Date'
                                    date_df = df[df[date_col].astype(str).str.contains(date_str)]
                                    
                                    if not date_df.empty:
                                        trades_count = len(date_df)
                                        stocks_count = date_df['stock_code'].nunique() if 'stock_code' in date_df.columns else len(date_df)
                                        logger.info(f"统计到 {trades_count} 条交易记录，涉及 {stocks_count} 只股票")
                                else:
                                    # 如果没有日期列，假设整个文件都是当前日期的数据
                                    trades_count = len(df)
                                    stocks_count = df['stock_code'].nunique() if 'stock_code' in df.columns else len(df)
                                    logger.info(f"统计到 {trades_count} 条交易记录，涉及 {stocks_count} 只股票")
                        except Exception as e:
                            logger.warning(f"读取CSV文件 {csv_file} 失败: {str(e)}")
                            
                        # 跳出循环，只处理第一个找到的CSV文件
                        break
                
                # 记录处理成功
                logger.info(f"日期 {date_str} 处理成功完成，交易记录: {trades_count}，股票数: {stocks_count}")
                
                # 更新会话进度
                metadata_manager.update_session_progress(session_id, successful_days)
                
                logger.info(f"日期 {date_str} 处理完成")
                successful_days += 1
            except Exception as e:
                logger.error(f"处理日期 {date_str} 时发生错误: {str(e)}")
                failed_days.append(date_str)
        
        # 记录处理结果
        logger.info(f"批量回测完成，成功处理 {successful_days}/{len(trading_days)} 天")
        if failed_days:
            logger.warning(f"失败的日期: {', '.join(failed_days)}")
        
        # 记录结束时间和计算用时
        end_time = time.time()
        elapsed_time = end_time - start_time
        logger.info(f"批量回测总用时: {elapsed_time:.2f}秒")
        
        # 结束批量回测会话
        session_status = 'completed' if successful_days > 0 else 'failed'
        metadata_manager.end_batch_session(
            session_id=session_id,
            status=session_status,
            completed_dates=successful_days,
            failed_dates=failed_days
        )
        
        # 合并结果 - 改进逻辑：即使successful_days为0，也要尝试合并结果
        # 因为可能有部分成功的交易记录虽然没有生成完整的CSV文件
        logger.info("开始尝试合并回测结果...")
        try:
            # 合并CSV回测结果
            results_combined = combine_results(results_dir, failed_days)
            
            if results_combined:
                logger.info("成功生成CSV合并结果文件")
            else:
                logger.warning("未能生成CSV合并结果文件")
        except Exception as e:
            logger.error(f"合并CSV结果时出错: {str(e)}")
            results_combined = False
        
        # 只有在有合并结果或有成功日期时才运行日志分析
        if successful_days > 0 or results_combined:
            # 运行日志分析（如果启用）
            config = load_config()
            if config['backtest'].get('enable_log_analysis', True):
                try:
                    # 添加短暂延迟，确保所有日志文件写入完成
                    time.sleep(2)
                    
                    # 使用专用的批量回测日志分析接口
                    from log_analyzer import run_daily_analysis_for_batch
                    logger.info("开始运行批量回测日志分析...")
                    
                    # 为所有日期运行分析，直接在各日期目录下生成分析文件
                    analysis_results = run_daily_analysis_for_batch(results_dir)
                    
                    if analysis_results:
                        logger.info(f"日志分析完成，生成了 {len(analysis_results)} 个分析文件")
                        for date, file_path in analysis_results.items():
                            logger.info(f"日期 {date}: {file_path}")
                            
                        # 清理可能存在的重复logs_analysis目录
                        logs_analysis_dir = os.path.join(results_dir, 'logs_analysis')
                        if os.path.exists(logs_analysis_dir):
                            logger.info(f"清理重复的logs_analysis目录: {logs_analysis_dir}")
                            shutil.rmtree(logs_analysis_dir)
                            logger.info("已删除重复的logs_analysis目录")
                            
                        # 清理各日期目录下可能存在的重复logs_analysis子目录
                        for date_dir in os.listdir(results_dir):
                            date_path = os.path.join(results_dir, date_dir)
                            if os.path.isdir(date_path) and len(date_dir) == 8 and date_dir.isdigit():
                                date_logs_analysis_dir = os.path.join(date_path, 'logs_analysis')
                                if os.path.exists(date_logs_analysis_dir):
                                    logger.info(f"清理日期 {date_dir} 目录下的重复logs_analysis子目录: {date_logs_analysis_dir}")
                                    shutil.rmtree(date_logs_analysis_dir)
                                    logger.info(f"已删除日期 {date_dir} 的重复logs_analysis子目录")
                        
                        # 合并所有日期的日志分析结果和CSV回测结果
                        logger.info("开始合并日志分析结果和CSV回测结果...")
                        combined_analysis_file = combine_log_analysis_results(results_dir)
                        
                        if combined_analysis_file:
                            logger.info(f"成功生成合并的分析结果文件: {combined_analysis_file}")
                        else:
                            logger.warning("未能生成合并的分析结果文件")
                    else:
                        logger.warning("未生成任何日志分析文件")
                        
                except ImportError as e:
                    logger.error(f"无法导入日志分析模块: {str(e)}")
                except Exception as e:
                    logger.error(f"运行日志分析时出错: {str(e)}")
                    import traceback
                    logger.error(f"详细错误信息: {traceback.format_exc()}")
            else:
                logger.info("日志分析功能已禁用，跳过")
        else:
            logger.warning("没有成功的回测日期且未生成合并结果，跳过日志分析")

        # 发送回测完成通知
        stats = {}
        
        # 如果生成了汇总统计，添加到通知中
        combined_csv = os.path.join(results_dir, 'combined_results.csv')
        if os.path.exists(combined_csv):
            stats["已生成汇总结果文件"] = "combined_results.csv"
            
            # 如果生成了股票统计，添加最佳表现的股票
            stock_stats_csv = os.path.join(results_dir, 'stock_statistics.csv')
            if os.path.exists(stock_stats_csv):
                try:
                    stock_stats_df = pd.read_csv(stock_stats_csv, encoding='utf-8-sig')
                    if not stock_stats_df.empty:
                        # 获取表现最好的前3只股票
                        top_stocks = stock_stats_df.sort_values('平均收益率', ascending=False).head(3)
                        
                        # 添加到统计数据
                        for i, (_, row) in enumerate(top_stocks.iterrows(), 1):
                            stats[f"最佳股票{i}"] = f"{row['股票代码']}: 收益率{row['平均收益率']:.2f}%, 交易{row['交易天数']}次"
                except Exception as e:
                    logger.error(f"读取股票统计结果时出错: {str(e)}")
        
        # 添加失败日期信息到统计
        if failed_days:
            stats["失败日期"] = f"{len(failed_days)}个日期失败: {', '.join(failed_days[:3])}{'...' if len(failed_days) > 3 else ''}"
        
        WechatNotifier.send_backtest_notification(
            backtest_type='batch',
            date_range=(start_date, end_date),
            success_count=successful_days,
            total_count=len(trading_days),
            results_dir=results_dir,
            stats=stats,
            elapsed_time=elapsed_time
        )
        
    finally:
        # 恢复原始配置
        save_config(original_config)
        logger.info("已恢复原始配置")

def combine_results(results_dir, failed_days):
    """合并所有日期的回测结果CSV文件
    
    Args:
        results_dir: 结果保存目录
        failed_days: 失败的日期列表（仅用于日志记录，不影响数据收集）
    """
    logger.info("开始合并回测结果CSV文件...")
    
    # 存储所有回测结果
    all_results = []
    processed_dates = []
    skipped_dates = []
    
    # 遍历结果目录中的所有日期目录
    for date_dir in os.listdir(results_dir):
        date_path = os.path.join(results_dir, date_dir)
        if not os.path.isdir(date_path) or date_dir.startswith('backtest_'):
            continue
        
        logger.info(f"检查日期目录: {date_dir}{'（标记为失败）' if date_dir in failed_days else ''}")
        
        # 查找回测结果CSV文件
        csv_file = os.path.join(date_path, f'backtest_results_{date_dir}.csv')
        csv_data_found = False
        
        if os.path.exists(csv_file):
            try:
                # 验证CSV文件是否为有效的回测结果
                df = pd.read_csv(csv_file, encoding='utf-8-sig')
                
                # 基本验证：检查必要的列是否存在
                required_columns = ['股票代码', '收益率']
                if not all(col in df.columns for col in required_columns):
                    logger.warning(f"CSV文件 {csv_file} 缺少必要的列: {required_columns}")
                elif df.empty:
                    logger.warning(f"CSV文件 {csv_file} 为空")
                elif df['收益率'].isna().all():
                    logger.warning(f"CSV文件 {csv_file} 收益率数据全为空")
                else:
                    # CSV文件有效，添加到结果中
                    df['日期'] = date_dir
                    df['数据来源'] = 'CSV回测结果'
                    all_results.append(df)
                    processed_dates.append(date_dir)
                    csv_data_found = True
                    logger.info(f"从CSV文件读取 {date_dir} 的回测结果，包含 {len(df)} 条记录")
                
            except Exception as e:
                logger.error(f"读取CSV文件 {csv_file} 时出错: {str(e)}")
        
        # 如果没有找到CSV文件，记录跳过
        if not csv_data_found:
            logger.warning(f"日期 {date_dir} 未找到有效的CSV回测结果文件")
            skipped_dates.append(date_dir)
    
    # 记录处理统计
    logger.info(f"CSV合并统计: 成功读取 {len(processed_dates)} 个日期，跳过 {len(skipped_dates)} 个日期")
    if processed_dates:
        logger.info(f"成功处理的日期: {', '.join(processed_dates)}")
    if skipped_dates:
        logger.warning(f"跳过的日期（无CSV文件）: {', '.join(skipped_dates)}")
    
    if all_results:
        # 合并所有结果
        combined_df = pd.concat(all_results, ignore_index=True)
        
        # 保存合并后的结果
        combined_csv = os.path.join(results_dir, 'combined_results.csv')
        combined_df.to_csv(combined_csv, index=False, encoding='utf-8-sig')
        logger.info(f"已合并所有回测结果到 {combined_csv}，共 {len(combined_df)} 条记录")
        
        # 按股票分组，创建每个股票的收益统计
        stock_stats = {}
        for stock_code, group in combined_df.groupby('股票代码'):
            stock_stats[stock_code] = {
                '平均收益率': group['收益率'].mean(),
                '最大收益率': group['收益率'].max(),
                '最小收益率': group['收益率'].min(),
                '平均夏普比率': group['夏普比率'].mean() if '夏普比率' in group.columns else 0.0,
                '平均胜率': group['胜率'].mean() if '胜率' in group.columns else 0.0,
                '交易天数': len(group)
            }
        
        # 创建股票统计DataFrame
        stock_stats_df = pd.DataFrame.from_dict(stock_stats, orient='index')
        stock_stats_df.index.name = '股票代码'
        stock_stats_df.reset_index(inplace=True)
        
        # 保存股票统计结果
        stock_stats_csv = os.path.join(results_dir, 'stock_statistics.csv')
        stock_stats_df.to_csv(stock_stats_csv, index=False, encoding='utf-8-sig')
        logger.info(f"已创建股票统计结果到 {stock_stats_csv}")
        
        # 计算每日的统计数据
        daily_stats = []
        for date, group in combined_df.groupby('日期'):
            daily_stats.append({
                '日期': date,
                '平均收益率': group['收益率'].mean(),
                '最大收益率': group['收益率'].max(),
                '最小收益率': group['收益率'].min(),
                '平均夏普比率': group['夏普比率'].mean() if '夏普比率' in group.columns else 0.0,
                '平均胜率': group['胜率'].mean() if '胜率' in group.columns else 0.0,
                '股票数量': len(group)
            })
        
        # 创建日期统计DataFrame
        daily_stats_df = pd.DataFrame(daily_stats)
        
        # 保存日期统计结果
        daily_stats_csv = os.path.join(results_dir, 'daily_statistics.csv')
        daily_stats_df.to_csv(daily_stats_csv, index=False, encoding='utf-8-sig')
        logger.info(f"已创建日期统计结果到 {daily_stats_csv}")
        
        return True
        
    else:
        logger.warning("没有找到任何有效的CSV回测结果数据，无法生成合并统计文件")
        return False

def combine_log_analysis_results(results_dir):
    """合并所有日期的日志分析结果和CSV回测结果
    
    Args:
        results_dir: 结果保存目录
    
    Returns:
        str: 生成的汇总日志分析文件路径，如果失败则返回None
    """
    logger.info("开始合并日志分析结果和CSV回测结果...")
    
    # 存储所有日志分析结果
    all_log_analysis = []
    processed_log_dates = []
    skipped_log_dates = []
    
    # 存储所有CSV回测结果
    all_csv_results = []
    processed_csv_dates = []
    skipped_csv_dates = []
    
    # 遍历结果目录中的所有日期目录
    for date_dir in os.listdir(results_dir):
        date_path = os.path.join(results_dir, date_dir)
        if not os.path.isdir(date_path) or date_dir.startswith('backtest_'):
            continue
        
        logger.debug(f"检查日期目录: {date_dir}")
        
        # 1. 处理日志分析文件
        analysis_files = []
        if os.path.exists(date_path):
            for file in os.listdir(date_path):
                if file.startswith('trade_analysis_') and file.endswith('.xlsx'):
                    analysis_files.append(os.path.join(date_path, file))
        
        if analysis_files:
            # 使用最新的分析文件
            latest_analysis_file = max(analysis_files, key=os.path.getctime)
            
            try:
                # 读取日志分析结果
                df = pd.read_excel(latest_analysis_file, sheet_name='交易记录', engine='openpyxl')
                
                if not df.empty:
                    # 添加日期和数据来源信息
                    df['分析日期'] = date_dir
                    df['数据来源'] = '日志分析'
                    
                    # 如果没有target_date列，使用分析日期
                    if 'target_date' not in df.columns:
                        df['target_date'] = date_dir
                    
                    all_log_analysis.append(df)
                    processed_log_dates.append(date_dir)
                    logger.info(f"读取日期 {date_dir} 的日志分析结果，包含 {len(df)} 条记录")
                else:
                    logger.warning(f"日期 {date_dir} 的日志分析文件为空")
                    skipped_log_dates.append(date_dir)
                    
            except Exception as e:
                logger.error(f"读取日志分析文件 {latest_analysis_file} 时出错: {str(e)}")
                skipped_log_dates.append(date_dir)
        else:
            logger.warning(f"日期 {date_dir} 未找到日志分析文件")
            skipped_log_dates.append(date_dir)
        
        # 2. 处理CSV回测结果文件
        csv_files = []
        if os.path.exists(date_path):
            for file in os.listdir(date_path):
                if file.startswith('backtest_results_') and file.endswith('.csv'):
                    csv_files.append(os.path.join(date_path, file))
        
        if csv_files:
            # 使用最新的CSV文件，或者优先使用标准命名的文件
            target_csv = None
            # 优先查找标准命名的文件 backtest_results_YYYYMMDD.csv
            for csv_file in csv_files:
                if f'backtest_results_{date_dir}.csv' in csv_file:
                    target_csv = csv_file
                    break
            
            # 如果没找到标准命名的，使用最新的
            if not target_csv:
                target_csv = max(csv_files, key=os.path.getctime)
            
            try:
                # 读取CSV回测结果
                csv_df = pd.read_csv(target_csv, encoding='utf-8-sig')
                
                # 基本验证：检查必要的列是否存在
                required_columns = ['股票代码', '收益率']
                if not all(col in csv_df.columns for col in required_columns):
                    logger.warning(f"CSV文件 {target_csv} 缺少必要的列: {required_columns}")
                    skipped_csv_dates.append(date_dir)
                elif csv_df.empty:
                    logger.warning(f"CSV文件 {target_csv} 为空")
                    skipped_csv_dates.append(date_dir)
                else:
                    # CSV文件有效，添加到结果中
                    csv_df['回测日期'] = date_dir
                    csv_df['数据来源'] = 'CSV回测结果'
                    
                    # 统一列名，确保兼容性
                    if '股票代码' in csv_df.columns:
                        csv_df['stock_code'] = csv_df['股票代码']
                    if '收益率' in csv_df.columns:
                        csv_df['return_rate'] = csv_df['收益率']
                    if '胜率' in csv_df.columns:
                        csv_df['win_rate'] = csv_df['胜率']
                    if '夏普比率' in csv_df.columns:
                        csv_df['sharpe_ratio'] = csv_df['夏普比率']
                    if '总交易次数' in csv_df.columns:
                        csv_df['total_trades'] = csv_df['总交易次数']
                    if '盈利次数' in csv_df.columns:
                        csv_df['profit_trades'] = csv_df['盈利次数']
                    if '最终资金' in csv_df.columns:
                        csv_df['final_capital'] = csv_df['最终资金']
                    if '初始资金' in csv_df.columns:
                        csv_df['initial_capital'] = csv_df['初始资金']
                    
                    all_csv_results.append(csv_df)
                    processed_csv_dates.append(date_dir)
                    logger.info(f"读取日期 {date_dir} 的CSV回测结果，包含 {len(csv_df)} 条记录")
                    
            except Exception as e:
                logger.error(f"读取CSV文件 {target_csv} 时出错: {str(e)}")
                skipped_csv_dates.append(date_dir)
        else:
            logger.warning(f"日期 {date_dir} 未找到CSV回测结果文件")
            skipped_csv_dates.append(date_dir)
    
    # 记录处理统计
    logger.info(f"日志分析合并统计: 成功读取 {len(processed_log_dates)} 个日期，跳过 {len(skipped_log_dates)} 个日期")
    logger.info(f"CSV回测结果合并统计: 成功读取 {len(processed_csv_dates)} 个日期，跳过 {len(skipped_csv_dates)} 个日期")
    
    if processed_log_dates:
        logger.info(f"成功处理的日志分析日期: {', '.join(processed_log_dates)}")
    if processed_csv_dates:
        logger.info(f"成功处理的CSV回测日期: {', '.join(processed_csv_dates)}")
    
    # 如果没有任何数据，返回None
    if not all_log_analysis and not all_csv_results:
        logger.warning("没有找到任何日志分析结果或CSV回测结果，无法生成合并文件")
        return None
    
    # 生成汇总文件路径
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    combined_analysis_file = os.path.join(results_dir, f'combined_analysis_results_{timestamp}.xlsx')
    
    # 保存合并后的结果到Excel文件（包含多个工作表）
    try:
        with pd.ExcelWriter(combined_analysis_file, engine='openpyxl') as writer:
            
            # 1. CSV回测结果汇总表（新增的主要sheet页）
            if all_csv_results:
                combined_csv_df = pd.concat(all_csv_results, ignore_index=True)
                combined_csv_df.to_excel(writer, sheet_name='CSV回测结果汇总', index=False)
                logger.info(f"CSV回测结果汇总: {len(combined_csv_df)} 条记录")
                
                # 基于CSV数据计算日期统计
                csv_date_stats = []
                for date in combined_csv_df['回测日期'].unique():
                    date_df = combined_csv_df[combined_csv_df['回测日期'] == date]
                    
                    # 使用CSV中的字段进行统计
                    total_stocks = len(date_df)
                    avg_return = date_df['return_rate'].mean() if 'return_rate' in date_df.columns else date_df.get('收益率', pd.Series([0])).mean()
                    max_return = date_df['return_rate'].max() if 'return_rate' in date_df.columns else date_df.get('收益率', pd.Series([0])).max()
                    min_return = date_df['return_rate'].min() if 'return_rate' in date_df.columns else date_df.get('收益率', pd.Series([0])).min()
                    avg_win_rate = date_df['win_rate'].mean() if 'win_rate' in date_df.columns else date_df.get('胜率', pd.Series([0])).mean()
                    avg_sharpe = date_df['sharpe_ratio'].mean() if 'sharpe_ratio' in date_df.columns else date_df.get('夏普比率', pd.Series([0])).mean()
                    total_trades = date_df['total_trades'].sum() if 'total_trades' in date_df.columns else date_df.get('总交易次数', pd.Series([0])).sum()
                    
                    csv_date_stats.append({
                        '日期': date,
                        '股票数量': total_stocks,
                        '平均收益率(%)': round(avg_return, 4),
                        '最高收益率(%)': round(max_return, 4),
                        '最低收益率(%)': round(min_return, 4),
                        '平均胜率(%)': round(avg_win_rate, 2),
                        '平均夏普比率': round(avg_sharpe, 4),
                        '总交易次数': int(total_trades),
                        '数据来源': 'CSV回测结果'
                    })
                
                csv_date_stats_df = pd.DataFrame(csv_date_stats).sort_values('日期')
                csv_date_stats_df.to_excel(writer, sheet_name='日期统计(基于CSV)', index=False)
                
                # 基于CSV数据计算股票统计  
                csv_stock_stats = []
                for stock_code in combined_csv_df['stock_code'].unique():
                    stock_df = combined_csv_df[combined_csv_df['stock_code'] == stock_code]
                    
                    trading_days = len(stock_df)
                    avg_return = stock_df['return_rate'].mean() if 'return_rate' in stock_df.columns else stock_df.get('收益率', pd.Series([0])).mean()
                    max_return = stock_df['return_rate'].max() if 'return_rate' in stock_df.columns else stock_df.get('收益率', pd.Series([0])).max()
                    min_return = stock_df['return_rate'].min() if 'return_rate' in stock_df.columns else stock_df.get('收益率', pd.Series([0])).min()
                    avg_win_rate = stock_df['win_rate'].mean() if 'win_rate' in stock_df.columns else stock_df.get('胜率', pd.Series([0])).mean()
                    avg_sharpe = stock_df['sharpe_ratio'].mean() if 'sharpe_ratio' in stock_df.columns else stock_df.get('夏普比率', pd.Series([0])).mean()
                    total_trades = stock_df['total_trades'].sum() if 'total_trades' in stock_df.columns else stock_df.get('总交易次数', pd.Series([0])).sum()
                    
                    csv_stock_stats.append({
                        '股票代码': stock_code,
                        '回测天数': trading_days,
                        '平均收益率(%)': round(avg_return, 4),
                        '最高收益率(%)': round(max_return, 4),
                        '最低收益率(%)': round(min_return, 4),
                        '平均胜率(%)': round(avg_win_rate, 2),
                        '平均夏普比率': round(avg_sharpe, 4),
                        '总交易次数': int(total_trades),
                        '数据来源': 'CSV回测结果'
                    })
                
                csv_stock_stats_df = pd.DataFrame(csv_stock_stats).sort_values('平均收益率(%)', ascending=False)
                csv_stock_stats_df.to_excel(writer, sheet_name='股票统计(基于CSV)', index=False)
            
            # 2. 日志分析结果汇总表（保持原有功能）
            if all_log_analysis:
                combined_log_df = pd.concat(all_log_analysis, ignore_index=True)
                combined_log_df.to_excel(writer, sheet_name='日志分析结果汇总', index=False)
                logger.info(f"日志分析结果汇总: {len(combined_log_df)} 条记录")
                
                # 只对已执行的交易进行统计分析
                executed_df = combined_log_df[combined_log_df.get('execution_status', '') == '已执行'].copy()
                
                if not executed_df.empty:
                    # 基于日志分析数据的日期统计
                    log_date_stats = []
                    for date in executed_df['分析日期'].unique():
                        date_df = executed_df[executed_df['分析日期'] == date]
                        
                        profit_trades = len(date_df[date_df['profit_loss'] > 0])
                        total_trades = len(date_df)
                        win_rate = (profit_trades / total_trades * 100) if total_trades > 0 else 0
                        
                        log_date_stats.append({
                            '日期': date,
                            '交易次数': total_trades,
                            '盈利次数': profit_trades,
                            '胜率(%)': round(win_rate, 2),
                            '总盈亏': round(date_df['profit_loss'].sum(), 2),
                            '平均收益率(%)': round(date_df['profit_rate'].mean(), 2),
                            '最大收益率(%)': round(date_df['profit_rate'].max(), 2),
                            '最小收益率(%)': round(date_df['profit_rate'].min(), 2),
                            '涉及股票数': date_df['stock_code'].nunique(),
                            '数据来源': '日志分析'
                        })
                    
                    log_date_stats_df = pd.DataFrame(log_date_stats).sort_values('日期')
                    log_date_stats_df.to_excel(writer, sheet_name='日期统计(基于日志)', index=False)
                    
                    # 基于日志分析数据的股票统计
                    log_stock_stats = []
                    for stock_code in executed_df['stock_code'].unique():
                        stock_df = executed_df[executed_df['stock_code'] == stock_code]
                        
                        profit_trades = len(stock_df[stock_df['profit_loss'] > 0])
                        total_trades = len(stock_df)
                        win_rate = (profit_trades / total_trades * 100) if total_trades > 0 else 0
                        
                        log_stock_stats.append({
                            '股票代码': stock_code,
                            '交易次数': total_trades,
                            '盈利次数': profit_trades,
                            '胜率(%)': round(win_rate, 2),
                            '总盈亏': round(stock_df['profit_loss'].sum(), 2),
                            '平均收益率(%)': round(stock_df['profit_rate'].mean(), 2),
                            '涉及日期数': stock_df['分析日期'].nunique(),
                            '数据来源': '日志分析'
                        })
                    
                    log_stock_stats_df = pd.DataFrame(log_stock_stats).sort_values('总盈亏', ascending=False)
                    log_stock_stats_df.to_excel(writer, sheet_name='股票统计(基于日志)', index=False)
                
                # 执行状态统计
                execution_stats = []
                total_signals = len(combined_log_df)
                executed_count = len(executed_df)
                unexecuted_count = len(combined_log_df[combined_log_df.get('execution_status', '') == '未执行'])
                execution_rate = (executed_count / total_signals * 100) if total_signals > 0 else 0
                
                execution_stats.append({
                    '统计项目': '总体执行情况',
                    '总信号数': total_signals,
                    '已执行': executed_count,
                    '未执行': unexecuted_count,
                    '执行率(%)': round(execution_rate, 2),
                    '说明': f'批量回测期间共{total_signals}个信号，执行{executed_count}个'
                })
                
                execution_stats_df = pd.DataFrame(execution_stats)
                execution_stats_df.to_excel(writer, sheet_name='执行统计', index=False)
            
            # 3. 数据来源说明表
            source_info = [
                {
                    'Sheet页名称': 'CSV回测结果汇总',
                    '数据来源': '每个日期目录下的backtest_results_YYYYMMDD.csv文件',
                    '说明': '包含回测的最终结果统计，如收益率、夏普比率、胜率等',
                    '统计意义': '反映策略在该日期的整体表现'
                },
                {
                    'Sheet页名称': '日期统计(基于CSV)',
                    '数据来源': 'CSV回测结果汇总',
                    '说明': '基于CSV数据按日期统计的汇总信息',
                    '统计意义': '查看每个日期的回测表现，比较不同日期的策略效果'
                },
                {
                    'Sheet页名称': '股票统计(基于CSV)',
                    '数据来源': 'CSV回测结果汇总',
                    '说明': '基于CSV数据按股票统计的汇总信息',
                    '统计意义': '查看每只股票在多个日期中的综合表现'
                },
                {
                    'Sheet页名称': '日志分析结果汇总',
                    '数据来源': '每个日期目录下的trade_analysis_*.xlsx文件',
                    '说明': '包含详细的交易信号和执行记录',
                    '统计意义': '分析具体的交易过程和信号执行情况'
                },
                {
                    'Sheet页名称': '日期统计(基于日志)',
                    '数据来源': '日志分析结果汇总',
                    '说明': '基于日志数据按日期统计的详细交易信息',
                    '统计意义': '查看每个日期的具体交易执行情况'
                },
                {
                    'Sheet页名称': '股票统计(基于日志)',
                    '数据来源': '日志分析结果汇总',
                    '说明': '基于日志数据按股票统计的详细交易信息',
                    '统计意义': '查看每只股票的具体交易记录和盈亏情况'
                }
            ]
            
            source_info_df = pd.DataFrame(source_info)
            source_info_df.to_excel(writer, sheet_name='数据来源说明', index=False)
            
            # 格式化所有工作表
            for sheet_name in writer.sheets:
                worksheet = writer.sheets[sheet_name]
                
                # 自动调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
        
        logger.info(f"已保存合并的分析结果到 {combined_analysis_file}")
        logger.info("包含以下sheet页:")
        if all_csv_results:
            logger.info("  - CSV回测结果汇总 (主要的回测结果数据)")
            logger.info("  - 日期统计(基于CSV) (基于CSV数据的日期维度统计)")
            logger.info("  - 股票统计(基于CSV) (基于CSV数据的股票维度统计)")
        if all_log_analysis:
            logger.info("  - 日志分析结果汇总 (详细的交易信号和执行记录)")
            logger.info("  - 日期统计(基于日志) (基于日志数据的日期维度统计)")
            logger.info("  - 股票统计(基于日志) (基于日志数据的股票维度统计)")
            logger.info("  - 执行统计 (信号执行情况统计)")
        logger.info("  - 数据来源说明 (各sheet页的数据来源和含义)")
        
        return combined_analysis_file
        
    except Exception as e:
        logger.error(f"保存合并分析结果时出错: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return None

def run_single_date_backtest(date_str):
    """运行单一日期回测
    
    Args:
        date_str: 格式为YYYYMMDD的日期字符串
    """
    # 记录开始时间
    start_time = time.time()
    
    # 确保logs目录存在
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 确保results目录存在
    results_base_dir = 'results'
    if not os.path.exists(results_base_dir):
        os.makedirs(results_base_dir)
    
    # 创建本次运行的结果目录
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = os.path.join(results_base_dir, f'backtest_{timestamp}')
    os.makedirs(results_dir)
    logger.info(f"创建结果目录: {results_dir}")
    
    # 复制当前配置文件到结果目录
    if os.path.exists('config.yaml'):
        shutil.copy2('config.yaml', os.path.join(results_dir, 'config.yaml'))
        logger.info("已复制配置文件到结果目录")
    else:
        logger.warning("未找到配置文件 config.yaml")
    
    # 检查是否为交易日
    if not is_trading_day(date_str):
        logger.warning(f"日期 {date_str} 不是交易日，无法进行回测")
        return
    
    logger.info(f"对日期 {date_str} 进行回测")
    
    # 保存原始配置
    original_config = load_config()
    
    try:
        # 更新配置文件中的日期
        config = update_config_dates(date_str)
        
        # 创建该日期的结果目录
        date_results_dir = os.path.join(results_dir, date_str)
        os.makedirs(date_results_dir, exist_ok=True)
        
        # 运行select_stocks脚本，传递目标日期参数和图片保存路径，以及日志保存路径
        logger.info(f"运行股票选择脚本，指定日期: {date_str}...")
        select_stocks_cmd = [
            'python', 'select_stocks.py', 
            '--date', date_str,
            '--img-dir', date_results_dir,
            '--log-dir', date_results_dir
        ]
        
        # 调用子进程运行select_stocks.py
        subprocess.run(select_stocks_cmd, check=True)
        
        # 回测完成后，将CSV结果文件移动到日期结果目录
        # 查找最新的CSV文件（现在从results目录查找）
        csv_files = []
        # 检查results目录中的CSV文件
        if os.path.exists('results'):
            csv_files.extend([os.path.join('results', f) for f in os.listdir('results') 
                            if f.endswith('.csv') and (f.startswith('backtest_results_') or f == 'multi_stock_backtest_results.csv')])
        
        # 为了兼容性，也检查根目录（如果有遗留文件）
        csv_files.extend([f for f in os.listdir('.') 
                        if f.endswith('.csv') and (f.startswith('backtest_results_') or f == 'multi_stock_backtest_results.csv')])
        
        if csv_files:
            latest_csv = max(csv_files, key=os.path.getctime)
            # 重命名并复制到日期目录
            new_name = f'backtest_results_{date_str}.csv'
            shutil.copy2(latest_csv, os.path.join(date_results_dir, new_name))
            # 也复制到结果根目录
            shutil.copy2(latest_csv, os.path.join(results_dir, new_name))
            logger.info(f"已将回测结果复制到 {os.path.join(date_results_dir, new_name)} 和 {os.path.join(results_dir, new_name)}")
        else:
            logger.warning("未找到回测结果CSV文件")
        
        logger.info(f"单一日期 {date_str} 回测完成")
        
        # 记录结束时间和计算用时
        end_time = time.time()
        elapsed_time = end_time - start_time
        logger.info(f"单日回测用时: {elapsed_time:.2f}秒")
        
        # 运行日志分析（如果启用）
        config = load_config()
        if config['backtest'].get('enable_log_analysis', True):
            try:
                # 添加短暂延迟，确保所有日志文件写入完成
                time.sleep(2)
                
                # 使用新的集成接口运行日志分析
                from log_analyzer import run_integrated_analysis
                logger.info("开始运行单日回测日志分析...")
                
                # 运行分析
                output_path = run_integrated_analysis(
                    source_script='run_batch_backtest',
                    results_dir=date_results_dir,
                    date_filter=date_str
                )
                
                if output_path:
                    logger.info(f"日志分析完成: {output_path}")
                else:
                    logger.warning("日志分析未生成文件")
                        
            except ImportError as e:
                logger.error(f"无法导入日志分析模块: {str(e)}")
            except Exception as e:
                logger.error(f"运行日志分析时出错: {str(e)}")
                import traceback
                logger.error(f"详细错误信息: {traceback.format_exc()}")
        else:
            logger.info("日志分析功能已禁用，跳过")
        
        # 发送回测完成通知
        stats = {}
        
        # 如果找到CSV结果文件，添加到通知中
        csv_file = os.path.join(date_results_dir, f'backtest_results_{date_str}.csv')
        if os.path.exists(csv_file):
            try:
                df = pd.read_csv(csv_file, encoding='utf-8-sig')
                stats["股票数量"] = len(df)
                stats["平均收益率"] = df['收益率'].mean()
                stats["最高收益率"] = df['收益率'].max()
                stats["最低收益率"] = df['收益率'].min()
                
                # 获取表现最好的股票
                if not df.empty:
                    best_stock = df.loc[df['收益率'].idxmax()]
                    stats["表现最佳股票"] = f"{best_stock['股票代码']} (收益率: {best_stock['收益率']:.2f}%)"
            except Exception as e:
                logger.error(f"读取回测结果时出错: {str(e)}")
        
        WechatNotifier.send_backtest_notification(
            backtest_type='single',
            date_range=date_str,
            results_dir=results_dir,
            stats=stats,
            elapsed_time=elapsed_time
        )
    
    except Exception as e:
        logger.error(f"单一日期回测时出错: {str(e)}")
    finally:
        # 恢复原始配置
        save_config(original_config)
        logger.info("已恢复原始配置")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='批量回测脚本')
    parser.add_argument('--start_date', type=str, help='开始日期 (YYYYMMDD)')
    parser.add_argument('--end_date', type=str, help='结束日期 (YYYYMMDD)')
    parser.add_argument('--days', type=int, default=30, help='当未指定开始日期时，从结束日期回溯的天数')
    parser.add_argument('--date', type=str, help='单一日期回测 (YYYYMMDD)')
    parser.add_argument('--no-cache', action='store_true', help='不使用选股缓存，强制重新选股')
    parser.add_argument('--serial', action='store_true', help='使用串行回测模式（默认使用多进程并行回测）')
    parser.add_argument('--max-processes', type=int, default=None, 
                        help='最大进程数，默认为10（建议不超过CPU核心数以避免资源过度占用）')
    
    args = parser.parse_args()
    
    # 验证参数
    if args.max_processes is not None and args.max_processes < 1:
        logger.error("最大进程数必须大于0")
        sys.exit(1)
    
    if args.serial and args.date:
        logger.info("单日回测默认使用单进程模式")
    
    # 处理是否使用缓存的选项
    if args.no_cache:
        # 临时修改配置，不使用缓存
        config = load_config()
        original_cache_setting = config['backtest'].get('use_stock_cache', True)
        config['backtest']['use_stock_cache'] = False
        save_config(config)
        
        try:
            if args.date:
                # 单一日期回测
                run_single_date_backtest(args.date)
            elif args.serial:
                # 串行回测
                logger.info("🔄 启动串行回测模式")
                run_batch_backtest(args.start_date, args.end_date, args.days)
            else:
                # 多进程并行回测
                logger.info("🚀 启动多进程并行回测模式")
                run_parallel_batch_backtest(args.start_date, args.end_date, args.days, args.max_processes)
        finally:
            # 恢复原始配置
            config = load_config()
            config['backtest']['use_stock_cache'] = original_cache_setting
            save_config(config)
    else:
        if args.date:
            # 单一日期回测
            run_single_date_backtest(args.date)
        elif args.serial:
            # 串行回测
            logger.info("🔄 启动串行回测模式")
            run_batch_backtest(args.start_date, args.end_date, args.days)
        else:
            # 多进程并行回测
            logger.info("🚀 启动多进程并行回测模式")
            run_parallel_batch_backtest(args.start_date, args.end_date, args.days, args.max_processes) 