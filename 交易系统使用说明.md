# 可转债自动交易系统使用说明

## 系统概述

本系统是一个完整的可转债自动选股和交易系统，集成了股票选择、交易执行、进程管理和实时监控功能。

## 快速启动

### 方法一：使用启动脚本（推荐）

1. **双击运行** `启动自动交易系统.bat`
2. **选择模式**：
   - `[1] 完整模式` - 自动选股 + 启动交易（推荐）
   - `[2] 仅选股模式` - 只执行选股，不启动交易
   - `[3] 指定日期选股` - 为特定日期执行选股
   - `[4] 查看交易状态` - 查看当前运行的交易进程
   - `[5] 停止所有交易` - 停止所有运行中的交易进程

### 方法二：命令行启动

```bash
# 完整模式（选股 + 交易）
python auto_trading.py

# 仅选股模式
python auto_trading.py --select-only

# 指定日期选股
python auto_trading.py --date 20241201 --select-only

# 手动启动交易进程
python simple_trade_manager.py --init
```

## 系统管理命令

### 查看交易状态
```bash
# 查看活跃股票列表
python trade_cli.py list

# 查看具体股票状态
python trade_cli.py status 127081.SZ

# 测试系统状态
python test_trading_status.py
```

### 进程管理
```bash
# 添加新股票
python trade_cli.py add 127081.SZ --initial-cash 100000

# 移除股票
python trade_cli.py remove 127081.SZ

# 停止所有交易
python trade_cli.py stop-all

# 从配置文件初始化
python trade_cli.py init
```

### 实时监控
```bash
# 启动实时监控界面
python monitor_trading.py
```

## 系统状态检查

### 1. 检查交易进程是否运行
```bash
python trade_cli.py list
```
**预期输出**：显示当前交易的股票列表，如：
```
当前正在交易的股票：
- 127081.SZ(中旗转债)
- 128144.SZ(利民转债)
- 123056.SZ(雪榕转债)
```

### 2. 检查具体股票状态
```bash
python trade_cli.py status 127081.SZ
```
**预期输出**：显示股票的详细状态，包括进程ID等信息

### 3. 运行系统测试
```bash
python test_trading_status.py
```
**预期输出**：完整的系统状态检查报告

## 日志文件说明

系统会在 `logs/` 目录下生成以下类型的日志文件：

- `strategy_<股票代码>_<时间戳>.log` - 策略执行日志
- `qmtbroker_<股票代码>_<时间戳>.log` - QMT经纪商日志
- `qmtfeed_<股票代码>_<时间戳>.log` - 数据源日志
- `backtest_<股票代码>_trade_<时间戳>.log` - 交易执行日志
- `simple_trade_manager_<时间戳>.log` - 交易管理器日志
- `trade_cli_<时间戳>.log` - 命令行工具日志

## 故障排除

### 问题1：`python trade_cli.py list` 显示"当前没有正在交易的股票"

**原因**：交易进程未启动或已停止

**解决方案**：
1. 重新启动交易进程：
   ```bash
   python simple_trade_manager.py --init
   ```
2. 检查配置文件 `config.yaml` 中的 `is_live` 设置是否为 `true`
3. 确保QMT软件正常运行

### 问题2：交易进程启动失败

**原因**：QMT连接问题或配置错误

**解决方案**：
1. 检查QMT软件是否正常运行
2. 验证QMT账户配置
3. 查看相关日志文件中的错误信息
4. 确保股票代码格式正确（如：127081.SZ）

### 问题3：选股失败

**原因**：网络连接问题或数据源异常

**解决方案**：
1. 检查网络连接
2. 重新运行选股：
   ```bash
   python select_stocks.py
   ```
3. 查看选股日志文件

### 问题4：进程意外停止

**原因**：系统资源不足或QMT连接中断

**解决方案**：
1. 检查系统资源使用情况
2. 重启QMT软件
3. 重新启动交易进程：
   ```bash
   python trade_cli.py stop-all
   python simple_trade_manager.py --init
   ```

## 配置文件说明

主要配置文件：`config.yaml`

关键配置项：
- `backtest.is_live`: 是否为实盘模式（true/false）
- `backtest.initial_cash`: 初始资金
- `backtest.stock_codes`: 交易的股票代码列表
- `strategy.*`: 策略参数配置

## 安全提示

1. **实盘交易风险**：系统在实盘模式下会执行真实交易，请确保策略参数合理
2. **资金管理**：建议设置合理的初始资金和仓位限制
3. **监控重要性**：建议定期检查交易状态和日志
4. **备份配置**：重要配置文件建议定期备份

## 技术支持

如遇到问题，请：
1. 查看相关日志文件
2. 运行 `python test_trading_status.py` 进行系统诊断
3. 检查QMT软件状态
4. 验证网络连接和数据源

---

**注意**：本系统仅供学习和研究使用，实盘交易存在风险，请谨慎使用。 