# 实盘模式下order.executed.price更新机制详解

## 1. 整体架构

```
Strategy (策略层)
    ↓ 下单请求
QMTBroker (broker层)
    ↓ 实盘订单
XtQuantTrader (QMT交易接口)
    ↓ API调用
券商服务器
    ↓ 市场订单
交易所
    ↓ 成交回报
券商服务器
    ↓ 成交推送
MyXtQuantTraderCallback (回调处理)
    ↓ 价格更新
QMTBroker.update_executed_price()
    ↓ 通知策略
Strategy.notify_order()
```

## 2. 核心组件说明

### 2.1 MyXtQuantTraderCallback 回调类

这是接收券商实时推送的核心类，主要回调方法：

- `on_stock_trade(trade)`: **最关键**，接收成交回报，包含真实执行价格
- `on_stock_order(order)`: 接收订单状态变化
- `on_order_error(order_error)`: 处理订单错误
- `on_order_stock_async_response(response)`: 异步下单响应

### 2.2 订单映射机制

```python
# QMTBroker中的映射关系
self.pending_orders = {}        # BT订单ID -> BT订单对象
self.bt_order_mapping = {}      # BT订单ID -> QMT订单ID
self.order_mapping = {}         # QMT订单ID -> BT订单对象
```

### 2.3 订单备注标识

通过订单备注建立BT订单与QMT订单的关联：
```python
order_remark = f"三重共振_{bt_order.ref}"
```

## 3. 价格更新流程详解

### 3.1 下单阶段

1. **策略发起订单**：
   ```python
   self.order = self.buy(size=size, price=self.dataclose[0])
   ```

2. **QMTBroker处理**：
   ```python
   def buy(self, owner, data, **kwargs):
       # 创建BT订单
       bt_order = super().buy(owner, data, **kwargs)
       
       # 生成包含BT订单ID的备注
       order_remark = self._generate_order_remark(bt_order)
       
       # 添加到待处理订单
       self.pending_orders[bt_order.ref] = bt_order
       
       # 提交到QMT
       self.xt_trader.order_stock_async(
           self.account, data._dataname, xtconstant.STOCK_BUY,
           buy_size, price_type, price, order_remark, '买入'
       )
   ```

### 3.2 成交回报阶段

1. **券商推送成交信息**：
   ```python
   def on_stock_trade(self, trade):
       # trade对象包含：
       # - trade.traded_price: 真实成交价格
       # - trade.traded_volume: 成交数量
       # - trade.order_remark: 包含BT订单ID的备注
       
       # 更新BT订单的执行价格
       self.broker_instance.update_executed_price(trade)
   ```

2. **更新执行价格**：
   ```python
   def update_executed_price(self, trade):
       # 从备注中提取BT订单ID
       bt_order_id = self._extract_bt_order_id(trade.order_remark)
       bt_order = self.pending_orders[bt_order_id]
       
       # 创建或更新执行信息
       if not hasattr(bt_order, 'executed'):
           bt_order.executed = OrderExecuted()
       
       # 更新真实执行价格和数量
       bt_order.executed.price = trade.traded_price    # 关键！
       bt_order.executed.size = trade.traded_volume
       bt_order.executed.value = trade.traded_price * trade.traded_volume
       bt_order.executed.comm = self._calculate_commission(...)
       
       # 更新订单状态
       bt_order.status = bt_order.Completed
       
       # 通知策略
       self._notify_order_executed(bt_order)
   ```

### 3.3 策略接收通知

```python
def notify_order(self, order):
    if order.status == order.Completed:
        # 此时order.executed.price已经是真实的成交价格
        real_price = order.executed.price
        self.log(f'订单执行, 价格: {real_price:.4f}')
```

## 4. 关键技术点

### 4.1 订单ID映射

通过订单备注字段建立BT订单与QMT订单的双向映射：

```python
# 生成备注
def _generate_order_remark(self, bt_order):
    return f"三重共振_{bt_order.ref}"

# 提取BT订单ID
def _extract_bt_order_id(self, order_remark):
    if '_' in order_remark:
        return order_remark.split('_')[-1]
    return order_remark
```

### 4.2 异步回调处理

QMT的回调是异步的，需要正确处理：

```python
# 回调类需要持有broker实例的引用
callback = MyXtQuantTraderCallback(self.logger, self)
xt_trader.register_callback(callback)
```

### 4.3 订单状态同步

QMT订单状态与BT订单状态的映射：
| 枚举变量名                       | 值   | 含义                                     |
| -------------------------------- | ---- | ---------------------------------------- |
| xtconstant.ORDER_UNREPORTED      | 48   | 未报                                     |
| xtconstant.ORDER_WAIT_REPORTING  | 49   | 待报                                     |
| xtconstant.ORDER_REPORTED        | 50   | 已报                                     |
| xtconstant.ORDER_REPORTED_CANCEL | 51   | 已报待撤                                 |
| xtconstant.ORDER_PARTSUCC_CANCEL | 52   | 部成待撤                                 |
| xtconstant.ORDER_PART_CANCEL     | 53   | 部撤（已经有一部分成交，剩下的已经撤单） |
| xtconstant.ORDER_CANCELED        | 54   | 已撤                                     |
| xtconstant.ORDER_PART_SUCC       | 55   | 部成（已经有一部分成交，剩下的待成交）   |
| xtconstant.ORDER_SUCCEEDED       | 56   | 已成                                     |
| xtconstant.ORDER_JUNK            | 57   | 废单                                     |
| xtconstant.ORDER_UNKNOWN         | 255  | 未知                                     |
```python
# QMT状态码 -> BT状态
qmt_status_mapping = {
    50: order.Submitted,    # 已报
    55: order.Partial,      # 部分成交
    56: order.Completed,    # 全部成交
    53、54: order.Canceled,     # 已撤
    57: order.Rejected      # 拒绝
}
```

## 5. 实际使用示例

### 5.1 策略中获取真实执行价格

```python
def notify_order(self, order):
    if order.status == order.Completed:
        if order.isbuy():
            # 获取真实买入价格
            self.buyprice = order.executed.price  # 这是真实成交价
            self.buycomm = order.executed.comm
            
            self.log(f'买入成交: 价格={order.executed.price:.4f}, '
                   f'数量={order.executed.size}, '
                   f'金额={order.executed.value:.2f}')
        else:
            # 计算真实收益
            real_pnl = (order.executed.price - self.buyprice) / self.buyprice
            self.log(f'卖出成交: 价格={order.executed.price:.4f}, '
                   f'收益率={real_pnl:.2%}')
```

### 5.2 价格滑点分析

```python
def notify_order(self, order):
    if order.status == order.Completed:
        # 比较预期价格与实际成交价格
        expected_price = self.dataclose[0]  # 策略下单时的价格
        actual_price = order.executed.price  # 真实成交价格
        slippage = (actual_price - expected_price) / expected_price
        
        self.log(f'价格滑点: 预期={expected_price:.4f}, '
               f'实际={actual_price:.4f}, '
               f'滑点={slippage:.4%}')
```

## 6. 注意事项

### 6.1 网络延迟

- 回调可能有延迟，需要考虑时序问题
- 建议在策略中添加超时处理机制

### 6.2 部分成交

- 大单可能分多次成交
- 需要累计计算平均成交价格

### 6.3 错误处理

- 网络断线、订单被拒绝等异常情况
- 需要完善的错误处理和恢复机制

### 6.4 日志记录

- 详细记录订单映射和价格更新过程
- 便于调试和问题排查

## 7. 优势

1. **真实价格**：获得真实的市场成交价格，而非理论价格
2. **实时更新**：通过回调机制实时更新，延迟极低
3. **完整信息**：包含成交价格、数量、手续费等完整信息
4. **状态同步**：订单状态与券商系统完全同步

这套机制确保了在实盘交易中，`order.executed.price`反映的是真实的市场成交价格，为策略提供准确的交易反馈。 