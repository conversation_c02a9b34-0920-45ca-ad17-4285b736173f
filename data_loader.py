import backtrader as bt
import pandas as pd
from datetime import datetime
from xtquant import xtdata
import time


class MiniQMTData(bt.feeds.PandasDirectData):
    lines = ('amount',)  # 声明 amount 字段
    
    params = (
        ('time', 0),         # 时间列（如果与datetime分开）
        ('open', 2),         # 开盘价列
        ('high', 3),         # 最高价列
        ('low', 4),          # 最低价列
        ('close', 1),        # 收盘价列（lastPrice）
        ('volume', 7),       # 成交量列
        ('amount', 6),       # 成交总额列
        ('openinterest', -1),  # 持仓量列（未使用）
        ('ask_price', 12),    # 卖价列
        ('bid_price', 13),    # 买价列
        ('ask_vol', 14),      # 卖量列
        ('bid_vol', 15),      # 买量列
        ('timeframe', bt.TimeFrame.Seconds), #秒级数据
    )

## 使用处理后的市场数据加载
class MiniQMTData2(bt.feeds.PandasDirectData):
    lines = ('amount', 'volume_cum', 'amount_cum', 'avg_price',)  # 声明 amount, volume_cum, amount_cum 字段
    
    params = (
        ('close', 1),
        ('low', 2),
        ('high', 3),
        ('open', 4),
        ('volume', 5),
        ('amount', 6),
        ('openinterest', -1),  # 持仓量列（未使用）
        ('datetime', 0),
        ('volume_cum', 7),
        ('amount_cum', 8),
        ('avg_price', 9),
        ('timeframe', bt.TimeFrame.Seconds), #秒级数据
    )
    

def load_miniqmt_data(stock_code, start_time, end_time):
    """
    通过xdata接口加载miniqmt的3s级数据
    
    参数:
    stock_code: str, 股票代码
    start_time: str, 开始时间，格式：'YYYYMMDDHHMMSS'
    end_time: str, 结束时间，格式：'YYYYMMDDHHMMSS'
    """
    rs = xtdata.download_history_data(stock_code, 'tick', start_time=start_time, end_time=end_time)
    # 调用xdata接口获取数据
    data = xtdata.get_market_data_ex(
        field_list=[],
        stock_list=[stock_code],
        period='tick',
        start_time=start_time,
        end_time=end_time,
        count=-1,
        dividend_type='none',
        fill_data=True
    )
    
    # 转换为DataFrame
    raw_df = data[stock_code]
    print('原始数据长度：', len(raw_df))
    
    # raw_df.to_csv('tick_data_{}.csv'.format(stock_code), index=False)
    df = process_market_data(raw_df)
    # df.to_csv('tick_data_processed_{}.csv'.format(stock_code), index=False)
    df = pd.DataFrame(df[df['close'] > 0])
    # 打印数据长度
    print('数据长度：', len(df))
    
    # # 转换时间戳
    # df['time'] = pd.to_datetime(df.index)
    
    # 设置时间戳为索引
    df.set_index('datetime', inplace=True)
    df = df[start_time:end_time]

    return df 

## 从本地加载miniqmt数据,从本地对象序列化文件中加载
def load_miniqmt_data_from_pickle(start_time, end_time):
    import joblib
    raw_df = joblib.load('his_tick_data_copy.pkl')
    df = process_market_data(raw_df)
    df.set_index('datetime', inplace=True)
    df = df[start_time:end_time]
    return df


def load_miniqmt_data_from_hd5(stock_code, start_time, end_time):
    ## 从本地加载数据到内存
    ## 开始和结束时间必须在同一个交易日
    start_date = datetime.strptime(start_time[:8], '%Y%m%d')
    end_date = datetime.strptime(end_time[:8], '%Y%m%d')
    if start_date.date() != end_date.date():
        print(f"开始和结束时间必须在同一个交易日: {start_time} 和 {end_time}")
        return pd.DataFrame()

    date_str = start_time[:8]
    new_code = f"{stock_code.split('.')[-1]}{stock_code.split('.')[0]}"
    file_path = 'F:\data\{date_str}\{new_code}.h5'
    
    import os
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return pd.DataFrame()
    store = pd.HDFStore(file_path)
    df = store[new_code]

    print('原始数据长度：', len(df))
    
    # raw_df.to_csv('tick_data_{}.csv'.format(stock_code), index=False)
    df = process_market_data(df)
    # df.to_csv('tick_data_processed_{}.csv'.format(stock_code), index=False)
    df = pd.DataFrame(df[df['close'] > 0])
    # 打印数据长度
    print('数据长度：', len(df))
    
    # # 转换时间戳
    # df['time'] = pd.to_datetime(df.index)
    
    # 设置时间戳为索引
    df.set_index('datetime', inplace=True)
    df = df[start_time:end_time]
    store.close()

    return df

## 创建实时数据源
class MyQuantData(bt.feed.DataBase):
    params = (
        ('timeframe', bt.TimeFrame.Seconds),
    )
    conn = None

    # lines = ('amount', 'volume_cum', 'amount_cum')

    def islive(self):
        return True

    def start(self):
        super(MyQuantData, self).start()
        self.resample(timeframe=bt.TimeFrame.Seconds, compression=1)
        if self.conn is None:
            raise Exception("数据管道连接失败!")

    def stop(self):
        super(MyQuantData, self).stop()
        if self.conn is not None:
            self.conn.close()
            self.conn = None

    def _load(self):
        if self.conn is None:
            print("self.conn is None")
            return False
        
        self_bar = self.conn.recv()
        print("recv bar", self_bar)
        self.lines.datetime[0] = bt.utils.date2num(pd.to_datetime(datetime.fromtimestamp(self_bar['time']/1000)))
        self.lines.open[0] = self_bar['open']
        self.lines.high[0] = self_bar['high']
        self.lines.low[0] = self_bar['low']
        self.lines.close[0] = self_bar['close']
        self.lines.volume[0] = self_bar['volume']
        self.lines.openinterest[0] = -1
        self.lines.amount[0] = self_bar['amount']
        return True


## 创建数据获取方法
def get_my_quant_data(conn):
    data = MyQuantData()
    data.conn = conn
    return data


## 使用miniqmt创建模拟实时数据源
def get_miniqmt_data(stock_code, start_time, end_time, conn):
    """
    参数:
    stock_code: str, 股票代码
    start_time: str, 开始时间，格式：'YYYYMMDDHHMMSS'
    end_time: str, 结束时间，格式：'YYYYMMDDHHMMSS'
    conn: 数据管道连接
    """
    rs = xtdata.download_history_data(stock_code, 'tick', start_time=start_time, end_time=end_time)
    # 调用xdata接口获取数据
    data = xtdata.get_market_data_ex(
        field_list=[],
        stock_list=[stock_code],
        period='tick',
        start_time=start_time,
        end_time=end_time,
        count=-1,
        dividend_type='none',
        fill_data=True
    )
    
    # 转换为DataFrame
    raw_df = data[stock_code]
    df = process_market_data(raw_df)
    # 遍历DataFrame的每一行并转换为字典发送
    for index, row in df.iterrows():
        bar_dict = {
            'time': row['time'],
            'open': row['open'],
            'high': row['high'], 
            'low': row['low'],
            'close': row['close'],
            'volume': row['volume'],
            'amount': row['amount'],  # 计算成交总额
            'volume_cum': row['volume_cum'],
            'amount_cum': row['amount_cum'],
            'avg_price': row['avg_price']
        }
        conn.send(bar_dict)
        # print("send bar index", index)
        time.sleep(0.1)
    conn.close()


    # 模拟使用本地数据生成实时数据源
def get_local_data(conn):
    # 读取tick数据文件并发送
    volume_tmp = 0
    with open('tick_data.txt', 'r') as f:
        for line in f:
            # 将每行字符串转换为字典并发送
            bar_dict = eval(line.strip())
            bar = {
                'time': bar_dict['time'],
                'open': bar_dict['open'],
                'high': bar_dict['high'],
                'low': bar_dict['low'],
                'close': bar_dict['lastPrice'],
                'volume': bar_dict['volume'] - volume_tmp,
                'amount': bar_dict['amount'],  # 计算成交总额
                'volume_cum': bar_dict['volume_cum'],
                'amount_cum': bar_dict['amount_cum']
            }
            conn.send(bar)
            print("发送数据:", bar)
            volume_tmp = bar_dict['volume']
            time.sleep(0.1)  # 每秒发送一条数据

def process_market_data(df):
    """
    处理市场数据，将输入数据转换为指定格式的输出数据
    
    参数:
    df: pandas DataFrame, 包含原始市场数据
    
    返回:
    pandas DataFrame: 处理后的数据，包含指定的字段和格式
    """
    # 创建结果DataFrame
    result_df = pd.DataFrame(index=df.index)
    
    # 处理价格相关字段，保留三位小数
    result_df['close'] = df['lastPrice'].round(3)
    result_df['low'] = df['lastPrice'].round(3)
    result_df['high'] = df['lastPrice'].round(3)
    result_df['open'] = df['lastPrice'].round(3)
    
    # 处理时间字段
    result_df['datetime'] = pd.to_datetime(df.index)
    
    # 获取日期分组
    df['date'] = df.index.astype(str).str[:8]
    
    # 按日期分组处理volume和amount
    def process_group(group):
        # 计算volume差值
        group['volume_diff'] = group['volume'].diff()
        # 第一行使用原始volume值
        group.loc[group.index[0], 'volume_diff'] = group.loc[group.index[0], 'volume']
        
        # 计算amount差值
        group['amount_diff'] = group['amount'].diff()
        # 第一行使用原始amount值
        group.loc[group.index[0], 'amount_diff'] = group.loc[group.index[0], 'amount']
        
        return group
    
    # 应用分组处理并重置索引
    processed_df = df.groupby('date').apply(process_group).reset_index(level=0, drop=True)
    
    # 将处理后的数据添加到结果DataFrame
    result_df['volume'] = processed_df['volume_diff']
    result_df['amount'] = processed_df['amount_diff']
    
    # 添加累计值
    result_df['volume_cum'] = df['volume'].round(3)
    result_df['amount_cum'] = df['amount'].round(3)
    result_df['avg_price'] = (df['amount'] / df['volume']) / 10
    result_df['time'] = df['time']
    
    # 删除临时列
    result_df = result_df.drop('date', axis=1, errors='ignore')
    
    return result_df

if __name__ == "__main__":
    df = load_miniqmt_data_from_hd5('127094.SZ', '20250606145900', '20250606150000')
    print(df.head(5))
    df = process_market_data(df)
    print(df)
    print(df.columns)
    df.set_index('datetime', inplace=True)
    print(df.columns)