#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
import datetime
import logging
from typing import Optional, Dict, List
import threading
import multiprocessing
import json

class BatchBacktestMetadata:
    """批量回测元数据管理器"""
    
    def __init__(self, db_path: str = "batch_backtest_metadata.db"):
        """初始化元数据管理器
        
        Args:
            db_path: SQLite数据库文件路径
        """
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self._lock = threading.Lock()
        self.conn = sqlite3.connect(self.db_path)
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            cursor = self.conn.cursor()
            
            # 创建批量回测会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS batch_backtest_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT UNIQUE NOT NULL,
                    start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    end_time DATETIME,
                    status TEXT DEFAULT 'running',
                    results_dir TEXT,
                    start_date TEXT,
                    end_date TEXT,
                    total_dates INTEGER DEFAULT 0,
                    completed_dates INTEGER DEFAULT 0,
                    failed_dates TEXT,
                    process_mode TEXT DEFAULT 'serial',
                    max_processes INTEGER DEFAULT 1
                )
            ''')
            
            # 创建批量回测任务表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS batch_backtest_tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    date_str TEXT NOT NULL,
                    status TEXT DEFAULT 'pending',
                    start_time DATETIME,
                    end_time DATETIME,
                    results_path TEXT,
                    error_message TEXT,
                    FOREIGN KEY (session_id) REFERENCES batch_backtest_sessions (session_id)
                )
            ''')

            # 创建进程任务表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS process_tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    task_id TEXT UNIQUE NOT NULL,
                    target_date TEXT NOT NULL,
                    process_id INTEGER NOT NULL,
                    status TEXT DEFAULT 'pending',
                    results_path TEXT,
                    error_message TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (session_id) REFERENCES batch_backtest_sessions (session_id)
                )
            ''')

            # 创建进程状态表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS process_status (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    process_id INTEGER NOT NULL,
                    pid INTEGER,
                    status TEXT DEFAULT 'idle',
                    current_task TEXT,
                    cpu_usage REAL,
                    memory_usage REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (session_id) REFERENCES batch_backtest_sessions (session_id)
                )
            ''')

            # === 新增：进程配置表 ===
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS process_configs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    task_id TEXT NOT NULL,
                    process_id INTEGER NOT NULL,
                    target_date TEXT NOT NULL,
                    config_file_path TEXT NOT NULL,
                    original_config TEXT,
                    updated_config TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (session_id) REFERENCES batch_backtest_sessions (session_id),
                    FOREIGN KEY (task_id) REFERENCES process_tasks (task_id)
                )
            ''')
            
            self.conn.commit()
            self.logger.debug("数据库表结构初始化完成")
            
            # 执行数据库迁移
            self._migrate_database_schema(cursor)
            
        except Exception as e:
            self.logger.error(f"初始化数据库失败: {str(e)}")
            raise
    
    def _migrate_database_schema(self, cursor):
        """数据库架构迁移，添加缺失的列"""
        try:
            # 1. 检查 batch_backtest_sessions 表的列
            cursor.execute("PRAGMA table_info(batch_backtest_sessions)")
            existing_columns = [column[1] for column in cursor.fetchall()]
            
            # 需要添加的列及其定义
            sessions_new_columns = {
                'process_mode': 'TEXT DEFAULT "serial"',
                'max_processes': 'INTEGER DEFAULT 1',
                'start_date': 'TEXT',
                'end_date': 'TEXT'
            }
            
            # 添加缺失的列
            for column_name, column_def in sessions_new_columns.items():
                if column_name not in existing_columns:
                    try:
                        cursor.execute(f'ALTER TABLE batch_backtest_sessions ADD COLUMN {column_name} {column_def}')
                        self.logger.info(f"已添加缺失的列到 batch_backtest_sessions: {column_name}")
                    except Exception as e:
                        self.logger.error(f"添加列 {column_name} 到 batch_backtest_sessions 失败: {str(e)}")
            
            # 2. 检查 process_tasks 表的列
            cursor.execute("PRAGMA table_info(process_tasks)")
            tasks_existing_columns = [column[1] for column in cursor.fetchall()]
            
            # process_tasks 表不需要 start_time 和 end_time，移除这些引用
            
            # 3. 检查 process_status 表的列
            cursor.execute("PRAGMA table_info(process_status)")
            status_existing_columns = [column[1] for column in cursor.fetchall()]
            
            # process_status 表也不需要 start_time 和 end_time，移除这些引用
            
        except Exception as e:
            self.logger.error(f"数据库迁移失败: {str(e)}")
            # 如果迁移失败，不影响整体流程，只记录错误
    
    def start_batch_session(self, session_id: str, results_dir: str, 
                           start_date: str = None, end_date: str = None,
                           total_dates: int = 0, process_mode: str = 'serial',
                           max_processes: int = 1) -> bool:
        """开始一个新的批量回测会话
        
        Args:
            session_id: 会话ID（通常使用timestamp）
            results_dir: 结果保存目录
            start_date: 开始日期
            end_date: 结束日期
            total_dates: 总日期数
            process_mode: 处理模式 ('serial', 'parallel')
            max_processes: 最大进程数
            
        Returns:
            bool: 是否成功
        """
        try:
            with self._lock:
                cursor = self.conn.cursor()
                
                now = datetime.datetime.now().isoformat()
                
                cursor.execute('''
                    INSERT INTO batch_backtest_sessions 
                    (session_id, start_time, results_dir, status, total_dates, 
                     process_mode, max_processes, created_at, updated_at)
                    VALUES (?, ?, ?, 'running', ?, ?, ?, ?, ?)
                ''', (session_id, start_date or now, results_dir, total_dates, 
                      process_mode, max_processes, now, now))
                
                self.conn.commit()
                self.logger.info(f"批量回测会话已开始: {session_id} (模式: {process_mode}, 最大进程数: {max_processes})")
                return True
                
        except Exception as e:
            self.logger.error(f"开始批量回测会话失败: {str(e)}")
            return False
    
    def create_process_task(self, session_id: str, task_id: str, target_date: str, 
                           process_id: int = None, results_path: str = None) -> bool:
        """创建进程任务记录
        
        Args:
            session_id: 会话ID
            task_id: 任务ID
            target_date: 目标日期
            process_id: 进程ID（可选）
            results_path: 结果路径（可选）
            
        Returns:
            bool: 是否成功
        """
        try:
            with self._lock:
                cursor = self.conn.cursor()
                
                now = datetime.datetime.now().isoformat()
                
                cursor.execute('''
                    INSERT INTO process_tasks 
                    (session_id, task_id, target_date, process_id, status, results_path, created_at, updated_at)
                    VALUES (?, ?, ?, ?, 'pending', ?, ?, ?)
                ''', (session_id, task_id, target_date, process_id, results_path, now, now))
                
                self.conn.commit()
                self.logger.debug(f"创建进程任务: {task_id} (日期: {target_date}, 进程: {process_id})")
                return True
                
        except Exception as e:
            self.logger.error(f"创建进程任务失败: {str(e)}")
            return False
    
    def update_task_status(self, task_id: str, status: str, process_id: int = None, 
                          results_path: str = None, error_message: str = None) -> bool:
        """更新任务状态
        
        Args:
            task_id: 任务ID
            status: 任务状态
            process_id: 进程ID
            results_path: 结果文件路径
            error_message: 错误信息
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with self._lock:
                cursor = self.conn.cursor()
                
                now = datetime.datetime.now().isoformat()
                
                # 构建动态更新SQL
                update_fields = ['status = ?', 'updated_at = ?']
                values = [status, now]
                
                if process_id is not None:
                    update_fields.append('process_id = ?')
                    values.append(process_id)
                
                if results_path is not None:
                    update_fields.append('results_path = ?')
                    values.append(results_path)
                
                if error_message is not None:
                    update_fields.append('error_message = ?')
                    values.append(error_message)
                
                values.append(task_id)  # 用于WHERE条件
                
                sql = f'''
                    UPDATE process_tasks 
                    SET {', '.join(update_fields)}
                    WHERE task_id = ?
                '''
                
                cursor.execute(sql, values)
                
                self.conn.commit()
                self.logger.debug(f"更新任务状态: {task_id} -> {status}")
                return True
                
        except Exception as e:
            self.logger.error(f"更新任务状态失败: {str(e)}")
            return False
    
    def update_task_result(self, task_id: str, result: dict) -> bool:
        """更新任务执行结果
        
        Args:
            task_id: 任务ID
            result: 执行结果字典，包含success、error、trades_count、stocks_count等字段
            
        Returns:
            bool: 是否更新成功
        """
        try:
            # 调用update_task_status方法更新基本状态
            status = 'completed' if result.get('success', False) else 'failed'
            error_message = result.get('error') if not result.get('success', False) else None
            
            return self.update_task_status(
                task_id=task_id,
                status=status,
                error_message=error_message
            )
                
        except Exception as e:
            self.logger.error(f"更新任务结果失败: {str(e)}")
            return False
    
    def register_process(self, session_id: str, process_id: int, pid: int = None) -> bool:
        """注册进程信息
        
        Args:
            session_id: 会话ID
            process_id: 进程ID
            pid: 系统进程ID
            
        Returns:
            bool: 是否注册成功
        """
        try:
            with self._lock:
                cursor = self.conn.cursor()
                
                now = datetime.datetime.now().isoformat()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO process_status
                    (session_id, process_id, pid, status, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (session_id, process_id, pid, 'idle', now, now))
                
                self.conn.commit()
                self.logger.debug(f"注册进程: {process_id} (PID: {pid})")
                return True
                
        except Exception as e:
            self.logger.error(f"注册进程失败: {str(e)}")
            return False
    
    def update_process_status(self, session_id: str, process_id: int, status: str, 
                             current_task: str = None, cpu_usage: float = None, 
                             memory_usage: float = None) -> bool:
        """更新进程状态
        
        Args:
            session_id: 会话ID
            process_id: 进程ID
            status: 进程状态
            current_task: 当前任务
            cpu_usage: CPU使用率
            memory_usage: 内存使用率
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with self._lock:
                cursor = self.conn.cursor()
                
                now = datetime.datetime.now().isoformat()
                
                # 构建动态更新SQL
                update_fields = ['status = ?', 'updated_at = ?']
                values = [status, now]
                
                if current_task is not None:
                    update_fields.append('current_task = ?')
                    values.append(current_task)
                
                if cpu_usage is not None:
                    update_fields.append('cpu_usage = ?')
                    values.append(cpu_usage)
                    
                if memory_usage is not None:
                    update_fields.append('memory_usage = ?')
                    values.append(memory_usage)
                
                values.extend([session_id, process_id])  # 用于WHERE条件
                
                sql = f'''
                    UPDATE process_status 
                    SET {', '.join(update_fields)}
                    WHERE session_id = ? AND process_id = ?
                '''
                
                cursor.execute(sql, values)
                
                self.conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"更新进程状态失败: {str(e)}")
            return False
    
    def get_session_tasks(self, session_id: str) -> List[Dict]:
        """获取会话的所有任务
        
        Args:
            session_id: 会话ID
            
        Returns:
            List[Dict]: 任务列表
        """
        try:
            self.conn.row_factory = sqlite3.Row
            cursor = self.conn.cursor()
            
            cursor.execute('''
                SELECT * FROM process_tasks 
                WHERE session_id = ?
                ORDER BY target_date
            ''', (session_id,))
            
            return [dict(row) for row in cursor.fetchall()]
            
        except Exception as e:
            self.logger.error(f"获取会话任务失败: {str(e)}")
            return []
    
    def get_session_processes(self, session_id: str) -> List[Dict]:
        """获取会话的所有进程
        
        Args:
            session_id: 会话ID
            
        Returns:
            List[Dict]: 进程列表
        """
        try:
            self.conn.row_factory = sqlite3.Row
            cursor = self.conn.cursor()
            
            cursor.execute('''
                SELECT * FROM process_status 
                WHERE session_id = ?
                ORDER BY process_id
            ''', (session_id,))
            
            return [dict(row) for row in cursor.fetchall()]
            
        except Exception as e:
            self.logger.error(f"获取会话进程失败: {str(e)}")
            return []
    
    def get_session_statistics(self, session_id: str) -> Dict:
        """获取会话统计信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            Dict: 统计信息
        """
        try:
            cursor = self.conn.cursor()
            
            # 获取任务统计
            cursor.execute('''
                SELECT status, COUNT(*) as count
                FROM process_tasks 
                WHERE session_id = ?
                GROUP BY status
            ''', (session_id,))
            
            task_stats = {row[0]: row[1] for row in cursor.fetchall()}
            
            # 获取进程统计
            cursor.execute('''
                SELECT status, COUNT(*) as count
                FROM process_status 
                WHERE session_id = ?
                GROUP BY status
            ''', (session_id,))
            
            process_stats = {row[0]: row[1] for row in cursor.fetchall()}
            
            # 获取会话信息
            cursor.execute('''
                SELECT * FROM batch_backtest_sessions 
                WHERE session_id = ?
            ''', (session_id,))
            
            session_info = cursor.fetchone()
            
            return {
                'session_info': dict(session_info) if session_info else {},
                'task_stats': task_stats,
                'process_stats': process_stats,
                'total_tasks': sum(task_stats.values()),
                'completed_tasks': task_stats.get('completed', 0),
                'failed_tasks': task_stats.get('failed', 0),
                'running_tasks': task_stats.get('running', 0),
                'pending_tasks': task_stats.get('pending', 0)
            }
            
        except Exception as e:
            self.logger.error(f"获取会话统计信息失败: {str(e)}")
            return {}

    def end_batch_session(self, session_id: str, status: str = 'completed',
                         end_date: str = None, completed_dates: int = 0,
                         failed_dates: List[str] = None) -> bool:
        """结束批量回测会话
        
        Args:
            session_id: 会话ID
            status: 最终状态 ('completed', 'failed', 'cancelled')
            end_date: 结束日期
            completed_dates: 完成的日期数
            failed_dates: 失败的日期列表
            
        Returns:
            bool: 是否成功
        """
        try:
            with self._lock:
                cursor = self.conn.cursor()
                
                now = datetime.datetime.now().isoformat()
                failed_dates_str = ','.join(failed_dates) if failed_dates else ''
                
                cursor.execute('''
                    UPDATE batch_backtest_sessions 
                    SET end_time = ?, status = ?, completed_dates = ?, 
                        failed_dates = ?, updated_at = ?
                    WHERE session_id = ?
                ''', (end_date or now, status, completed_dates, 
                      failed_dates_str, now, session_id))
                
                self.conn.commit()
                self.logger.info(f"批量回测会话已结束: {session_id}, 状态: {status}")
                return True
                
        except Exception as e:
            self.logger.error(f"结束批量回测会话失败: {str(e)}")
            return False
    
    def is_batch_session_running(self) -> bool:
        """检查是否有正在运行的批量回测会话
        
        Returns:
            bool: 是否有正在运行的会话
        """
        try:
            cursor = self.conn.cursor()
            
            cursor.execute('''
                SELECT COUNT(*) FROM batch_backtest_sessions 
                WHERE status = 'running'
            ''')
            
            count = cursor.fetchone()[0]
            return count > 0
            
        except Exception as e:
            self.logger.error(f"检查批量回测会话状态失败: {str(e)}")
            return False
    
    def get_current_batch_session(self) -> Optional[Dict]:
        """获取当前正在运行的批量回测会话信息
        
        Returns:
            Dict: 会话信息，如果没有则返回None
        """
        try:
            self.conn.row_factory = sqlite3.Row  # 使结果可以像字典一样访问
            cursor = self.conn.cursor()
            
            cursor.execute('''
                SELECT * FROM batch_backtest_sessions 
                WHERE status = 'running'
                ORDER BY created_at DESC
                LIMIT 1
            ''')
            
            row = cursor.fetchone()
            if row:
                return dict(row)
            return None
            
        except Exception as e:
            self.logger.error(f"获取当前批量回测会话失败: {str(e)}")
            return None
    
    def is_path_in_batch_session(self, path: str) -> bool:
        """检查给定路径是否属于当前正在运行的批量回测会话
        
        Args:
            path: 要检查的路径
            
        Returns:
            bool: 是否属于批量回测会话
        """
        try:
            current_session = self.get_current_batch_session()
            if not current_session:
                return False
            
            # 标准化路径进行比较
            path = os.path.normpath(path)
            results_dir = os.path.normpath(current_session['results_dir'])
            
            # 检查路径是否在批量回测的结果目录下
            return path.startswith(results_dir)
            
        except Exception as e:
            self.logger.error(f"检查路径是否属于批量回测会话失败: {str(e)}")
            return False
    
    def update_session_progress(self, session_id: str, completed_dates: int) -> bool:
        """更新会话进度
        
        Args:
            session_id: 会话ID
            completed_dates: 已完成的日期数
            
        Returns:
            bool: 是否成功
        """
        try:
            with self._lock:
                cursor = self.conn.cursor()
                
                now = datetime.datetime.now().isoformat()
                
                cursor.execute('''
                    UPDATE batch_backtest_sessions 
                    SET completed_dates = ?, updated_at = ?
                    WHERE session_id = ?
                ''', (completed_dates, now, session_id))
                
                self.conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"更新会话进度失败: {str(e)}")
            return False
    
    def cleanup_old_sessions(self, days: int = 30) -> int:
        """清理旧的会话记录
        
        Args:
            days: 保留多少天内的记录
            
        Returns:
            int: 删除的记录数
        """
        try:
            with self._lock:
                cursor = self.conn.cursor()
                
                cutoff_date = (datetime.datetime.now() - 
                             datetime.timedelta(days=days)).isoformat()
                
                cursor.execute('''
                    DELETE FROM batch_backtest_sessions 
                    WHERE created_at < ? AND status != 'running'
                ''', (cutoff_date,))
                
                deleted_count = cursor.rowcount
                self.conn.commit()
                
                self.logger.info(f"清理了 {deleted_count} 条旧的会话记录")
                return deleted_count
                
        except Exception as e:
            self.logger.error(f"清理旧会话记录失败: {str(e)}")
            return 0
    
    def get_session_history(self, limit: int = 10) -> List[Dict]:
        """获取会话历史记录
        
        Args:
            limit: 返回的记录数限制
            
        Returns:
            List[Dict]: 会话历史记录列表
        """
        try:
            self.conn.row_factory = sqlite3.Row
            cursor = self.conn.cursor()
            
            cursor.execute('''
                SELECT * FROM batch_backtest_sessions 
                ORDER BY created_at DESC
                LIMIT ?
            ''', (limit,))
            
            return [dict(row) for row in cursor.fetchall()]
            
        except Exception as e:
            self.logger.error(f"获取历史会话记录失败: {str(e)}")
            return []

    def close(self):
        """关闭数据库连接"""
        try:
            if hasattr(self, 'conn') and self.conn:
                self.conn.close()
                self.logger.debug("数据库连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭数据库连接时出错: {str(e)}")

    def store_process_config(self, session_id: str, task_id: str, process_id: int, 
                           target_date: str, config_file_path: str, 
                           original_config: str, updated_config: str) -> bool:
        """存储进程配置信息
        
        Args:
            session_id: 会话ID
            task_id: 任务ID
            process_id: 进程ID
            target_date: 目标日期
            config_file_path: 配置文件路径
            original_config: 原始配置内容
            updated_config: 更新后的配置内容
            
        Returns:
            bool: 是否存储成功
        """
        try:
            with self._lock:
                cursor = self.conn.cursor()
                
                now = datetime.datetime.now().isoformat()
                
                cursor.execute('''
                    INSERT INTO process_configs
                    (session_id, task_id, process_id, target_date, config_file_path, 
                     original_config, updated_config, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (session_id, task_id, process_id, target_date, config_file_path,
                      original_config, updated_config, now))
                
                self.conn.commit()
                self.logger.debug(f"进程配置已存储: 会话={session_id}, 任务={task_id}, 进程={process_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"存储进程配置失败: {str(e)}")
            return False

    def get_process_config(self, task_id: str) -> dict:
        """获取进程配置信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            dict: 配置信息字典，包含 config_file_path, target_date, updated_config 等
        """
        try:
            with self._lock:
                cursor = self.conn.cursor()
                
                cursor.execute('''
                    SELECT session_id, process_id, target_date, config_file_path, 
                           original_config, updated_config
                    FROM process_configs
                    WHERE task_id = ?
                    ORDER BY created_at DESC
                    LIMIT 1
                ''', (task_id,))
                
                row = cursor.fetchone()
                if row:
                    return {
                        'session_id': row[0],
                        'process_id': row[1],
                        'target_date': row[2],
                        'config_file_path': row[3],
                        'original_config': row[4],
                        'updated_config': row[5]
                    }
                else:
                    self.logger.warning(f"未找到任务 {task_id} 的配置信息")
                    return {}
                    
        except Exception as e:
            self.logger.error(f"获取进程配置失败: {str(e)}")
            return {}

    def cleanup_process_config(self, task_id: str) -> bool:
        """清理进程配置信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否清理成功
        """
        try:
            with self._lock:
                cursor = self.conn.cursor()
                
                cursor.execute('DELETE FROM process_configs WHERE task_id = ?', (task_id,))
                
                self.conn.commit()
                self.logger.debug(f"进程配置已清理: 任务={task_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"清理进程配置失败: {str(e)}")
            return False

# 创建全局实例
_metadata_manager = None

def get_metadata_manager() -> BatchBacktestMetadata:
    """获取批量回测元数据管理器的全局实例"""
    global _metadata_manager
    if _metadata_manager is None:
        _metadata_manager = BatchBacktestMetadata()
    return _metadata_manager 