# 自动交易系统使用说明

## 系统概述

本系统是一个完全自动化的股票选股和交易系统，集成了选股算法和交易执行功能。系统采用简化的进程管理方式，确保可靠性和易用性。

## 主要功能

1. **自动选股**：基于技术指标自动筛选符合条件的股票
2. **自动交易**：为选中的股票启动独立的交易进程
3. **进程管理**：提供完整的交易进程管理功能
4. **实时监控**：通过命令行工具查看和管理交易状态

## 快速开始

### 1. 启动交易系统

双击运行 `启动自动交易系统.bat`，选择启动模式：

```
请选择启动模式:

[1] 完整模式 - 自动选股 + 启动交易（推荐）
[2] 仅选股模式 - 只执行选股，不启动交易
[3] 指定日期选股 - 为特定日期执行选股
[4] 退出
```

**推荐使用模式1**，系统会：
1. 自动执行选股算法
2. 为选中的股票启动交易进程
3. 交易进程在后台持续运行

### 2. 管理交易进程

使用命令行工具管理交易进程：

```bash
# 查看当前活跃的股票
python trade_cli.py list

# 查看指定股票的状态
python trade_cli.py status <股票代码>

# 停止所有交易进程
python trade_cli.py stop-all

# 手动添加股票交易
python trade_cli.py add <股票代码> --initial-cash 100000

# 手动移除股票交易
python trade_cli.py remove <股票代码>
```

## 系统架构

### 核心组件

1. **选股模块** (`select_stocks.py`)
   - 基于技术指标筛选股票
   - 支持指定日期选股
   - 自动更新配置文件

2. **交易管理器** (`simple_trade_manager.py`)
   - 使用 `subprocess` 启动独立交易进程
   - 通过 `psutil` 实时发现和管理进程
   - 无状态设计，可靠性高

3. **交易执行** (`run_backtest.py`)
   - 每只股票运行独立的交易进程
   - 支持多种交易策略
   - 实时数据处理和订单执行

4. **命令行工具** (`trade_cli.py`)
   - 提供完整的进程管理接口
   - 支持查看、添加、删除交易进程
   - 兼容不同的管理器实现

### 进程管理机制

- **直接启动**：使用 `subprocess.Popen` 直接启动交易进程
- **实时发现**：通过 `psutil` 扫描系统进程，根据命令行参数识别交易进程
- **无依赖状态**：不依赖进程间通信或状态文件
- **容错能力**：即使管理器重启也能发现现有进程

## 配置说明

### config.yaml 主要配置项

```yaml
backtest:
  is_live: true                    # 是否实盘交易
  initial_cash: 100000            # 初始资金
  stock_codes: []                 # 选中的股票代码（自动更新）
  
logging:
  level: INFO                     # 日志级别
  log_dir: logs                   # 日志目录
  
# 其他配置项...
```

## 使用场景

### 场景1：日常自动交易

1. 每天运行启动脚本，选择模式1
2. 系统自动选股并启动交易
3. 使用 `python trade_cli.py list` 查看交易状态
4. 交易进程在后台持续运行

### 场景2：仅执行选股

1. 运行启动脚本，选择模式2
2. 系统只执行选股，不启动交易
3. 可以查看选股结果后手动决定是否交易

### 场景3：历史数据选股

1. 运行启动脚本，选择模式3
2. 输入指定日期（如：20241201）
3. 系统为该日期执行选股分析

### 场景4：手动管理

```bash
# 先执行选股
python auto_trading.py --select-only

# 手动启动交易进程
python simple_trade_manager.py --init

# 查看和管理
python trade_cli.py list
python trade_cli.py status 123456.SZ
```

## 注意事项

1. **实盘交易**：确保 `config.yaml` 中 `is_live: true`
2. **资金管理**：合理设置 `initial_cash` 参数
3. **进程管理**：交易进程会在后台持续运行，关闭启动窗口不会停止交易
4. **日志查看**：所有操作都有详细日志记录在 `logs/` 目录
5. **企业微信通知**：系统会发送重要操作的企业微信通知

## 故障排除

### 常见问题

1. **找不到Python解释器**
   - 检查启动脚本中的 `PYTHON_PATH` 设置
   - 确保Anaconda正确安装

2. **选股失败**
   - 检查网络连接
   - 查看选股日志文件

3. **交易进程启动失败**
   - 检查配置文件格式
   - 查看交易日志文件

4. **查询不到交易进程**
   - 使用 `python trade_cli.py list` 查看
   - 检查进程是否真的在运行

### 日志文件位置

- 选股日志：`logs/select_stocks_*.log`
- 交易管理器日志：`logs/simple_trade_manager_*.log`
- 交易进程日志：`logs/backtest_*.log`
- CLI工具日志：`logs/trade_cli_*.log`

## 技术优势

1. **可靠性**：简化的进程管理，减少故障点
2. **易用性**：一键启动，自动化程度高
3. **可维护性**：代码结构清晰，日志完整
4. **扩展性**：模块化设计，易于添加新功能

## 更新历史

- **v2.0**：简化系统架构，移除复杂的持久化模式
- **v1.0**：初始版本，包含完整功能

---

如有问题，请查看日志文件或联系技术支持。 