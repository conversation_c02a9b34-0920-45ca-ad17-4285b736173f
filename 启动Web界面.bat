@echo off
echo ========================================
echo     量化交易系统 Web界面启动器
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

REM 检查并安装依赖
echo [1/3] 检查依赖...
pip show streamlit >nul 2>&1
if errorlevel 1 (
    echo [1/3] 正在安装依赖包...
    pip install -r quant_app/requirements.txt
    if errorlevel 1 (
        echo [错误] 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
) else (
    echo [1/3] 依赖已安装
)

REM 创建必要的目录
echo [2/3] 初始化系统...
if not exist "quant_app\data" mkdir "quant_app\data"
if not exist "quant_app\data\logs" mkdir "quant_app\data\logs"
if not exist "quant_app\data\market_data" mkdir "quant_app\data\market_data"
if not exist "quant_app\data\exports" mkdir "quant_app\data\exports"

REM 启动Streamlit应用
echo [3/3] 正在启动Web界面...
echo.
echo 系统将在浏览器中自动打开，如未打开请手动访问:
echo http://localhost:8501
echo.
echo 按 Ctrl+C 可以停止服务
echo ========================================
echo.

REM 设置Streamlit配置并启动
set STREAMLIT_SERVER_PORT=8501
set STREAMLIT_SERVER_HEADLESS=true
set STREAMLIT_BROWSER_GATHER_USAGE_STATS=false

streamlit run quant_app/app/main.py --server.maxUploadSize=200

pause 